from textwrap import dedent

def build_final_html(page_htmls: list[str], title: str = "Extracted Content") -> str:
    """
    Merges a list of page-level HTML strings into one complete HTML document with KaTeX rendering.
    """
    merged = "<hr class='page-separator'>".join(page_htmls)

    html = f"""<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>{title}</title>
  <link href="https://fonts.googleapis.com/css2?family=Actor&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css">
  <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js"></script>
  <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/contrib/auto-render.min.js"></script>
  <script>
    document.addEventListener("DOMContentLoaded", function () {{
      renderMathInElement(document.body, {{
        delimiters: [
          {{left: '\\\\(', right: '\\\\)', display: false}},
          {{left: '\\\\[', right: '\\\\]', display: true}}
        ],
        throwOnError: false,
        ignoredTags: [],
        ignoredClasses: []
      }});
    }});
  </script>
  <style>
    body {{
      font-family: 'Actor', sans-serif;
      margin: 2rem auto;
      padding: 0 1.5rem;
      max-width: 820px;
      line-height: 1.5;
      background: #fffbfa;
      color: #111;
    }}
    h1, h2, h3, h4, h5 {{
      color: #004d40;
      margin-top: 1rem;
      margin-bottom: 0.5rem;
    }}
    h1 {{ font-size: 1.8rem; }}
    h2 {{ font-size: 1.5rem; }}
    h3 {{ font-size: 1.3rem; }}
    h4, h5 {{ font-size: 1.15rem; }}
    .page-title {{
      text-align: center;
      font-weight: 600;
      font-size: 1.3rem;
      color: #00332f;
      margin: 1.5rem 0;
    }}
    p {{
      margin-bottom: 0.7rem;
      font-size: 1rem;
    }}
    ol, ul {{
      padding-left: 1.5rem;
      margin-bottom: 1rem;
    }}
    li {{
      margin-bottom: 0.4rem;
    }}
    .marks {{
      float: right;
      font-weight: bold;
      font-size: 0.95rem;
      color: #444;
    }}
    .page-separator {{
      border: none;
      border-top: 2px dashed #ccc;
      margin: 2.5rem 0;
    }}
    .table-wrapper {{
      overflow-x: auto;
      margin-bottom: 1.5rem;
    }}
    table {{
      width: 100%;
      border-collapse: collapse;
    }}
    th, td {{
      border: 1px solid #ccc;
      padding: 0.4rem 0.6rem;
      text-align: left;
    }}
    .question-label {{
      font-weight: bold;
      display: inline-block;
      min-width: 2rem;
    }}
    .content-wrapper {{
      background: #fff;
      padding: 2rem;
      border-radius: 10px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }}
    .centered {{
      text-align: center;
    }}
    .spaced-section {{
      margin-top: 1.5rem;
      margin-bottom: 1.5rem;
    }}
    footer {{
      text-align: center;
      margin-top: 3rem;
      font-size: 0.9rem;
      color: #999;
    }}
  </style>
</head>
<body>
  <main class="content-wrapper">
    {merged}
  </main>
  <footer>
    Powered by <strong>gptsir.ai</strong>
  </footer>
</body>
</html>"""
    return dedent(html)
