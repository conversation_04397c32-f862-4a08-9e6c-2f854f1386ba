"""
Decorators for timing functions in the agent workflow.
"""
import functools
import logging
import uuid
from typing import Callable, Any, Optional

from utils.timing_tracker import timing_tracker

logger = logging.getLogger(__name__)

def time_workflow(workflow_name: str) -> Callable:
    """
    Decorator to time an entire workflow.
    
    Args:
        workflow_name: Name of the workflow
        
    Returns:
        Callable: Decorated function
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            # Generate a workflow ID
            workflow_id = str(uuid.uuid4())
            
            # Start timing the workflow
            timing_tracker.start_workflow(workflow_id, workflow_name)
            
            try:
                # Execute the function
                result = await func(*args, **kwargs, workflow_id=workflow_id)
                return result
            finally:
                # End timing the workflow
                timing_tracker.end_workflow(workflow_id)
        
        return wrapper
    
    return decorator

def time_stage(stage_name: str, workflow_id_arg: str = 'workflow_id') -> Callable:
    """
    Decorator to time a stage within a workflow.
    
    Args:
        stage_name: Name of the stage
        workflow_id_arg: Name of the argument containing the workflow ID
        
    Returns:
        Callable: Decorated function
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            # Get the workflow ID from kwargs
            workflow_id = kwargs.get(workflow_id_arg)
            
            if not workflow_id:
                # If workflow_id is not in kwargs, try to find it in args
                # This is a bit hacky, but it works for methods where self is the first argument
                if len(args) > 0 and hasattr(args[0], workflow_id_arg):
                    workflow_id = getattr(args[0], workflow_id_arg)
            
            if not workflow_id:
                # If we still don't have a workflow ID, execute the function without timing
                logger.warning(f"No workflow ID found for stage '{stage_name}', skipping timing")
                return await func(*args, **kwargs)
            
            # Start timing the stage
            timing_tracker.start_stage(workflow_id, stage_name)
            
            try:
                # Execute the function
                result = await func(*args, **kwargs)
                return result
            finally:
                # End timing the stage
                timing_tracker.end_stage(workflow_id, stage_name)
        
        return wrapper
    
    return decorator

def time_function(func_name: Optional[str] = None) -> Callable:
    """
    Decorator to time a function as a standalone workflow.
    
    Args:
        func_name: Optional name for the function (defaults to function name)
        
    Returns:
        Callable: Decorated function
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            # Generate a workflow ID
            workflow_id = str(uuid.uuid4())
            
            # Use provided name or function name
            name = func_name or func.__name__
            
            # Start timing the workflow
            timing_tracker.start_workflow(workflow_id, name)
            
            try:
                # Execute the function
                result = await func(*args, **kwargs)
                return result
            finally:
                # End timing the workflow
                timing_tracker.end_workflow(workflow_id)
        
        return wrapper
    
    # Handle case where decorator is used without parentheses
    if callable(func_name):
        func, func_name = func_name, None
        return decorator(func)
    
    return decorator
