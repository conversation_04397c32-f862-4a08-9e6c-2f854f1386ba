# utils/log_disabler.py

import logging
import sys
from config import SHOW_LOGS

def disable_all_logs():
    """
    If SHOW_LOGS is True in config.py, this function will do nothing.

    Completely disable all logging in the application.
    This function can be called from anywhere to silence all logs.
    """
    # If SHOW_LOGS is True, don't disable logs
    if SHOW_LOGS:
        return False

    # Disable all existing loggers
    for logger_name in logging.root.manager.loggerDict:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.CRITICAL)
        logger.propagate = False

        # Remove all handlers
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

    # Configure the root logger to discard all messages
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.CRITICAL)

    # Remove all handlers from the root logger
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Add a NullHandler to the root logger
    root_logger.addHandler(logging.NullHandler())

    # Disable specific third-party loggers that might be problematic
    for logger_name in [
        'urllib3', 'requests', 'sqlalchemy', 'boto3', 'botocore',
        'fastapi', 'uvicorn', 'uvicorn.access', 'uvicorn.error',
        'asyncio', 'aiohttp', 'pydantic'
    ]:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.CRITICAL)
        logger.propagate = False

        # Remove all handlers
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

    # Redirect stdout and stderr to devnull if needed
    # Uncomment these lines if you want to completely silence all output
    # sys.stdout = open(os.devnull, 'w')
    # sys.stderr = open(os.devnull, 'w')

    # Return True to indicate success
    return True
