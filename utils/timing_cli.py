#!/usr/bin/env python
"""
Command-line tool for analyzing timing data.
"""
import argparse
import logging
import os
import sys
from typing import List, Optional

from utils.timing_analyzer import TimingAnalyzer

logger = logging.getLogger(__name__)

def setup_logging(verbose: bool = False) -> None:
    """
    Set up logging configuration.
    
    Args:
        verbose: Whether to enable verbose logging
    """
    log_level = logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def parse_args() -> argparse.Namespace:
    """
    Parse command-line arguments.
    
    Returns:
        argparse.Namespace: Parsed arguments
    """
    parser = argparse.ArgumentParser(description='Analyze agent workflow timing data')
    
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose logging')
    parser.add_argument('--output-dir', '-o', type=str, default='storage/timing_reports', help='Output directory for reports and plots')
    parser.add_argument('--input-dir', '-i', type=str, default='storage/timing_logs', help='Input directory containing timing log files')
    
    subparsers = parser.add_subparsers(dest='command', help='Command to run')
    
    # List command
    list_parser = subparsers.add_parser('list', help='List available timing data')
    list_parser.add_argument('--format', '-f', choices=['table', 'json', 'csv'], default='table', help='Output format')
    
    # Summary command
    summary_parser = subparsers.add_parser('summary', help='Generate a summary of timing data')
    summary_parser.add_argument('--workflow-id', '-w', type=str, help='Filter by workflow ID')
    summary_parser.add_argument('--format', '-f', choices=['text', 'json', 'markdown'], default='text', help='Output format')
    
    # Report command
    report_parser = subparsers.add_parser('report', help='Generate a comprehensive report')
    report_parser.add_argument('--workflow-id', '-w', type=str, help='Filter by workflow ID')
    report_parser.add_argument('--output', '-o', type=str, help='Output file path')
    
    # Plot command
    plot_parser = subparsers.add_parser('plot', help='Generate plots')
    plot_parser.add_argument('--type', '-t', choices=['workflows', 'stages', 'timeline'], required=True, help='Type of plot to generate')
    plot_parser.add_argument('--workflow-id', '-w', type=str, help='Workflow ID for timeline plot')
    plot_parser.add_argument('--output', '-o', type=str, help='Output file path')
    
    return parser.parse_args()

def ensure_output_dir(output_dir: str) -> None:
    """
    Ensure the output directory exists.
    
    Args:
        output_dir: Output directory path
    """
    os.makedirs(output_dir, exist_ok=True)

def list_timing_data(analyzer: TimingAnalyzer, format: str = 'table') -> None:
    """
    List available timing data.
    
    Args:
        analyzer: TimingAnalyzer instance
        format: Output format (table, json, csv)
    """
    timing_data = analyzer.load_timing_data()
    
    if not timing_data:
        print("No timing data found")
        return
    
    if format == 'json':
        import json
        print(json.dumps([{
            'id': w.get('id'),
            'name': w.get('name'),
            'start_time': w.get('formatted_start_time'),
            'end_time': w.get('formatted_end_time'),
            'duration': w.get('formatted_total_duration'),
            'stage_count': len(w.get('stages', []))
        } for w in timing_data], indent=2))
    elif format == 'csv':
        print("id,name,start_time,end_time,duration,stage_count")
        for w in timing_data:
            print(f"{w.get('id', '')},{w.get('name', '')},{w.get('formatted_start_time', '')},{w.get('formatted_end_time', '')},{w.get('formatted_total_duration', '')},{len(w.get('stages', []))}")
    else:  # table
        from tabulate import tabulate
        table_data = []
        for w in timing_data:
            table_data.append([
                w.get('id', ''),
                w.get('name', ''),
                w.get('formatted_start_time', ''),
                w.get('formatted_end_time', ''),
                w.get('formatted_total_duration', ''),
                len(w.get('stages', []))
            ])
        
        headers = ['ID', 'Name', 'Start Time', 'End Time', 'Duration', 'Stages']
        print(tabulate(table_data, headers=headers, tablefmt='grid'))

def generate_summary(analyzer: TimingAnalyzer, workflow_id: Optional[str] = None, format: str = 'text') -> None:
    """
    Generate a summary of timing data.
    
    Args:
        analyzer: TimingAnalyzer instance
        workflow_id: Optional workflow ID to filter by
        format: Output format (text, json, markdown)
    """
    timing_data = analyzer.load_timing_data(workflow_id)
    
    if not timing_data:
        print("No timing data found")
        return
    
    summary = analyzer.generate_summary(timing_data)
    
    if format == 'json':
        import json
        print(json.dumps(summary, indent=2))
    elif format == 'markdown':
        print("# Timing Summary\n")
        print(f"- Total workflows: {summary['workflow_count']}")
        print(f"- Total duration: {analyzer._format_duration(summary['total_duration'])}")
        print(f"- Average duration: {analyzer._format_duration(summary['avg_duration'])}")
        print(f"- Minimum duration: {analyzer._format_duration(summary['min_duration'])}")
        print(f"- Maximum duration: {analyzer._format_duration(summary['max_duration'])}\n")
        
        print("## Stage Analysis\n")
        print("| Stage | Count | Total Duration | Avg Duration | Min Duration | Max Duration |")
        print("|-------|-------|---------------|--------------|--------------|---------------|")
        
        for stage_name, stage_data in summary['stages'].items():
            print(f"| {stage_name} | {stage_data['count']} | "
                  f"{analyzer._format_duration(stage_data['total_duration'])} | "
                  f"{analyzer._format_duration(stage_data['avg_duration'])} | "
                  f"{analyzer._format_duration(stage_data['min_duration'])} | "
                  f"{analyzer._format_duration(stage_data['max_duration'])} |")
    else:  # text
        print("Timing Summary:")
        print(f"- Total workflows: {summary['workflow_count']}")
        print(f"- Total duration: {analyzer._format_duration(summary['total_duration'])}")
        print(f"- Average duration: {analyzer._format_duration(summary['avg_duration'])}")
        print(f"- Minimum duration: {analyzer._format_duration(summary['min_duration'])}")
        print(f"- Maximum duration: {analyzer._format_duration(summary['max_duration'])}")
        print()
        
        print("Stage Analysis:")
        for stage_name, stage_data in summary['stages'].items():
            print(f"- {stage_name}:")
            print(f"  - Count: {stage_data['count']}")
            print(f"  - Total duration: {analyzer._format_duration(stage_data['total_duration'])}")
            print(f"  - Average duration: {analyzer._format_duration(stage_data['avg_duration'])}")
            print(f"  - Minimum duration: {analyzer._format_duration(stage_data['min_duration'])}")
            print(f"  - Maximum duration: {analyzer._format_duration(stage_data['max_duration'])}")
            print()

def generate_report(analyzer: TimingAnalyzer, output_file: Optional[str] = None, workflow_id: Optional[str] = None) -> None:
    """
    Generate a comprehensive report of timing data.
    
    Args:
        analyzer: TimingAnalyzer instance
        output_file: Optional file path to save the report
        workflow_id: Optional workflow ID to filter by
    """
    timing_data = analyzer.load_timing_data(workflow_id)
    
    if not timing_data:
        print("No timing data found")
        return
    
    report = analyzer.generate_report(timing_data, output_file)
    
    if output_file:
        print(f"Report saved to {output_file}")
    else:
        print(report)

def generate_plot(analyzer: TimingAnalyzer, plot_type: str, output_file: Optional[str] = None, workflow_id: Optional[str] = None) -> None:
    """
    Generate plots of timing data.
    
    Args:
        analyzer: TimingAnalyzer instance
        plot_type: Type of plot to generate (workflows, stages, timeline)
        output_file: Optional file path to save the plot
        workflow_id: Optional workflow ID for timeline plot
    """
    if plot_type == 'timeline' and not workflow_id:
        print("Error: Workflow ID is required for timeline plot")
        return
    
    timing_data = analyzer.load_timing_data(workflow_id if plot_type == 'timeline' else None)
    
    if not timing_data:
        print("No timing data found")
        return
    
    if plot_type == 'workflows':
        analyzer.plot_workflow_durations(timing_data, output_file)
    elif plot_type == 'stages':
        analyzer.plot_stage_durations(timing_data, output_file)
    elif plot_type == 'timeline':
        analyzer.plot_workflow_timeline(timing_data[0], output_file)
    
    if output_file:
        print(f"Plot saved to {output_file}")
    else:
        print("Plot displayed")

def main() -> int:
    """
    Main entry point.
    
    Returns:
        int: Exit code
    """
    args = parse_args()
    setup_logging(args.verbose)
    
    # Create output directory if it doesn't exist
    ensure_output_dir(args.output_dir)
    
    # Create analyzer
    analyzer = TimingAnalyzer(args.input_dir)
    
    # Run the appropriate command
    if args.command == 'list':
        list_timing_data(analyzer, args.format)
    elif args.command == 'summary':
        generate_summary(analyzer, args.workflow_id, args.format)
    elif args.command == 'report':
        output_file = args.output or os.path.join(args.output_dir, 'timing_report.md')
        generate_report(analyzer, output_file, args.workflow_id)
    elif args.command == 'plot':
        output_file = args.output
        if not output_file:
            output_file = os.path.join(args.output_dir, f"{args.type}_plot.png")
        generate_plot(analyzer, args.type, output_file, args.workflow_id)
    else:
        print("Error: No command specified")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
