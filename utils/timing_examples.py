"""
Examples of how to use the timing tracker and decorators.
"""
import asyncio
import logging
from typing import Dict, Any

from utils.timing_tracker import timing_tracker
from utils.timing_decorators import time_workflow, time_stage, time_function

logger = logging.getLogger(__name__)

# Example 1: Using the timing tracker directly
async def example_direct_usage():
    # Start a workflow
    workflow_id = timing_tracker.start_workflow("example_workflow", "Example Workflow")
    
    # Start a stage
    timing_tracker.start_stage(workflow_id, "Stage 1")
    
    # Simulate some work
    await asyncio.sleep(1)
    
    # End the stage
    timing_tracker.end_stage(workflow_id, "Stage 1")
    
    # Start another stage
    timing_tracker.start_stage(workflow_id, "Stage 2")
    
    # Simulate some more work
    await asyncio.sleep(2)
    
    # End the stage
    timing_tracker.end_stage(workflow_id, "Stage 2")
    
    # End the workflow
    workflow_data = timing_tracker.end_workflow(workflow_id)
    
    return workflow_data

# Example 2: Using the decorators
@time_workflow("Decorated Workflow")
async def example_decorated_workflow(workflow_id: str) -> Dict[str, Any]:
    # First stage
    result1 = await example_decorated_stage1(workflow_id=workflow_id)
    
    # Second stage
    result2 = await example_decorated_stage2(workflow_id=workflow_id)
    
    return {"stage1": result1, "stage2": result2}

@time_stage("Stage 1")
async def example_decorated_stage1(workflow_id: str) -> str:
    # Simulate some work
    await asyncio.sleep(1)
    return "Stage 1 completed"

@time_stage("Stage 2")
async def example_decorated_stage2(workflow_id: str) -> str:
    # Simulate some work
    await asyncio.sleep(2)
    return "Stage 2 completed"

# Example 3: Using the time_function decorator for standalone functions
@time_function
async def example_timed_function() -> str:
    # Simulate some work
    await asyncio.sleep(1.5)
    return "Function completed"

# Example 4: Using the time_function decorator with a custom name
@time_function("Custom Function Name")
async def example_timed_function_with_name() -> str:
    # Simulate some work
    await asyncio.sleep(0.5)
    return "Function with custom name completed"

# Run all examples
async def run_examples():
    logger.info("Running Example 1: Direct usage")
    workflow_data = await example_direct_usage()
    logger.info(f"Example 1 completed with total duration: {workflow_data['formatted_total_duration']}")
    
    logger.info("Running Example 2: Decorated workflow")
    result = await example_decorated_workflow()
    logger.info(f"Example 2 completed with result: {result}")
    
    logger.info("Running Example 3: Timed function")
    result = await example_timed_function()
    logger.info(f"Example 3 completed with result: {result}")
    
    logger.info("Running Example 4: Timed function with custom name")
    result = await example_timed_function_with_name()
    logger.info(f"Example 4 completed with result: {result}")

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run the examples
    asyncio.run(run_examples())
