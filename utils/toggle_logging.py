#!/usr/bin/env python
# utils/toggle_logging.py

"""
Utility script to toggle logging on/off in the application.
This script modifies the SHOW_LOGS setting in config.py.

Usage:
    python utils/toggle_logging.py --enable    # Enable logs (set SHOW_LOGS to True)
    python utils/toggle_logging.py --disable   # Disable logs (set SHOW_LOGS to False)
    python utils/toggle_logging.py --status    # Check current logging status
"""

import os
import sys
import re
import argparse


def get_config_path():
    """Get the path to the config.py file."""
    # Assuming this script is in the utils directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    root_dir = os.path.dirname(script_dir)
    return os.path.join(root_dir, 'config.py')


def get_current_log_settings(config_path):
    """Get the current LOG_LEVEL and SHOW_LOGS from config.py."""
    with open(config_path, 'r') as f:
        content = f.read()
    
    log_level_match = re.search(r"LOG_LEVEL\s*=\s*['\"]([A-Z]+)['\"]", content)
    show_logs_match = re.search(r"SHOW_LOGS\s*=\s*(True|False)", content)
    
    log_level = log_level_match.group(1) if log_level_match else None
    show_logs = show_logs_match.group(1) == 'True' if show_logs_match else None
    
    return log_level, show_logs


def set_show_logs(config_path, enabled):
    """Set the SHOW_LOGS in config.py."""
    with open(config_path, 'r') as f:
        content = f.read()
    
    # Replace the SHOW_LOGS line
    new_content = re.sub(
        r"(SHOW_LOGS\s*=\s*)(True|False)", 
        r"\1" + str(enabled), 
        content
    )
    
    with open(config_path, 'w') as f:
        f.write(new_content)


def main():
    parser = argparse.ArgumentParser(description='Toggle application logging')
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--enable', action='store_true', help='Enable logs (set SHOW_LOGS to True)')
    group.add_argument('--disable', action='store_true', help='Disable logs (set SHOW_LOGS to False)')
    group.add_argument('--status', action='store_true', help='Check current logging status')
    
    args = parser.parse_args()
    config_path = get_config_path()
    
    if not os.path.exists(config_path):
        print(f"Error: Config file not found at {config_path}")
        return 1
    
    log_level, show_logs = get_current_log_settings(config_path)
    
    if args.status:
        print(f"Current logging level: {log_level}")
        print(f"Logs are currently {'ENABLED' if show_logs else 'DISABLED'}")
    
    elif args.enable:
        if show_logs:
            print("Logging is already enabled")
        else:
            set_show_logs(config_path, True)
            print("Logging has been ENABLED")
            print("Restart the application for changes to take effect")
    
    elif args.disable:
        if not show_logs:
            print("Logging is already disabled")
        else:
            set_show_logs(config_path, False)
            print("Logging has been DISABLED")
            print("Restart the application for changes to take effect")
    
    return 0


if __name__ == '__main__':
    sys.exit(main())
