import os
import glob

def cleanup_temp_files(base_name: str):
    """
    Deletes files related to a specific PDF (based on base_name) from temp folders.
    """
    folders = ["storage/uploads", "storage/page_images", "storage/page_htmls"]
    for folder in folders:
        pattern = os.path.join(folder, f"{base_name}*")
        for file_path in glob.glob(pattern):
            try:
                os.remove(file_path)
                print(f"✅ Deleted: {file_path}")
            except Exception as e:
                print(f"⚠️ Failed to delete {file_path}: {e}")
