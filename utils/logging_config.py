# utils/logging_config.py

import logging
from config import LOG_LEVEL, SHOW_LOGS


def setup_logging():
    log_level = LOG_LEVEL.upper()

    # Configure the root logger
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Check if logs should be shown or disabled
    if not SHOW_LOGS:
        # Disable all loggers
        for logger_name in logging.root.manager.loggerDict:
            logging.getLogger(logger_name).setLevel(logging.CRITICAL)

        # Set the root logger level
        logging.getLogger().setLevel(logging.CRITICAL)

        # Disable common third-party loggers
        for logger_name in ['urllib3', 'requests', 'sqlalchemy', 'boto3', 'botocore', 'fastapi', 'uvicorn']:
            if logger_name in logging.root.manager.loggerDict:
                logging.getLogger(logger_name).setLevel(logging.CRITICAL)
                logging.getLogger(logger_name).propagate = False
