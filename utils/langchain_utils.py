# langchain_utils.py

from langchain.text_splitter import RecursiveCharacterTextSplitter as RCTS
from utils.pkg_res_version_checker import get_version
import re
from utils.pinecone_utils import get_embeddings
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np

# Print the version of langchain package
print("langchain: ", get_version("langchain"))


def break_text_into_chunks(text):
    """
    Function to break a given text into chunks using langchain's RecursiveCharacterTextSplitter.
    The chunk size is set to 1000 and the chunk overlap is set to 200.

    Args:
        text (str): The text to be broken into chunks.

    Returns:
        list: The list of text chunks.
    """
    # Use langchain recursive character text splitter with chunk size of 1000 and chunk overlap of 200
    text_splitter = RCTS(
        chunk_size=5000,
        chunk_overlap=500,
        length_function=len
    )
    chunks = text_splitter.split_text(text)

    return chunks


def break_text_into_chunks_admin(text):
    """
    Function to break a given text into chunks using langchain's RecursiveCharacterTextSplitter.
    The chunk size is set to 1000 and the chunk overlap is set to 200.

    Args:
        text (str): The text to be broken into chunks.

    Returns:
        list: The list of text chunks.
    """
    # Use langchain recursive character text splitter with chunk size of 1000 and chunk overlap of 200
    text_splitter = RCTS(
        chunk_size=20000,
        chunk_overlap=1000,
        length_function=len
    )
    chunks = text_splitter.split_text(text)

    return chunks


def text_split_into_chunks(text):
    single_sentences_list = re.split(r'(?<=[.?!])\s+', text)
    sentences = [{'sentence': x, 'index': i} for i, x in enumerate(single_sentences_list)]
    sentences = combine_sentences(sentences)
    embeddings = get_embeddings(sentences)
    for i, sentence in enumerate(sentences):
        sentence['combined_sentence_embedding'] = embeddings[i]
    distances, sentences = calculate_cosine_distances(sentences)

    breakpoint_percentile_threshold = 95
    breakpoint_distance_threshold = np.percentile(distances, breakpoint_percentile_threshold)
    indices_above_thresh = [i for i, x in enumerate(distances) if x > breakpoint_distance_threshold]

    start_index = 0
    chunks = []

    for index in indices_above_thresh:
        # The end index is the current breakpoint
        end_index = index

        # Slice the sentence_dicts from the current start index to the end index
        group = sentences[start_index:end_index + 1]
        combined_text = ' '.join([d['sentence'] for d in group])
        chunks.append(combined_text)

        # Update the start index for the next group
        start_index = index + 1

    # The last group, if any sentences remain
    if start_index < len(sentences):
        combined_text = ' '.join([d['sentence'] for d in sentences[start_index:]])
        chunks.append(combined_text)

    return chunks


def combine_sentences(sentences, buffer_size=1):
    # Go through each sentence dict
    for i in range(len(sentences)):

        # Create a string that will hold the sentences which are joined
        combined_sentence = ''

        # Add sentences before the current one, based on the buffer size.
        for j in range(i - buffer_size, i):
            # Check if the index j is not negative (to avoid index out of range like on the first one)
            if j >= 0:
                # Add the sentence at index j to the combined_sentence string
                combined_sentence += sentences[j]['sentence'] + ' '

        # Add the current sentence
        combined_sentence += sentences[i]['sentence']

        # Add sentences after the current one, based on the buffer size
        for j in range(i + 1, i + 1 + buffer_size):
            # Check if the index j is within the range of the sentences list
            if j < len(sentences):
                # Add the sentence at index j to the combined_sentence string
                combined_sentence += ' ' + sentences[j]['sentence']

        # Then add the whole thing to your dict
        # Store the combined sentence in the current sentence dict
        sentences[i]['combined_sentence'] = combined_sentence

    return sentences


def calculate_cosine_distances(sentences):
    distances = []
    for i in range(len(sentences) - 1):
        embedding_current = sentences[i]['combined_sentence_embedding']
        embedding_next = sentences[i + 1]['combined_sentence_embedding']

        # Calculate cosine similarity
        similarity = cosine_similarity([embedding_current], [embedding_next])[0][0]

        # Convert to cosine distance
        distance = 1 - similarity

        # Append cosine distance to the list
        distances.append(distance)

        # Store distance in the dictionary
        sentences[i]['distance_to_next'] = distance

    # Optionally handle the last sentence
    # sentences[-1]['distance_to_next'] = None  # or a default value
    return distances, sentences
