from importlib.metadata import version, PackageNotFoundError


def get_version(package_name):
    """
    Function to get the version of a given package.
    It uses the version function from importlib.metadata.
    If the package is not found, it returns None.

    Args:
        package_name (str): The name of the package.

    Returns:
        str or None: The version of the package or None if the package is not found.
    """
    try:
        return version(package_name)
    except PackageNotFoundError:
        return None
