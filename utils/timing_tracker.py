"""
Timing tracking utility for measuring processing time at each stage of the agent workflow.
"""
import os
import time
import json
import logging
import datetime
import matplotlib.pyplot as plt
import numpy as np
from typing import Dict, List, Optional, Any, Tuple

logger = logging.getLogger(__name__)

class TimingTracker:
    """
    Tracks processing time at each stage of the agent workflow.
    Uses a singleton pattern to maintain state across requests.
    """
    _instance = None
    _timings: Dict[str, Dict[str, Any]] = {}
    _active_stages: Dict[str, Dict[str, Any]] = {}
    _output_dir = "storage/timing_logs"
    _reports_dir = "storage/timing_reports"
    _visualizations_dir = "storage/timing_visualizations"

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(TimingTracker, cls).__new__(cls)
            # Create output directories if they don't exist
            os.makedirs(cls._output_dir, exist_ok=True)
            os.makedirs(cls._reports_dir, exist_ok=True)
            os.makedirs(cls._visualizations_dir, exist_ok=True)
        return cls._instance

    @classmethod
    def start_workflow(cls, workflow_id: str, workflow_name: str) -> str:
        """
        Start tracking a new workflow.

        Args:
            workflow_id: Unique identifier for the workflow
            workflow_name: Name of the workflow

        Returns:
            str: Workflow ID
        """
        timestamp = time.time()
        formatted_time = datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')

        cls._timings[workflow_id] = {
            "id": workflow_id,
            "name": workflow_name,
            "start_time": timestamp,
            "formatted_start_time": formatted_time,
            "end_time": None,
            "formatted_end_time": None,
            "total_duration": None,
            "formatted_total_duration": None,
            "stages": []
        }

        logger.info(f"Started timing workflow {workflow_id} ({workflow_name}) at {formatted_time}")
        return workflow_id

    @classmethod
    def start_stage(cls, workflow_id: str, stage_name: str) -> None:
        """
        Start timing a stage within a workflow.

        Args:
            workflow_id: Workflow ID
            stage_name: Name of the stage
        """
        if workflow_id not in cls._timings:
            logger.warning(f"Attempted to start stage for non-existent workflow {workflow_id}")
            return

        timestamp = time.time()
        formatted_time = datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')

        # Store the active stage
        if workflow_id not in cls._active_stages:
            cls._active_stages[workflow_id] = {}

        cls._active_stages[workflow_id][stage_name] = {
            "name": stage_name,
            "start_time": timestamp,
            "formatted_start_time": formatted_time
        }

        logger.info(f"Started timing stage '{stage_name}' for workflow {workflow_id} at {formatted_time}")

    @classmethod
    def end_stage(cls, workflow_id: str, stage_name: str) -> Dict[str, Any]:
        """
        End timing a stage within a workflow.

        Args:
            workflow_id: Workflow ID
            stage_name: Name of the stage

        Returns:
            Dict: Stage timing data
        """
        if workflow_id not in cls._timings:
            logger.warning(f"Attempted to end stage for non-existent workflow {workflow_id}")
            return None

        if workflow_id not in cls._active_stages or stage_name not in cls._active_stages[workflow_id]:
            logger.warning(f"Attempted to end stage '{stage_name}' that was not started for workflow {workflow_id}")
            return None

        # Get the active stage
        active_stage = cls._active_stages[workflow_id][stage_name]

        # Calculate end time and duration
        end_timestamp = time.time()
        formatted_end_time = datetime.datetime.fromtimestamp(end_timestamp).strftime('%Y-%m-%d %H:%M:%S')
        duration = end_timestamp - active_stage["start_time"]

        # Format duration in hours, minutes, seconds
        formatted_duration = cls._format_duration(duration)

        # Create the completed stage data
        stage_data = {
            "name": stage_name,
            "start_time": active_stage["start_time"],
            "formatted_start_time": active_stage["formatted_start_time"],
            "end_time": end_timestamp,
            "formatted_end_time": formatted_end_time,
            "duration": duration,
            "formatted_duration": formatted_duration
        }

        # Add to the workflow's stages
        cls._timings[workflow_id]["stages"].append(stage_data)

        # Remove from active stages
        del cls._active_stages[workflow_id][stage_name]

        logger.info(f"Ended timing stage '{stage_name}' for workflow {workflow_id} at {formatted_end_time}. Duration: {formatted_duration}")

        return stage_data

    @classmethod
    def end_workflow(cls, workflow_id: str) -> Dict[str, Any]:
        """
        End timing a workflow and save the results to files.

        Args:
            workflow_id: Workflow ID

        Returns:
            Dict: Workflow timing data
        """
        if workflow_id not in cls._timings:
            logger.warning(f"Attempted to end non-existent workflow {workflow_id}")
            return None

        # End any active stages
        if workflow_id in cls._active_stages:
            active_stages = list(cls._active_stages[workflow_id].keys())
            for stage_name in active_stages:
                cls.end_stage(workflow_id, stage_name)

        # Calculate end time and duration
        end_timestamp = time.time()
        formatted_end_time = datetime.datetime.fromtimestamp(end_timestamp).strftime('%Y-%m-%d %H:%M:%S')
        duration = end_timestamp - cls._timings[workflow_id]["start_time"]

        # Format duration in hours, minutes, seconds
        formatted_duration = cls._format_duration(duration)

        # Update workflow data
        cls._timings[workflow_id]["end_time"] = end_timestamp
        cls._timings[workflow_id]["formatted_end_time"] = formatted_end_time
        cls._timings[workflow_id]["total_duration"] = duration
        cls._timings[workflow_id]["formatted_total_duration"] = formatted_duration

        # Save to JSON file
        cls._save_to_file(workflow_id)

        # Generate text report
        cls._generate_text_report(workflow_id)

        # Generate visualization
        cls._generate_visualization(workflow_id)

        logger.info(f"Ended timing workflow {workflow_id} at {formatted_end_time}. Total duration: {formatted_duration}")

        return cls._timings[workflow_id]

    @classmethod
    def get_workflow_timing(cls, workflow_id: str) -> Optional[Dict[str, Any]]:
        """
        Get timing data for a workflow.

        Args:
            workflow_id: Workflow ID

        Returns:
            Dict or None: Workflow timing data if found
        """
        return cls._timings.get(workflow_id)

    @classmethod
    def get_stage_timing(cls, workflow_id: str, stage_name: str) -> Optional[Dict[str, Any]]:
        """
        Get timing data for a specific stage in a workflow.

        Args:
            workflow_id: Workflow ID
            stage_name: Stage name

        Returns:
            Dict or None: Stage timing data if found
        """
        if workflow_id not in cls._timings:
            return None

        for stage in cls._timings[workflow_id]["stages"]:
            if stage["name"] == stage_name:
                return stage

        return None

    @classmethod
    def _format_duration(cls, seconds: float) -> str:
        """
        Format duration in hours, minutes, and seconds.

        Args:
            seconds: Duration in seconds

        Returns:
            str: Formatted duration string
        """
        hours, remainder = divmod(seconds, 3600)
        minutes, seconds = divmod(remainder, 60)

        parts = []
        if hours > 0:
            parts.append(f"{int(hours)} hr")
        if minutes > 0 or hours > 0:
            parts.append(f"{int(minutes)} min")
        parts.append(f"{seconds:.2f} sec")

        return " ".join(parts)

    @staticmethod
    def _wrap_text(text: str, max_length: int = 30) -> str:
        """
        Wrap long text to make it more readable in visualizations.

        Args:
            text: Text to wrap
            max_length: Maximum length of each line

        Returns:
            str: Wrapped text
        """
        if len(text) <= max_length:
            return text

        # Split by spaces
        words = text.split()
        lines = []
        current_line = []
        current_length = 0

        for word in words:
            if current_length + len(word) + (1 if current_line else 0) <= max_length:
                current_line.append(word)
                current_length += len(word) + (1 if current_length > 0 else 0)
            else:
                if current_line:
                    lines.append(" ".join(current_line))
                current_line = [word]
                current_length = len(word)

        if current_line:
            lines.append(" ".join(current_line))

        return "\n".join(lines)

    @classmethod
    def _save_to_file(cls, workflow_id: str) -> str:
        """
        Save workflow timing data to a JSON file.

        Args:
            workflow_id: Workflow ID

        Returns:
            str: Path to the saved file
        """
        if workflow_id not in cls._timings:
            logger.warning(f"Attempted to save non-existent workflow {workflow_id}")
            return ""

        workflow_data = cls._timings[workflow_id]

        # Create a filename with timestamp
        timestamp = datetime.datetime.fromtimestamp(workflow_data["start_time"]).strftime('%Y%m%d_%H%M%S')
        workflow_name = workflow_data["name"].replace(" ", "_").lower()
        filename = f"{timestamp}_{workflow_name}_{workflow_id}.json"

        file_path = os.path.join(cls._output_dir, filename)

        try:
            with open(file_path, 'w') as f:
                json.dump(workflow_data, f, indent=2)

            logger.info(f"Saved timing data for workflow {workflow_id} to {file_path}")
            return file_path
        except Exception as e:
            logger.error(f"Error saving timing data for workflow {workflow_id}: {e}")
            return ""

    @classmethod
    def get_all_workflows(cls) -> Dict[str, Dict[str, Any]]:
        """
        Get timing data for all workflows.

        Returns:
            Dict: All workflow timing data
        """
        return cls._timings

    @classmethod
    def _generate_text_report(cls, workflow_id: str) -> str:
        """
        Generate a text report of the workflow timing data.

        Args:
            workflow_id: Workflow ID

        Returns:
            str: Path to the generated report file
        """
        if workflow_id not in cls._timings:
            logger.warning(f"Attempted to generate report for non-existent workflow {workflow_id}")
            return ""

        workflow_data = cls._timings[workflow_id]

        # Create a filename with timestamp
        timestamp = datetime.datetime.fromtimestamp(workflow_data["start_time"]).strftime('%Y%m%d_%H%M%S')
        workflow_name = workflow_data["name"].replace(" ", "_").lower()
        filename = f"{timestamp}_{workflow_name}_{workflow_id}_report.txt"

        file_path = os.path.join(cls._reports_dir, filename)

        try:
            with open(file_path, 'w') as f:
                # Write header
                f.write(f"Timing Report for {workflow_data['name']}\n")
                f.write(f"{'=' * 50}\n\n")

                # Write workflow summary
                f.write(f"Workflow ID: {workflow_id}\n")
                f.write(f"Start Time: {workflow_data['formatted_start_time']}\n")
                f.write(f"End Time: {workflow_data['formatted_end_time']}\n")
                f.write(f"Total Duration: {workflow_data['formatted_total_duration']}\n\n")

                # Write stage details
                f.write(f"Stage Details:\n")
                f.write(f"{'-' * 50}\n\n")

                # Sort stages by start time
                stages = sorted(workflow_data['stages'], key=lambda x: x['start_time'])

                # Calculate the longest stage name for formatting
                max_name_length = max([len(stage['name']) for stage in stages]) if stages else 0

                # Write header row
                f.write(f"{'Stage Name':{max_name_length + 5}} | {'Start Time'} | {'End Time'} | {'Duration'}\n")
                f.write(f"{'-' * (max_name_length + 5)}-+-{'-' * 19}-+-{'-' * 19}-+-{'-' * 15}\n")

                # Write each stage
                for stage in stages:
                    f.write(f"{stage['name']:{max_name_length + 5}} | {stage['formatted_start_time']} | {stage['formatted_end_time']} | {stage['formatted_duration']}\n")

                # Write footer
                f.write(f"\n{'=' * 50}\n")
                f.write(f"Report generated at {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

            logger.info(f"Generated text report for workflow {workflow_id} at {file_path}")
            return file_path
        except Exception as e:
            logger.error(f"Error generating text report for workflow {workflow_id}: {e}")
            return ""

    @classmethod
    def _generate_visualization(cls, workflow_id: str) -> str:
        """
        Generate a visualization of the workflow timing data.

        Args:
            workflow_id: Workflow ID

        Returns:
            str: Path to the generated visualization file
        """
        if workflow_id not in cls._timings:
            logger.warning(f"Attempted to generate visualization for non-existent workflow {workflow_id}")
            return ""

        workflow_data = cls._timings[workflow_id]

        # Create a filename with timestamp
        timestamp = datetime.datetime.fromtimestamp(workflow_data["start_time"]).strftime('%Y%m%d_%H%M%S')
        workflow_name = workflow_data["name"].replace(" ", "_").lower()
        filename = f"{timestamp}_{workflow_name}_{workflow_id}_timeline.png"

        file_path = os.path.join(cls._visualizations_dir, filename)

        try:
            # Sort stages by start time
            stages = sorted(workflow_data['stages'], key=lambda x: x['start_time'])

            if not stages:
                logger.warning(f"No stages found for workflow {workflow_id}")
                return ""

            # Create figure and axis with more height per stage
            fig = plt.figure(figsize=(14, max(10, len(stages) * 0.7)))
            ax = fig.add_subplot(111)

            # Calculate workflow start time for relative positioning
            workflow_start = workflow_data['start_time']

            # Prepare data for plotting
            stage_names = []
            stage_starts = []
            stage_durations = []

            for i, stage in enumerate(stages):
                # Wrap long stage names
                stage_names.append(cls._wrap_text(stage['name']))
                stage_starts.append(stage['start_time'] - workflow_start)
                stage_durations.append(stage['duration'])

            # Create horizontal bar chart with more height and spacing
            bars = ax.barh(stage_names, stage_durations, left=stage_starts, height=0.5)

            # Make stage names wrap if they're too long
            ax.tick_params(axis='y', labelsize=9)

            # Increase figure width if there are long stage names
            max_name_len = max([len(name) for name in stage_names]) if stage_names else 0
            if max_name_len > 30:
                fig.set_figwidth(16)

            # Add duration labels with better positioning and font size
            for i, bar in enumerate(bars):
                width = bar.get_width()
                label_x_pos = bar.get_x() + width / 2
                # Only add text label if the bar is wide enough
                if width > 1.0:  # Only add text if duration is more than 1 second
                    ax.text(label_x_pos, bar.get_y() + bar.get_height() / 2,
                            f"{cls._format_duration(width)}", ha='center', va='center',
                            color='white', fontweight='bold', fontsize=9)

            # Set labels and title
            ax.set_xlabel('Time (seconds from start)')
            ax.set_title(f"Timeline for {workflow_data['name']}")

            # Add grid lines
            ax.grid(axis='x', linestyle='--', alpha=0.7)

            # Adjust layout and save
            plt.subplots_adjust(left=0.3)  # Add more space for stage names
            plt.tight_layout(pad=3.0)  # Add padding around the plot
            plt.savefig(file_path, dpi=100, bbox_inches='tight')
            plt.close()

            # Generate a bar chart of stage durations
            cls._generate_stage_duration_chart(workflow_id, stages)

            logger.info(f"Generated visualization for workflow {workflow_id} at {file_path}")
            return file_path
        except Exception as e:
            logger.error(f"Error generating visualization for workflow {workflow_id}: {e}")
            return ""

    @classmethod
    def _generate_stage_duration_chart(cls, workflow_id: str, stages: List[Dict[str, Any]]) -> str:
        """
        Generate a bar chart of stage durations.

        Args:
            workflow_id: Workflow ID
            stages: List of stage data

        Returns:
            str: Path to the generated chart file
        """
        if not stages:
            return ""

        workflow_data = cls._timings[workflow_id]

        # Create a filename with timestamp
        timestamp = datetime.datetime.fromtimestamp(workflow_data["start_time"]).strftime('%Y%m%d_%H%M%S')
        workflow_name = workflow_data["name"].replace(" ", "_").lower()
        filename = f"{timestamp}_{workflow_name}_{workflow_id}_durations.png"

        file_path = os.path.join(cls._visualizations_dir, filename)

        try:
            # Prepare data for plotting
            stage_names = []
            stage_durations = []

            for stage in stages:
                # Wrap long stage names
                stage_names.append(cls._wrap_text(stage['name']))
                stage_durations.append(stage['duration'])

            # Sort by duration (descending)
            sorted_indices = np.argsort(stage_durations)[::-1]
            stage_names = [stage_names[i] for i in sorted_indices]
            stage_durations = [stage_durations[i] for i in sorted_indices]

            # Create figure and axis with more height per stage
            fig = plt.figure(figsize=(14, max(10, len(stages) * 0.7)))
            ax = fig.add_subplot(111)

            # Create bar chart with more height and spacing
            bars = ax.barh(stage_names, stage_durations, height=0.5)

            # Make stage names wrap if they're too long
            ax.tick_params(axis='y', labelsize=9)

            # Increase figure width if there are long stage names
            max_name_len = max([len(name) for name in stage_names]) if stage_names else 0
            if max_name_len > 30:
                fig.set_figwidth(16)

            # Add duration labels with better positioning
            for i, bar in enumerate(bars):
                width = bar.get_width()
                # Position the text with a small offset from the end of the bar
                # Use smaller font size and add a small background for readability
                ax.text(width + (width * 0.01), bar.get_y() + bar.get_height() / 2,
                        f" {cls._format_duration(width)} ", ha='left', va='center',
                        fontsize=9, bbox=dict(facecolor='white', alpha=0.7, pad=2, boxstyle='round'))

            # Set labels and title
            ax.set_xlabel('Duration (seconds)')
            ax.set_title(f"Stage Durations for {workflow_data['name']}")

            # Add grid lines
            ax.grid(axis='x', linestyle='--', alpha=0.7)

            # Adjust layout and save
            plt.subplots_adjust(left=0.3)  # Add more space for stage names
            plt.tight_layout(pad=3.0)  # Add padding around the plot
            plt.savefig(file_path, dpi=100, bbox_inches='tight')
            plt.close()

            return file_path
        except Exception as e:
            logger.error(f"Error generating stage duration chart for workflow {workflow_id}: {e}")
            return ""

    @classmethod
    def clean_old_workflows(cls, max_age_seconds: int = 86400) -> None:
        """
        Remove old workflow timing data to prevent memory leaks.

        Args:
            max_age_seconds: Maximum age in seconds for completed workflows (default: 24 hours)
        """
        current_time = time.time()
        to_remove = []

        for workflow_id, workflow in cls._timings.items():
            if workflow["end_time"] and (current_time - workflow["end_time"]) > max_age_seconds:
                to_remove.append(workflow_id)

        for workflow_id in to_remove:
            del cls._timings[workflow_id]
            if workflow_id in cls._active_stages:
                del cls._active_stages[workflow_id]

        if to_remove:
            logger.info(f"Cleaned up {len(to_remove)} old workflow timing data")

# Create a singleton instance
timing_tracker = TimingTracker()
