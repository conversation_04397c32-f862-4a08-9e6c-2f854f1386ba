# utils/token_utils.py

import json
import re


def concatenate_answers(arr, res_type):
    """
    This function concatenates answers based on the response type.

    If the response type is not 'mcq', 'qna', or 'flashcards', it concatenates all the 'answer' fields in the input list
    with a newline character in between each answer. If the response type is one of 'mcq', 'qna', or 'flashcards', it
    appends all the 'answer' fields in the input list to a new list.

    Args:
        arr (list): A list of dictionaries, each containing an 'answer' field.
        res_type (str): The response type. It determines how the 'answer' fields in the input list are concatenated.

    Returns: str or list: If the response type is not 'mcq', 'qna', or 'flashcards', it returns a string with all the
    'answer' fields in the input list concatenated with a newline character in between each answer. If the response
    type is one of 'mcq', 'qna', or 'flashcards', it returns a list with all the 'answer' fields in the input list.
    """
    concatenated_answers = ''
    mcqs_arr = []
    if (
        'mcq' not in res_type and
        'qna' not in res_type and
        'flashcards' not in res_type
    ):
        for obj in arr:
            concatenated_answers += obj['answer'] + '\n'
        return concatenated_answers
    else:
        for obj in arr:
            mcqs_arr.append(obj['answer'])
        return mcqs_arr


def calculate_sum(arr):
    """
    Function to calculate the sum of 'inputCost', 'outputCost', and 'totalCost' from each dictionary in a list.
    It iterates over the list and adds up the 'inputCost', 'outputCost', and 'totalCost' from each dictionary.

    Args:
        arr (list): The list of dictionaries.

    Returns:
        dict: A dictionary with the total 'inputCost', 'outputCost', and 'totalCost'.
    """
    total_input_cost = 0
    total_output_cost = 0
    total_total_cost = 0

    for obj in arr:
        total_input_cost += obj['inputCost']
        total_output_cost += obj['outputCost']
        total_total_cost += obj['totalCost']

    return {
        'totalInputCost': total_input_cost,
        'totalOutputCost': total_output_cost,
        'totalTotalCost': total_total_cost,
    }


def calculate_token_sum(arr):
    """
    Function to calculate the sum of 'promptTokens', 'completionTokens', and 'totalTokens' from each dictionary
    in a list.
    It iterates over the list and adds up the 'promptTokens', 'completionTokens', and 'totalTokens' from each
    dictionary.

    Args:
        arr (list): The list of dictionaries.

    Returns:
        dict: A dictionary with the total 'promptTokens', 'completionTokens', and 'totalTokens'.
    """
    total_prompt_tokens = 0
    total_completion_tokens = 0
    total_total_tokens = 0

    for obj in arr:
        total_prompt_tokens += obj['promptTokens']
        total_completion_tokens += obj['completionTokens']
        total_total_tokens += obj['totalTokens']

    return {
        'totalPromptTokens': total_prompt_tokens,
        'totalCompletionTokens': total_completion_tokens,
        'totalTotalTokens': total_total_tokens,
    }


def format_mcq(answers):
    """
    Function to format multiple choice questions (MCQs) in a list of answers.

    This function iterates over a list of answers, each of which is a JSON string. It parses each answer and checks
    if it contains a 'questions' field. If it does, it modifies the 'answer' field of each question based on certain
    conditions: if the 'answer' field is equal to 'op1', 'op2', 'op3', or 'op4' and does not start with 'op',
    it is replaced with 'op1', 'op2', 'op3', or 'op4' respectively. The modified questions are then added to the
    `formatted_answer` list as a JSON string.

    Args:
        answers (list): The list of answers, each of which is a JSON string.

    Returns:
        list: The list of formatted answers, each of which is a JSON string.
    """
    formatted_answer = []
    for a in range(len(answers)):
        parsed_content = answers[a]
        parsed_content = json.loads(re.sub(r'\n\s*', '', parsed_content))
        if 'questions' not in parsed_content:
            return answers
        questions = parsed_content['questions']
        for q in range(len(questions)):
            if questions[q]['answer'] == questions[q]['op1'] and not questions[q]['answer'].startswith('op'):
                questions[q]['answer'] = 'op1'
            elif questions[q]['answer'] == questions[q]['op2'] and not questions[q]['answer'].startswith('op'):
                questions[q]['answer'] = 'op2'
            elif questions[q]['answer'] == questions[q]['op3'] and not questions[q]['answer'].startswith('op'):
                questions[q]['answer'] = 'op3'
            elif questions[q]['answer'] == questions[q]['op4'] and not questions[q]['answer'].startswith('op'):
                questions[q]['answer'] = 'op4'
        formatted_answer.append(json.dumps({'questions': questions}))
    return formatted_answer


def is_not_null_or_not_empty(string):
    if string is None or string == '':
        return False
    else:
        return True
