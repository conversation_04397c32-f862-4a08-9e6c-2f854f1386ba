# redis_utils.py
import redis
from config import WS_REDIS_HOST, WS_REDIS_PORT, WS_REDIS_DB

# Connect to Redis server
def get_redis_client():
    """
    Creates and returns a Redis client object.

    Returns:
        redis.Redis: A Redis client object connected to the specified Redis server.
    """
    redis_client = redis.Redis(host=WS_REDIS_HOST, port=WS_REDIS_PORT, db=WS_REDIS_DB)
    return redis_client