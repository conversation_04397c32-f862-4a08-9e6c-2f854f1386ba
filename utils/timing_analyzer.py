"""
Utility for analyzing and displaying timing data.
"""
import os
import json
import logging
from typing import Dict, List, Any, Optional
import datetime
import matplotlib.pyplot as plt
import pandas as pd

from utils.timing_tracker import TimingTracker

logger = logging.getLogger(__name__)

class TimingAnalyzer:
    """
    Analyzes and displays timing data from the TimingTracker.
    """
    
    def __init__(self, output_dir: str = "storage/timing_logs"):
        """
        Initialize the TimingAnalyzer.
        
        Args:
            output_dir: Directory containing timing log files
        """
        self.output_dir = output_dir
    
    def load_timing_data(self, workflow_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Load timing data from files.
        
        Args:
            workflow_id: Optional workflow ID to filter by
            
        Returns:
            List[Dict]: List of workflow timing data
        """
        if not os.path.exists(self.output_dir):
            logger.warning(f"Output directory {self.output_dir} does not exist")
            return []
        
        timing_data = []
        
        for filename in os.listdir(self.output_dir):
            if not filename.endswith('.json'):
                continue
            
            file_path = os.path.join(self.output_dir, filename)
            
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                
                # Filter by workflow ID if provided
                if workflow_id and data.get('id') != workflow_id:
                    continue
                
                timing_data.append(data)
            except Exception as e:
                logger.error(f"Error loading timing data from {file_path}: {e}")
        
        return timing_data
    
    def generate_summary(self, timing_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate a summary of timing data.
        
        Args:
            timing_data: List of workflow timing data
            
        Returns:
            Dict: Summary statistics
        """
        if not timing_data:
            return {
                "workflow_count": 0,
                "total_duration": 0,
                "avg_duration": 0,
                "min_duration": 0,
                "max_duration": 0,
                "stages": {}
            }
        
        # Calculate workflow statistics
        durations = [w.get('total_duration', 0) for w in timing_data]
        
        # Collect stage statistics
        stages = {}
        for workflow in timing_data:
            for stage in workflow.get('stages', []):
                stage_name = stage.get('name')
                if stage_name not in stages:
                    stages[stage_name] = {
                        "count": 0,
                        "total_duration": 0,
                        "durations": []
                    }
                
                stages[stage_name]["count"] += 1
                stages[stage_name]["total_duration"] += stage.get('duration', 0)
                stages[stage_name]["durations"].append(stage.get('duration', 0))
        
        # Calculate stage statistics
        for stage_name, stage_data in stages.items():
            stage_data["avg_duration"] = stage_data["total_duration"] / stage_data["count"]
            stage_data["min_duration"] = min(stage_data["durations"])
            stage_data["max_duration"] = max(stage_data["durations"])
            # Remove the raw durations list to keep the summary concise
            del stage_data["durations"]
        
        return {
            "workflow_count": len(timing_data),
            "total_duration": sum(durations),
            "avg_duration": sum(durations) / len(durations),
            "min_duration": min(durations),
            "max_duration": max(durations),
            "stages": stages
        }
    
    def plot_workflow_durations(self, timing_data: List[Dict[str, Any]], output_file: Optional[str] = None) -> None:
        """
        Plot workflow durations.
        
        Args:
            timing_data: List of workflow timing data
            output_file: Optional file path to save the plot
        """
        if not timing_data:
            logger.warning("No timing data to plot")
            return
        
        # Extract workflow names and durations
        names = [w.get('name', f"Workflow {i}") for i, w in enumerate(timing_data)]
        durations = [w.get('total_duration', 0) for w in timing_data]
        
        # Create a DataFrame for easier plotting
        df = pd.DataFrame({
            'Workflow': names,
            'Duration (s)': durations
        })
        
        # Sort by duration
        df = df.sort_values('Duration (s)', ascending=False)
        
        # Create the plot
        plt.figure(figsize=(10, 6))
        plt.bar(df['Workflow'], df['Duration (s)'])
        plt.xlabel('Workflow')
        plt.ylabel('Duration (seconds)')
        plt.title('Workflow Durations')
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()
        
        if output_file:
            plt.savefig(output_file)
            logger.info(f"Saved workflow duration plot to {output_file}")
        else:
            plt.show()
    
    def plot_stage_durations(self, timing_data: List[Dict[str, Any]], output_file: Optional[str] = None) -> None:
        """
        Plot average stage durations across all workflows.
        
        Args:
            timing_data: List of workflow timing data
            output_file: Optional file path to save the plot
        """
        if not timing_data:
            logger.warning("No timing data to plot")
            return
        
        # Collect stage durations
        stage_data = {}
        for workflow in timing_data:
            for stage in workflow.get('stages', []):
                stage_name = stage.get('name')
                if stage_name not in stage_data:
                    stage_data[stage_name] = []
                
                stage_data[stage_name].append(stage.get('duration', 0))
        
        # Calculate average durations
        avg_durations = {
            stage_name: sum(durations) / len(durations)
            for stage_name, durations in stage_data.items()
        }
        
        # Create a DataFrame for easier plotting
        df = pd.DataFrame({
            'Stage': list(avg_durations.keys()),
            'Avg Duration (s)': list(avg_durations.values())
        })
        
        # Sort by duration
        df = df.sort_values('Avg Duration (s)', ascending=False)
        
        # Create the plot
        plt.figure(figsize=(10, 6))
        plt.bar(df['Stage'], df['Avg Duration (s)'])
        plt.xlabel('Stage')
        plt.ylabel('Average Duration (seconds)')
        plt.title('Average Stage Durations')
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()
        
        if output_file:
            plt.savefig(output_file)
            logger.info(f"Saved stage duration plot to {output_file}")
        else:
            plt.show()
    
    def plot_workflow_timeline(self, workflow_data: Dict[str, Any], output_file: Optional[str] = None) -> None:
        """
        Plot a timeline of stages within a single workflow.
        
        Args:
            workflow_data: Workflow timing data
            output_file: Optional file path to save the plot
        """
        if not workflow_data or 'stages' not in workflow_data or not workflow_data['stages']:
            logger.warning("No stage data to plot")
            return
        
        # Extract stage data
        stages = workflow_data['stages']
        workflow_start = workflow_data.get('start_time', 0)
        
        # Create a DataFrame for the timeline
        data = []
        for stage in stages:
            stage_name = stage.get('name', 'Unknown')
            start_time = stage.get('start_time', 0) - workflow_start  # Relative to workflow start
            duration = stage.get('duration', 0)
            
            data.append({
                'Stage': stage_name,
                'Start': start_time,
                'Duration': duration,
                'End': start_time + duration
            })
        
        df = pd.DataFrame(data)
        
        # Sort by start time
        df = df.sort_values('Start')
        
        # Create the plot
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # Plot each stage as a horizontal bar
        for i, row in df.iterrows():
            ax.barh(row['Stage'], row['Duration'], left=row['Start'], height=0.5)
            
            # Add duration text
            ax.text(
                row['Start'] + row['Duration'] / 2,
                row['Stage'],
                f"{row['Duration']:.2f}s",
                ha='center',
                va='center',
                color='white',
                fontweight='bold'
            )
        
        ax.set_xlabel('Time (seconds)')
        ax.set_title(f"Timeline for {workflow_data.get('name', 'Workflow')}")
        ax.grid(axis='x', linestyle='--', alpha=0.7)
        
        if output_file:
            plt.savefig(output_file)
            logger.info(f"Saved workflow timeline plot to {output_file}")
        else:
            plt.show()
    
    def generate_report(self, timing_data: List[Dict[str, Any]], output_file: Optional[str] = None) -> str:
        """
        Generate a comprehensive report of timing data.
        
        Args:
            timing_data: List of workflow timing data
            output_file: Optional file path to save the report
            
        Returns:
            str: Report text
        """
        if not timing_data:
            report = "No timing data available."
            if output_file:
                with open(output_file, 'w') as f:
                    f.write(report)
            return report
        
        # Generate summary
        summary = self.generate_summary(timing_data)
        
        # Format the report
        report = "# Timing Analysis Report\n\n"
        report += f"Generated: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        report += "## Summary\n\n"
        report += f"- Total workflows: {summary['workflow_count']}\n"
        report += f"- Total duration: {self._format_duration(summary['total_duration'])}\n"
        report += f"- Average duration: {self._format_duration(summary['avg_duration'])}\n"
        report += f"- Minimum duration: {self._format_duration(summary['min_duration'])}\n"
        report += f"- Maximum duration: {self._format_duration(summary['max_duration'])}\n\n"
        
        report += "## Stage Analysis\n\n"
        report += "| Stage | Count | Total Duration | Avg Duration | Min Duration | Max Duration |\n"
        report += "|-------|-------|---------------|--------------|--------------|---------------|\n"
        
        for stage_name, stage_data in summary['stages'].items():
            report += f"| {stage_name} | {stage_data['count']} | "
            report += f"{self._format_duration(stage_data['total_duration'])} | "
            report += f"{self._format_duration(stage_data['avg_duration'])} | "
            report += f"{self._format_duration(stage_data['min_duration'])} | "
            report += f"{self._format_duration(stage_data['max_duration'])} |\n"
        
        report += "\n## Workflow Details\n\n"
        
        for i, workflow in enumerate(timing_data):
            report += f"### {i+1}. {workflow.get('name', 'Unnamed Workflow')}\n\n"
            report += f"- ID: {workflow.get('id', 'Unknown')}\n"
            report += f"- Start time: {workflow.get('formatted_start_time', 'Unknown')}\n"
            report += f"- End time: {workflow.get('formatted_end_time', 'Unknown')}\n"
            report += f"- Total duration: {workflow.get('formatted_total_duration', 'Unknown')}\n\n"
            
            report += "#### Stages\n\n"
            report += "| Stage | Start Time | End Time | Duration |\n"
            report += "|-------|------------|----------|----------|\n"
            
            for stage in workflow.get('stages', []):
                report += f"| {stage.get('name', 'Unknown')} | "
                report += f"{stage.get('formatted_start_time', 'Unknown')} | "
                report += f"{stage.get('formatted_end_time', 'Unknown')} | "
                report += f"{stage.get('formatted_duration', 'Unknown')} |\n"
            
            report += "\n"
        
        if output_file:
            with open(output_file, 'w') as f:
                f.write(report)
            logger.info(f"Saved timing report to {output_file}")
        
        return report
    
    @staticmethod
    def _format_duration(seconds: float) -> str:
        """
        Format duration in hours, minutes, and seconds.
        
        Args:
            seconds: Duration in seconds
            
        Returns:
            str: Formatted duration string
        """
        hours, remainder = divmod(seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        parts = []
        if hours > 0:
            parts.append(f"{int(hours)} hr")
        if minutes > 0 or hours > 0:
            parts.append(f"{int(minutes)} min")
        parts.append(f"{seconds:.2f} sec")
        
        return " ".join(parts)

# Example usage
if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    analyzer = TimingAnalyzer()
    
    # Load timing data
    timing_data = analyzer.load_timing_data()
    
    if timing_data:
        # Generate and print summary
        summary = analyzer.generate_summary(timing_data)
        print(f"Found {summary['workflow_count']} workflows with total duration {analyzer._format_duration(summary['total_duration'])}")
        
        # Generate report
        report = analyzer.generate_report(timing_data, "storage/timing_report.md")
        print(f"Generated report with {len(report)} characters")
        
        # Plot workflow durations
        analyzer.plot_workflow_durations(timing_data, "storage/workflow_durations.png")
        
        # Plot stage durations
        analyzer.plot_stage_durations(timing_data, "storage/stage_durations.png")
        
        # Plot timeline for the first workflow
        if timing_data:
            analyzer.plot_workflow_timeline(timing_data[0], "storage/workflow_timeline.png")
    else:
        print("No timing data found")
