"""
open_utils.py

This module contains utility functions that can be used across the project for common operations 
such as currency conversion, file handling, and data parsing.

Functions:
- convert_usd_to_inr(usd_amount: float) -> float: Converts USD to INR using the latest exchange rate.
- ensure_directory_exists(directory_path: str) -> None: Ensures a directory exists or creates it if not found.
- delete_all_files_in_directory(directory_path: str) -> None: Deletes all files in a directory but keeps the directory.
- parse_int(value: Union[str, int]) -> int: Ensures a value is safely converted to an integer.
- sanitize_filename(filename: str) -> str: Cleans a filename by removing invalid characters.
"""


import requests
from typing import Union


def convert_usd_to_inr(usd_amount: Union[str, int, float]) -> float:
    """
    Converts a given amount from USD to INR using real-time exchange rates.

    Args:
        usd_amount (Union[str, int, float]): The amount in USD to be converted.
            - If a string, it will be converted to a float.
            - If an integer, it will be treated as a float.

    Returns:
        float: Equivalent amount in INR, rounded to 2 decimal places.

    Raises:
        ValueError: If usd_amount cannot be converted to a valid number.
        Exception: If there is an issue fetching the exchange rate.
    """
    try:
        # Convert usd_amount to float safely
        if isinstance(usd_amount, str):
            usd_amount = usd_amount.strip()
            if usd_amount.replace(".", "", 1).isdigit():  # Check if it's a valid number (supports decimals)
                usd_amount = float(usd_amount)
            else:
                raise ValueError(f"Invalid input for USD amount: {usd_amount}")
        elif isinstance(usd_amount, int):
            usd_amount = float(usd_amount)  # Convert int to float directly
        elif not isinstance(usd_amount, float):
            raise ValueError(f"Invalid type for USD amount: {type(usd_amount)}")

        # Fetch real-time exchange rate
        response = requests.get("https://api.exchangerate-api.com/v4/latest/USD")
        response.raise_for_status()  # Raise error for bad response
        data = response.json()
        exchange_rate = data["rates"].get("INR", 0)  # Get INR rate, default to 0 if not found

        return round(usd_amount * exchange_rate, 2)

    except ValueError as ve:
        raise ValueError(f"Conversion error: {str(ve)}")
    except Exception as e:
        raise Exception(f"Error fetching exchange rate: {str(e)}")
