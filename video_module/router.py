from fastapi import APIRouter, Depends, Request, HTTPException
from auth.dependencies import require_login
from auth.rbac import require_roles
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.responses import HTMLResponse
from pathlib import Path

from video_module.api.api import api_router as video_router

# Create router
router = APIRouter(prefix="/video", tags=["video"])

# Templates
templates_path = Path(__file__).parent / "templates"
templates = Jinja2Templates(directory=str(templates_path))

# Also use the main application templates
main_templates_path = Path(__file__).parent.parent / "web" / "templates"
templates.env.loader.searchpath.append(str(main_templates_path))

@router.get("/", response_class=HTMLResponse)
async def video_home(
    request: Request,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Video generator home page
    """
    if login_check:
        return login_check

    return templates.TemplateResponse("video_home.html", {"request": request})

@router.get("/sample", response_class=HTMLResponse)
async def sample(
    request: Request,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Show the sample question paper.
    """
    if login_check:
        return login_check

    return templates.TemplateResponse("sample.html", {"request": request})


router.include_router(video_router)
