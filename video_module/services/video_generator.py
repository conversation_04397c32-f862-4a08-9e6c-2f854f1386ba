"""
Video Generator Service

This module handles the conversion of HTML content to scrolling videos using Selenium
for proper HTML rendering. It supports multiple aspect ratios and customizable styling.
"""

import os
import time
import uuid
import tempfile
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple

from PIL import Image, ImageDraw, ImageFont
from moviepy.video.compositing.concatenate import concatenate_videoclips
from moviepy.video.io.ImageSequenceClip import ImageSe<PERSON><PERSON><PERSON>
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager

# Set up logging
logger = logging.getLogger(__name__)

# Constants
DEFAULT_WIDTH = 1920
DEFAULT_HEIGHT = 1080
DEFAULT_FPS = 30
DEFAULT_SCROLL_DURATION = 60  # seconds

# Get base directory
BASE_DIR = Path(__file__).resolve().parent.parent.parent
TEMP_DIR = BASE_DIR / "video_module" / "temp"
OUTPUT_DIR = BASE_DIR / "video_module" / "static" / "output"

# Ensure directories exist
TEMP_DIR.mkdir(exist_ok=True, parents=True)
OUTPUT_DIR.mkdir(exist_ok=True, parents=True)


class VideoGenerator:
    """Video generator service for creating scrolling videos from HTML content."""

    def __init__(self):
        """Initialize the video generator service."""
        self.temp_dir = TEMP_DIR
        self.output_dir = OUTPUT_DIR

    def setup_driver(self, width: int = DEFAULT_WIDTH) -> webdriver.Chrome:
        """
        Set up a headless Chrome browser for rendering HTML.

        Args:
            width: Width of the browser window

        Returns:
            Configured Chrome WebDriver
        """
        options = Options()
        options.add_argument("--headless")
        options.add_argument("--disable-gpu")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument(f"--window-size={width},2000")  # Initial height, will be adjusted
        options.add_argument("--hide-scrollbars")  # Hide scrollbars for cleaner screenshots
        options.add_argument("--disable-javascript")  # Disable JavaScript to prevent infinite loops
        options.page_load_strategy = 'eager'  # Load only the DOM, don't wait for resources

        try:
            # Use webdriver_manager to handle driver installation
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=options)
            return driver
        except Exception as e:
            logger.error(f"Error setting up Chrome driver: {e}")
            # Fallback to direct path if available
            try:
                driver = webdriver.Chrome(options=options)
                return driver
            except Exception as e2:
                logger.error(f"Fallback also failed: {e2}")
                raise Exception("Could not initialize Chrome driver. Make sure Chrome is installed.")

    def html_to_image(
        self,
        html_content: str,
        output_path: str,
        width: int = DEFAULT_WIDTH,
        bg_color: str = "#FFFFFF",
        text_color: str = "#000000"
    ) -> str:
        """
        Convert HTML content to a PNG image using Selenium for proper rendering.

        Args:
            html_content: HTML content to convert
            output_path: Path to save the output image
            width: Width of the output image
            bg_color: Background color in hex format
            text_color: Text color in hex format

        Returns:
            Path to the generated image
        """
        # Create a temporary HTML file with custom styling
        with tempfile.NamedTemporaryFile(suffix='.html', delete=False, mode='w', encoding='utf-8') as f:
            styled_html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <style>
                    @page {{ size: {width}px; margin: 0; }}
                    html {{
                        width: {width}px !important;
                        margin: 0 !important;
                        padding: 0 !important;
                    }}
                    body {{
                        width: {width}px !important;
                        margin: 0 !important;
                        padding: 0 !important;
                        background-color: {bg_color};
                        color: {text_color};
                        font-family: Arial, sans-serif;
                    }}
                    * {{ box-sizing: border-box; }}

                    .container {{
                        width: {width}px !important;
                        padding: 20px;
                        margin: 0 auto;
                        background-color: {bg_color};
                    }}

                    pre, code {{
                        white-space: pre-wrap;
                        word-wrap: break-word;
                        overflow-wrap: break-word;
                    }}

                    table {{
                        border-collapse: collapse;
                        width: 100%;
                        margin: 15px 0;
                    }}

                    th, td {{
                        border: 1px solid {text_color};
                        padding: 8px;
                        text-align: left;
                    }}

                    img {{
                        max-width: 100%;
                        height: auto;
                    }}

                    /* Force all elements to respect container width */
                    div, p, h1, h2, h3, h4, h5, h6, ul, ol, li, table, tr, td, th, pre, code, section, article, aside, header, footer, nav, main {{
                        max-width: 100%;
                        overflow-wrap: break-word;
                    }}
                </style>
            </head>
            <body>
                <div class="container">
                    {html_content}
                </div>
            </body>
            </html>
            """
            f.write(styled_html)
            temp_html_path = f.name

        driver = None
        try:
            # Initialize the driver
            driver = self.setup_driver(width)

            # Set page load timeout to prevent infinite loops
            driver.set_page_load_timeout(10)  # 10 seconds timeout

            try:
                # Load the HTML file
                file_url = f"file://{os.path.abspath(temp_html_path)}"
                driver.get(file_url)

                # Wait for content to render
                time.sleep(1)
            except Exception as e:
                logger.error(f"Page load timed out or failed: {e}")
                # Continue anyway - we'll work with what we have

            # Get the rendered height of the page
            height = driver.execute_script("return Math.max(document.body.scrollHeight, document.documentElement.scrollHeight)")

            # Set the window size to match the content
            driver.set_window_size(width, height)

            # Wait for the resize to take effect
            time.sleep(0.5)

            # Take a screenshot of the entire page
            # Use get_screenshot_as_file instead of save_screenshot for better quality
            driver.get_screenshot_as_file(output_path)

            # Verify the screenshot was taken correctly
            if not os.path.exists(output_path) or os.path.getsize(output_path) == 0:
                raise Exception("Screenshot failed or produced an empty file")

            return output_path

        except Exception as e:
            logger.error(f"Error rendering HTML with Selenium: {e}")
            raise

        finally:
            # Clean up
            if driver:
                driver.quit()
            if os.path.exists(temp_html_path):
                os.remove(temp_html_path)

    def create_title_frame(
        self,
        title: str,
        width: int,
        height: int,
        bg_color: str = "#FFFFFF",
        title_color: str = "#000000"
    ) -> str:
        """
        Create a title frame as an image.

        Args:
            title: Title text
            width: Frame width
            height: Frame height
            bg_color: Background color in hex format
            title_color: Title text color in hex format

        Returns:
            Path to the title frame image
        """
        # Convert hex colors to RGB
        bg_color_rgb = tuple(int(bg_color.lstrip('#')[i:i+2], 16) for i in (0, 2, 4))
        title_color_rgb = tuple(int(title_color.lstrip('#')[i:i+2], 16) for i in (0, 2, 4))

        # Create a blank image with the specified background color
        image = Image.new('RGB', (width, height), color=bg_color_rgb)
        draw = ImageDraw.Draw(image)

        # Try to load a font, fall back to default if not available
        try:
            # Adjust font size based on title length and frame width
            font_size = min(height // 10, width // (len(title) // 2 + 1))
            font = ImageFont.truetype("Arial", font_size)
        except IOError:
            # Use default font if Arial is not available
            font = ImageFont.load_default()

        # Calculate text position to center it
        try:
            text_width, text_height = draw.textsize(title, font=font)
        except AttributeError:
            # For newer versions of Pillow
            text_width, text_height = draw.textbbox((0, 0), title, font=font)[2:4]
        position = ((width - text_width) // 2, (height - text_height) // 2)

        # Draw the title text
        draw.text(position, title, fill=title_color_rgb, font=font)

        # Save to a temporary file
        temp_path = os.path.join(self.temp_dir, f"title_{uuid.uuid4().hex}.png")
        image.save(temp_path)

        return temp_path

    def generate_scrolling_video(
        self,
        html_content: str,
        title: str,
        output_filename: str,
        aspect_ratio: str = "16:9",
        bg_color: str = "#FFFFFF",
        title_color: str = "#000000",
        text_color: str = "#000000",
        duration: int = DEFAULT_SCROLL_DURATION
    ) -> str:
        """
        Generate a scrolling video from HTML content.

        Args:
            html_content: HTML content to convert
            title: Video title
            output_filename: Output filename (without extension)
            aspect_ratio: Video aspect ratio (16:9, 9:16, etc.)
            bg_color: Background color in hex format
            title_color: Title text color in hex format
            text_color: Main text color in hex format
            duration: Video duration in seconds

        Returns:
            Path to the generated video
        """
        # Parse aspect ratio
        width_ratio, height_ratio = map(int, aspect_ratio.split(':'))

        # Calculate dimensions based on aspect ratio
        if width_ratio > height_ratio:  # Landscape
            width = DEFAULT_WIDTH
            height = DEFAULT_WIDTH * height_ratio // width_ratio
        else:  # Portrait
            height = DEFAULT_HEIGHT
            width = DEFAULT_HEIGHT * width_ratio // height_ratio

        # Create temporary directory for this job
        job_id = uuid.uuid4().hex
        job_dir = os.path.join(self.temp_dir, f"job_{job_id}")
        os.makedirs(job_dir, exist_ok=True)

        try:
            # Generate content image
            content_image_path = os.path.join(job_dir, "content.png")
            self.html_to_image(html_content, content_image_path, width, bg_color, text_color)

            # Create title frame
            title_image_path = self.create_title_frame(title, width, height, bg_color, title_color)

            # Calculate scroll duration (subtract title duration)
            scroll_duration = max(duration - 3, 5)  # Minimum 5 seconds

            # Create clips using simpler approach
            title_clip = ImageSequenceClip([title_image_path], durations=[3])

            # For scrolling effect, we'll create a series of frames
            content_img = Image.open(content_image_path)

            # Verify the image dimensions
            img_width, content_height = content_img.size
            logger.info(f"Content image dimensions: {img_width}x{content_height}")

            # If the image width doesn't match our target width, resize it
            if img_width != width:
                logger.info(f"Resizing image from width {img_width} to {width}")
                # Calculate new height maintaining aspect ratio
                new_height = int(content_height * (width / img_width))
                content_img = content_img.resize((width, new_height), Image.LANCZOS)
                content_height = new_height
                # Save the resized image for debugging
                content_img.save(os.path.join(job_dir, "content_resized.png"))

            # Calculate how many frames we need
            fps = DEFAULT_FPS
            total_frames = int(scroll_duration * fps)

            # Create frames directory
            frames_dir = os.path.join(job_dir, "frames")
            os.makedirs(frames_dir, exist_ok=True)

            # Generate frames for scrolling
            frame_paths = []

            # Calculate the maximum scroll position
            max_scroll = max(0, content_height - height)

            for i in range(total_frames):
                # Calculate scroll position with easing (slow start, slow end)
                progress = i / total_frames
                # Apply easing function (cubic ease in/out)
                eased_progress = progress * progress * (3 - 2 * progress) if progress <= 0.5 else \
                                1 - (1 - progress) * (1 - progress) * (3 - 2 * (1 - progress))
                scroll_position = int(eased_progress * max_scroll)

                # Ensure scroll position is valid
                scroll_position = max(0, min(scroll_position, content_height - height))

                # Create frame
                frame = content_img.crop((0, scroll_position, width, min(scroll_position + height, content_height)))

                # If the cropped frame is smaller than the target height (at the end of the content),
                # create a new frame with the background color and paste the content at the top
                if frame.height < height:
                    bg_color_rgb = tuple(int(bg_color.lstrip('#')[i:i+2], 16) for i in (0, 2, 4))
                    new_frame = Image.new('RGB', (width, height), color=bg_color_rgb)
                    new_frame.paste(frame, (0, 0))
                    frame = new_frame

                frame_path = os.path.join(frames_dir, f"frame_{i:04d}.png")
                frame.save(frame_path)
                frame_paths.append(frame_path)

            # Create scrolling clip
            scrolling_clip = ImageSequenceClip(frame_paths, fps=fps)

            # Concatenate clips
            final_clip = concatenate_videoclips([title_clip, scrolling_clip])

            # Ensure output directory exists
            os.makedirs(self.output_dir, exist_ok=True)

            # Save the video
            output_path = os.path.join(self.output_dir, f"{output_filename}_{aspect_ratio.replace(':', '_')}.mp4")
            try:
                final_clip.write_videofile(output_path, codec='libx264', fps=fps, audio=False, verbose=False, logger=None)
            except Exception as e:
                logger.error(f"Error writing video file: {e}")
                # Try with different settings
                try:
                    final_clip.write_videofile(output_path, codec='mpeg4', fps=fps, audio=False, verbose=False, logger=None)
                except Exception as e2:
                    logger.error(f"Second attempt failed: {e2}")
                    raise

            return output_path

        finally:
            # Clean up temporary files
            import shutil
            if os.path.exists(job_dir):
                shutil.rmtree(job_dir)

    def generate_videos(
        self,
        html_content: str,
        title: str,
        output_filename: str,
        aspect_ratios: Optional[List[str]] = None,
        bg_color: str = "#FFFFFF",
        title_color: str = "#000000",
        text_color: str = "#000000",
        duration: int = DEFAULT_SCROLL_DURATION
    ) -> Dict[str, str]:
        """
        Generate videos in multiple aspect ratios.

        Args:
            html_content: HTML content to convert
            title: Video title
            output_filename: Base output filename
            aspect_ratios: List of aspect ratios to generate
            bg_color: Background color in hex format
            title_color: Title text color in hex format
            text_color: Main text color in hex format
            duration: Video duration in seconds

        Returns:
            Dictionary mapping aspect ratios to video paths
        """
        if aspect_ratios is None:
            aspect_ratios = ["16:9", "9:16"]

        results = {}

        for ratio in aspect_ratios:
            try:
                video_path = self.generate_scrolling_video(
                    html_content=html_content,
                    title=title,
                    output_filename=output_filename,
                    aspect_ratio=ratio,
                    bg_color=bg_color,
                    title_color=title_color,
                    text_color=text_color,
                    duration=duration
                )

                # Store relative path for serving via FastAPI
                rel_path = os.path.basename(video_path)
                results[ratio] = rel_path

            except Exception as e:
                logger.error(f"Error generating video for aspect ratio {ratio}: {e}")
                results[ratio] = None

        return results


# Create a singleton instance
video_generator = VideoGenerator()
