<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VideoCreator</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <div class="app-container">
        <div class="header-section">
            <div class="container">
                <h1 class="app-title">Scroll<span class="highlight">Video</span></h1>
                <p class="app-subtitle">Transform your HTML content into engaging scrolling videos</p>
            </div>
        </div>
        
        <div class="container main-content">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="content-card">
                        <form id="videoForm" enctype="multipart/form-data">
                            <!-- HTML Content Section -->
                            <div class="form-section">
                                <h3 class="section-title"><i class="fas fa-code section-icon"></i> Content</h3>
                                
                                <div class="upload-container mb-3">
                                    <label for="htmlFile" class="upload-label">
                                        <i class="fas fa-file-upload upload-icon"></i>
                                        <span>Drop HTML file here or click to browse</span>
                                    </label>
                                    <input class="upload-input" type="file" id="htmlFile" name="html_file" accept=".html,.htm">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="htmlContent" class="form-label">Or paste HTML content</label>
                                    <textarea class="form-control content-textarea" id="htmlContent" name="html_content" rows="6" placeholder="Paste your HTML content here..."></textarea>
                                    <div class="sample-links">
                                        <a href="/static/sample_question_paper.html" target="_blank" class="sample-link">View sample</a> | 
                                        <a href="#" id="loadSampleBtn" class="sample-link">Load sample</a>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Styling Section -->
                            <div class="form-section">
                                <h3 class="section-title"><i class="fas fa-palette section-icon"></i> Style</h3>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="title" class="form-label">Title</label>
                                        <input type="text" class="form-control" id="title" name="title" placeholder="Enter video title" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="duration" class="form-label">Duration (seconds)</label>
                                        <input type="number" class="form-control" id="duration" name="duration" value="60" min="10" max="300">
                                    </div>
                                </div>
                                
                                <div class="color-pickers">
                                    <div class="color-picker-item">
                                        <label for="titleColor" class="form-label">Title Color</label>
                                        <div class="color-picker-container">
                                            <input type="color" class="color-picker" id="titleColor" name="title_color" value="#6200EA">
                                            <input type="text" class="color-text" id="titleColorHex" value="#6200EA">
                                        </div>
                                    </div>
                                    
                                    <div class="color-picker-item">
                                        <label for="bgColor" class="form-label">Background</label>
                                        <div class="color-picker-container">
                                            <input type="color" class="color-picker" id="bgColor" name="bg_color" value="#FFFFFF">
                                            <input type="text" class="color-text" id="bgColorHex" value="#FFFFFF">
                                        </div>
                                    </div>
                                    
                                    <div class="color-picker-item">
                                        <label for="textColor" class="form-label">Text Color</label>
                                        <div class="color-picker-container">
                                            <input type="color" class="color-picker" id="textColor" name="text_color" value="#212121">
                                            <input type="text" class="color-text" id="textColorHex" value="#212121">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Video Format Section -->
                            <div class="form-section">
                                <h3 class="section-title"><i class="fas fa-video section-icon"></i> Format</h3>
                                
                                <div class="format-options">
                                    <div class="format-option">
                                        <input class="format-checkbox" type="checkbox" id="ratio16_9" name="aspect_ratios" value="16:9" checked>
                                        <label class="format-label" for="ratio16_9">
                                            <div class="format-preview ratio-16-9"></div>
                                            <span>16:9</span>
                                            <small>YouTube, Standard</small>
                                        </label>
                                    </div>
                                    
                                    <div class="format-option">
                                        <input class="format-checkbox" type="checkbox" id="ratio9_16" name="aspect_ratios" value="9:16">
                                        <label class="format-label" for="ratio9_16">
                                            <div class="format-preview ratio-9-16"></div>
                                            <span>9:16</span>
                                            <small>Shorts, Reels, TikTok</small>
                                        </label>
                                    </div>
                                    
                                    <div class="format-option">
                                        <input class="format-checkbox" type="checkbox" id="ratio1_1" name="aspect_ratios" value="1:1">
                                        <label class="format-label" for="ratio1_1">
                                            <div class="format-preview ratio-1-1"></div>
                                            <span>1:1</span>
                                            <small>Instagram, Facebook</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Submit Button -->
                            <div class="form-section text-center">
                                <button type="submit" class="generate-btn" id="generateBtn">
                                    <span class="spinner-border spinner-border-sm d-none" id="generateSpinner" role="status" aria-hidden="true"></span>
                                    <i class="fas fa-magic me-2"></i> Generate Videos
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Results Section -->
            <div class="row justify-content-center mt-4">
                <div class="col-lg-10">
                    <div id="resultsCard" class="results-card d-none">
                        <div class="results-header">
                            <h3><i class="fas fa-check-circle me-2"></i> Your Videos</h3>
                        </div>
                        <div class="results-body">
                            <div id="videoResults">
                                <!-- Results will be displayed here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Error Alert -->
            <div class="row justify-content-center mt-4">
                <div class="col-lg-10">
                    <div id="errorAlert" class="error-alert d-none">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <span id="errorMessage"></span>
                    </div>
                </div>
            </div>
        </div>
        
        <footer class="app-footer">
            <div class="container">
                <p>© 2023 ScrollVideo Creator | Made with <i class="fas fa-heart"></i> for content creators</p>
            </div>
        </footer>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/main.js"></script>
</body>
</html>
