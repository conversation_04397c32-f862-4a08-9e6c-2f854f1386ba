{% extends "base.html" %}

{% block title %}VideoGenerator | GPT Sir{% endblock %}

{% block head %}
<!-- Bootstrap CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<!-- Font Awesome -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<!-- Custom CSS -->
<link rel="stylesheet" href="/video-static/css/style.css">
{% endblock %}

{% block content %}
<div class="app-container">
    <div class="header-section">
        <div class="container">
            <h1 class="app-title">Scroll<span class="highlight">Video</span></h1>
            <p class="app-subtitle">Transform your HTML content into engaging scrolling videos</p>
        </div>
    </div>

    <div class="container main-content">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="content-card">
                    <form id="videoForm" enctype="multipart/form-data">
                        <!-- HTML Content Section -->
                        <div class="form-section">
                            <h3 class="section-title"><i class="fas fa-code section-icon"></i> Content</h3>

                            <div class="upload-container mb-3">
                                <label for="htmlFile" class="upload-label">
                                    <i class="fas fa-file-upload upload-icon"></i>
                                    <span>Drop HTML file here or click to browse</span>
                                </label>
                                <input class="upload-input" type="file" id="htmlFile" name="html_file" accept=".html,.htm">
                            </div>

                            <div class="mb-3">
                                <label for="htmlContent" class="form-label">Or paste HTML content</label>
                                <textarea class="form-control content-textarea" id="htmlContent" name="html_content" rows="6" placeholder="Paste your HTML content here..."></textarea>
                                <div class="sample-links">
                                    <a href="/video/sample" target="_blank" class="sample-link">View sample</a> |
                                    <a href="#" id="loadSampleBtn" class="sample-link">Load sample</a>
                                </div>
                            </div>
                        </div>

                        <!-- Styling Section -->
                        <div class="form-section">
                            <h3 class="section-title"><i class="fas fa-palette section-icon"></i> Style</h3>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="title" class="form-label">Title</label>
                                    <input type="text" class="form-control" id="title" name="title" placeholder="Enter video title" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="duration" class="form-label">Duration (seconds)</label>
                                    <input type="number" class="form-control" id="duration" name="duration" value="60" min="10" max="300">
                                </div>
                            </div>

                            <div class="color-pickers">
                                <div class="color-picker-item">
                                    <label for="titleColor" class="form-label">Title Color</label>
                                    <div class="color-picker-container">
                                        <input type="color" class="color-picker" id="titleColor" name="title_color" value="#e63462">
                                        <input type="text" class="color-text" id="titleColorHex" value="#e63462">
                                    </div>
                                </div>

                                <div class="color-picker-item">
                                    <label for="bgColor" class="form-label">Background</label>
                                    <div class="color-picker-container">
                                        <input type="color" class="color-picker" id="bgColor" name="bg_color" value="#FFFFFF">
                                        <input type="text" class="color-text" id="bgColorHex" value="#FFFFFF">
                                    </div>
                                </div>

                                <div class="color-picker-item">
                                    <label for="textColor" class="form-label">Text Color</label>
                                    <div class="color-picker-container">
                                        <input type="color" class="color-picker" id="textColor" name="text_color" value="#333745">
                                        <input type="text" class="color-text" id="textColorHex" value="#333745">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Video Format Section -->
                        <div class="form-section">
                            <h3 class="section-title"><i class="fas fa-video section-icon"></i> Format</h3>

                            <div class="format-options">
                                <div class="format-option">
                                    <input class="format-checkbox" type="checkbox" id="ratio16_9" name="aspect_ratios" value="16:9" checked>
                                    <label class="format-label" for="ratio16_9">
                                        <div class="format-preview ratio-16-9"></div>
                                        <span>16:9</span>
                                        <small>YouTube, Standard</small>
                                    </label>
                                </div>

                                <div class="format-option">
                                    <input class="format-checkbox" type="checkbox" id="ratio9_16" name="aspect_ratios" value="9:16">
                                    <label class="format-label" for="ratio9_16">
                                        <div class="format-preview ratio-9-16"></div>
                                        <span>9:16</span>
                                        <small>Shorts, Reels, TikTok</small>
                                    </label>
                                </div>

                                <div class="format-option">
                                    <input class="format-checkbox" type="checkbox" id="ratio1_1" name="aspect_ratios" value="1:1">
                                    <label class="format-label" for="ratio1_1">
                                        <div class="format-preview ratio-1-1"></div>
                                        <span>1:1</span>
                                        <small>Instagram, Facebook</small>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="form-section text-center">
                            <button type="submit" class="generate-btn" id="generateBtn">
                                <span class="spinner-border spinner-border-sm d-none" id="generateSpinner" role="status" aria-hidden="true"></span>
                                <i class="fas fa-magic me-2"></i> Generate Videos
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <div class="row justify-content-center mt-4">
            <div class="col-lg-10">
                <div id="resultsCard" class="results-card d-none">
                    <div class="results-header">
                        <h3><i class="fas fa-check-circle me-2"></i> Your Videos</h3>
                    </div>
                    <div class="results-body">
                        <div id="videoResults">
                            <!-- Results will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Alert -->
        <div class="row justify-content-center mt-4">
            <div class="col-lg-10">
                <div id="errorAlert" class="error-alert d-none">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <span id="errorMessage"></span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- Custom JS -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const videoForm = document.getElementById('videoForm');
        const htmlFileInput = document.getElementById('htmlFile');
        const htmlContentTextarea = document.getElementById('htmlContent');
        const uploadContainer = document.querySelector('.upload-container');
        const titleColorInput = document.getElementById('titleColor');
        const bgColorInput = document.getElementById('bgColor');
        const textColorInput = document.getElementById('textColor');
        const titleColorHex = document.getElementById('titleColorHex');
        const bgColorHex = document.getElementById('bgColorHex');
        const textColorHex = document.getElementById('textColorHex');
        const generateBtn = document.getElementById('generateBtn');
        const generateSpinner = document.getElementById('generateSpinner');
        const resultsCard = document.getElementById('resultsCard');
        const videoResults = document.getElementById('videoResults');
        const errorAlert = document.getElementById('errorAlert');
        const errorMessage = document.getElementById('errorMessage');
        const loadSampleBtn = document.getElementById('loadSampleBtn');

        // Handle file upload styling
        if (htmlFileInput) {
            htmlFileInput.addEventListener('change', function() {
                if (this.files.length > 0) {
                    uploadContainer.classList.add('file-selected');
                    const fileName = this.files[0].name;
                    const fileLabel = uploadContainer.querySelector('span');
                    fileLabel.textContent = `Selected: ${fileName}`;
                } else {
                    uploadContainer.classList.remove('file-selected');
                    const fileLabel = uploadContainer.querySelector('span');
                    fileLabel.textContent = 'Drop HTML file here or click to browse';
                }
            });
        }

        // Handle drag and drop
        if (uploadContainer) {
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                uploadContainer.addEventListener(eventName, preventDefaults, false);
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            ['dragenter', 'dragover'].forEach(eventName => {
                uploadContainer.addEventListener(eventName, highlight, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                uploadContainer.addEventListener(eventName, unhighlight, false);
            });

            function highlight() {
                uploadContainer.classList.add('highlight');
            }

            function unhighlight() {
                uploadContainer.classList.remove('highlight');
            }

            uploadContainer.addEventListener('drop', handleDrop, false);

            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;

                if (files.length > 0) {
                    htmlFileInput.files = files;
                    const event = new Event('change');
                    htmlFileInput.dispatchEvent(event);
                }
            }
        }

        // Sync color inputs with hex text inputs
        if (titleColorInput && titleColorHex) {
            titleColorInput.addEventListener('input', function() {
                titleColorHex.value = this.value.toUpperCase();
            });

            titleColorHex.addEventListener('input', function() {
                if (/^#[0-9A-F]{6}$/i.test(this.value)) {
                    titleColorInput.value = this.value;
                }
            });
        }

        if (bgColorInput && bgColorHex) {
            bgColorInput.addEventListener('input', function() {
                bgColorHex.value = this.value.toUpperCase();
            });

            bgColorHex.addEventListener('input', function() {
                if (/^#[0-9A-F]{6}$/i.test(this.value)) {
                    bgColorInput.value = this.value;
                }
            });
        }

        if (textColorInput && textColorHex) {
            textColorInput.addEventListener('input', function() {
                textColorHex.value = this.value.toUpperCase();
            });

            textColorHex.addEventListener('input', function() {
                if (/^#[0-9A-F]{6}$/i.test(this.value)) {
                    textColorInput.value = this.value;
                }
            });
        }

        // Load sample HTML
        if (loadSampleBtn) {
            loadSampleBtn.addEventListener('click', function(e) {
                e.preventDefault();
                fetch('/video/sample')
                    .then(response => response.text())
                    .then(html => {
                        htmlContentTextarea.value = html;
                    })
                    .catch(error => {
                        console.error('Error loading sample:', error);
                    });
            });
        }

        // Form submission
        if (videoForm) {
            videoForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Hide any previous errors
                errorAlert.classList.add('d-none');

                // Show loading state
                generateBtn.disabled = true;
                generateSpinner.classList.remove('d-none');

                // Get form data
                const formData = new FormData(this);

                // Get selected aspect ratios
                const aspectRatios = [];
                document.querySelectorAll('input[name="aspect_ratios"]:checked').forEach(checkbox => {
                    aspectRatios.push(checkbox.value);
                });

                // Clear existing aspect ratios and add selected ones
                for (const entry of formData.entries()) {
                    if (entry[0] === 'aspect_ratios') {
                        formData.delete(entry[0]);
                    }
                }

                aspectRatios.forEach(ratio => {
                    formData.append('aspect_ratios', ratio);
                });

                // Send API request
                fetch('/video/videos/generate', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(err => {
                            throw new Error(err.detail || 'An error occurred');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    // Display results
                    videoResults.innerHTML = '';

                    // Check if videos is an object with keys or an array
                    if (data.videos && typeof data.videos === 'object') {
                        if (Array.isArray(data.videos)) {
                            // Handle array format
                            data.videos.forEach(video => {
                                addVideoResult(video);
                            });
                        } else {
                            // Handle object format with keys
                            Object.entries(data.videos).forEach(([ratio, url]) => {
                                if (url) {
                                    addVideoResult({
                                        aspect_ratio: ratio,
                                        filename: url.split('/').pop(),
                                        url: url
                                    });
                                }
                            });
                        }
                    }

                    // Show results
                    resultsCard.classList.remove('d-none');
                    resultsCard.scrollIntoView({ behavior: 'smooth' });
                })
                .catch(error => {
                    // Show error
                    errorMessage.textContent = error.message || 'An error occurred while generating videos';
                    errorAlert.classList.remove('d-none');
                    errorAlert.scrollIntoView({ behavior: 'smooth' });
                    console.error('Error:', error);
                })
                .finally(() => {
                    // Reset loading state
                    generateBtn.disabled = false;
                    generateSpinner.classList.add('d-none');
                });
            });
        }

        // Helper function to add video result
        function addVideoResult(video) {
            const videoCard = document.createElement('div');
            videoCard.className = 'video-card';

            let aspectRatioName = '';
            switch(video.aspect_ratio) {
                case '16:9':
                    aspectRatioName = 'Landscape (16:9)';
                    break;
                case '9:16':
                    aspectRatioName = 'Portrait (9:16)';
                    break;
                case '1:1':
                    aspectRatioName = 'Square (1:1)';
                    break;
                default:
                    aspectRatioName = video.aspect_ratio;
            }

            videoCard.innerHTML = `
                <h4 class="video-title">${aspectRatioName}</h4>
                <p class="video-info">Ready for download</p>
                <a href="${video.url || `/video/videos/download/${video.filename}`}" class="download-btn" download>
                    <i class="fas fa-download"></i> Download Video
                </a>
            `;

            videoResults.appendChild(videoCard);
        }
    });
</script>
{% endblock %}
