"""
Application configuration.
"""

import os
import logging
from pathlib import Path
from typing import List, Optional

from pydantic.v1 import BaseSettings


class Settings(BaseSettings):
    """Application settings."""

    # Base settings
    APP_NAME: str = "ScrollVideo Creator"
    API_V1_STR: str = "/api"
    DEBUG: bool = True

    # CORS settings
    BACKEND_CORS_ORIGINS: List[str] = ["http://localhost:8000", "http://127.0.0.1:8000"]

    # Directory settings
    BASE_DIR: Path = Path(__file__).resolve().parent.parent.parent
    UPLOAD_DIR: Path = BASE_DIR / "video_module" / "uploads"
    TEMP_DIR: Path = BASE_DIR / "video_module" / "temp"
    OUTPUT_DIR: Path = BASE_DIR / "video_module" / "static" / "output"

    # Video settings
    DEFAULT_WIDTH: int = 1920
    DEFAULT_HEIGHT: int = 1080
    DEFAULT_FPS: int = 30
    DEFAULT_SCROLL_DURATION: int = 60
    MAX_CONTENT_LENGTH: int = 16 * 1024 * 1024  # 16 MB

    class Config:
        """Pydantic config."""
        env_file = ".env"
        case_sensitive = True


# Create settings instance
settings = Settings()

# Ensure directories exist
settings.UPLOAD_DIR.mkdir(exist_ok=True, parents=True)
settings.TEMP_DIR.mkdir(exist_ok=True, parents=True)
settings.OUTPUT_DIR.mkdir(exist_ok=True, parents=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler()
    ]
)
