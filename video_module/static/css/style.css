/* Modern, vibrant styles for ScrollVideo Creator - Using GPT Sir color scheme */

:root {
    --primary-color: #e63462;     /* Cerise */
    --secondary-color: #c7efcf;   /* Tea Green */
    --accent-color: #fe5f55;      /* Bittersweet */
    --dark-color: #333745;        /* Gunmetal */
    --light-color: #FFFFFF;       /* White */
    --gray-color: #757575;        /* Medium gray */
    --light-gray: #eef5db;        /* Beige */
    --success-color: #c7efcf;     /* Tea Green */
    --warning-color: #fe5f55;     /* Bittersweet */
    --error-color: #e63462;       /* Cerise */
    --gradient-start: #e63462;    /* Cerise */
    --gradient-end: #fe5f55;      /* Bittersweet */
    --shadow-sm: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 5px 20px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 10px 30px rgba(0, 0, 0, 0.2);
    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 20px;
    --transition: all 0.3s ease;
}

/* Base styles */
body {
    background-color: var(--light-gray);
    font-family: 'Alata', sans-serif;
    color: var(--dark-color);
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

.app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Header section */
.header-section {
    background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
    color: var(--light-color);
    padding: 3rem 0 2rem;
    text-align: center;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-md);
}

.app-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    letter-spacing: -0.5px;
}

.highlight {
    color: var(--secondary-color);
}

.app-subtitle {
    font-size: 1.1rem;
    font-weight: 300;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Main content */
.main-content {
    flex: 1;
    padding-bottom: 3rem;
}

/* Content card */
.content-card {
    background-color: var(--light-color);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    margin-bottom: 2rem;
    transition: var(--transition);
}

.content-card:hover {
    box-shadow: var(--shadow-lg);
}

/* Form sections */
.form-section {
    margin-bottom: 2.5rem;
    position: relative;
}

.form-section:last-child {
    margin-bottom: 0;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
    display: flex;
    align-items: center;
}

.section-icon {
    margin-right: 0.5rem;
    opacity: 0.8;
}

/* Upload container */
.upload-container {
    position: relative;
    border: 2px dashed var(--primary-color);
    border-radius: var(--border-radius-sm);
    padding: 2rem;
    text-align: center;
    transition: var(--transition);
    background-color: rgba(98, 0, 234, 0.05);
    cursor: pointer;
}

.upload-container:hover {
    background-color: rgba(98, 0, 234, 0.1);
}

.upload-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    width: 100%;
    height: 100%;
    color: var(--primary-color);
}

.upload-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.upload-input {
    position: absolute;
    width: 0.1px;
    height: 0.1px;
    opacity: 0;
    overflow: hidden;
    z-index: -1;
}

/* Content textarea */
.content-textarea {
    min-height: 150px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius-sm);
    padding: 1rem;
    font-family: monospace;
    transition: var(--transition);
}

.content-textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(98, 0, 234, 0.2);
}

/* Sample links */
.sample-links {
    margin-top: 0.75rem;
    text-align: right;
    font-size: 0.9rem;
}

.sample-link {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.sample-link:hover {
    color: var(--gradient-end);
    text-decoration: underline;
}

/* Form controls */
.form-control {
    border-radius: var(--border-radius-sm);
    padding: 0.75rem 1rem;
    border: 1px solid #ddd;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(98, 0, 234, 0.2);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

/* Color pickers */
.color-pickers {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-top: 1rem;
}

.color-picker-item {
    flex: 1;
    min-width: 150px;
}

.color-picker-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.color-picker {
    width: 3rem;
    height: 3rem;
    padding: 0;
    border: none;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    box-shadow: var(--shadow-sm);
}

.color-text {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius-sm);
    font-family: monospace;
    text-transform: uppercase;
}

/* Format options */
.format-options {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    justify-content: center;
}

.format-option {
    position: relative;
    width: 150px;
}

.format-checkbox {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.format-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    border-radius: var(--border-radius-sm);
    border: 2px solid transparent;
    background-color: #f9f9f9;
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
}

.format-checkbox:checked + .format-label {
    border-color: var(--primary-color);
    background-color: rgba(98, 0, 234, 0.05);
}

.format-preview {
    margin-bottom: 0.75rem;
    background-color: #ddd;
    border-radius: 4px;
}

.ratio-16-9 {
    width: 80px;
    height: 45px;
}

.ratio-9-16 {
    width: 45px;
    height: 80px;
}

.ratio-1-1 {
    width: 60px;
    height: 60px;
}

.format-label span {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.format-label small {
    color: var(--gray-color);
    font-size: 0.8rem;
}

/* Generate button */
.generate-btn {
    background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
    color: var(--light-color);
    border: none;
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-md);
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.generate-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--gradient-start), var(--primary-color));
}

.generate-btn:active {
    transform: translateY(1px);
}

/* Results card */
.results-card {
    background-color: var(--light-color);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: var(--transition);
}

.results-header {
    background-color: var(--success-color);
    color: var(--light-color);
    padding: 1.25rem 1.5rem;
}

.results-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.results-body {
    padding: 1.5rem;
}

/* Video result cards */
.video-card {
    background-color: #f9f9f9;
    border-radius: var(--border-radius-sm);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
}

.video-card:last-child {
    margin-bottom: 0;
}

.video-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-sm);
}

.video-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--primary-color);
}

.video-info {
    color: var(--gray-color);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.video-preview {
    width: 100%;
    border-radius: var(--border-radius-sm);
    margin-bottom: 1rem;
    box-shadow: var(--shadow-sm);
}

.download-btn {
    background-color: var(--primary-color);
    color: var(--light-color);
    border: none;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 500;
    border-radius: 50px;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    align-self: flex-start;
}

.download-btn:hover {
    background-color: var(--gradient-end);
    transform: translateY(-2px);
}

/* Error alert */
.error-alert {
    background-color: rgba(255, 23, 68, 0.1);
    color: var(--error-color);
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

/* Footer */
.app-footer {
    background-color: var(--dark-color);
    color: var(--light-color);
    padding: 1.5rem 0;
    text-align: center;
    margin-top: auto;
}

.app-footer p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.8;
}

.app-footer .fa-heart {
    color: var(--accent-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .header-section {
        padding: 2rem 0 1.5rem;
    }

    .app-title {
        font-size: 2rem;
    }

    .content-card {
        padding: 1.5rem;
    }

    .color-pickers {
        flex-direction: column;
        gap: 1rem;
    }

    .format-options {
        gap: 1rem;
    }

    .format-option {
        width: 120px;
    }

    .generate-btn {
        width: 100%;
        padding: 0.75rem 1.5rem;
    }
}

/* Additional styles for drag and drop */
.upload-container.highlight {
    border-color: var(--secondary-color);
    background-color: rgba(3, 218, 198, 0.1);
}

.upload-container.file-selected {
    border-color: var(--success-color);
    background-color: rgba(0, 200, 83, 0.1);
}

.upload-container.file-selected .upload-icon {
    color: var(--success-color);
}

/* Loading state */
.form-section.loading {
    opacity: 0.7;
    pointer-events: none;
}
