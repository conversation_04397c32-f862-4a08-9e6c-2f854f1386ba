/**
 * ScrollVideo Creator - Main JavaScript
 * Handles form submission, validation, and results display with modern UI interactions
 */

document.addEventListener('DOMContentLoaded', function() {
    // Form elements
    const videoForm = document.getElementById('videoForm');
    const generateBtn = document.getElementById('generateBtn');
    const generateSpinner = document.getElementById('generateSpinner');
    const resultsCard = document.getElementById('resultsCard');
    const videoResults = document.getElementById('videoResults');
    const errorAlert = document.getElementById('errorAlert');
    const errorMessage = document.getElementById('errorMessage');
    
    // Color input elements
    const titleColor = document.getElementById('titleColor');
    const titleColorHex = document.getElementById('titleColorHex');
    const bgColor = document.getElementById('bgColor');
    const bgColorHex = document.getElementById('bgColorHex');
    const textColor = document.getElementById('textColor');
    const textColorHex = document.getElementById('textColorHex');
    
    // HTML input elements
    const htmlFile = document.getElementById('htmlFile');
    const htmlContent = document.getElementById('htmlContent');
    const loadSampleBtn = document.getElementById('loadSampleBtn');
    const durationInput = document.getElementById('duration');
    
    // Upload container
    const uploadContainer = document.querySelector('.upload-container');
    
    // Add drag and drop functionality
    if (uploadContainer) {
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadContainer.addEventListener(eventName, preventDefaults, false);
        });
        
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        ['dragenter', 'dragover'].forEach(eventName => {
            uploadContainer.addEventListener(eventName, highlight, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            uploadContainer.addEventListener(eventName, unhighlight, false);
        });
        
        function highlight() {
            uploadContainer.classList.add('highlight');
        }
        
        function unhighlight() {
            uploadContainer.classList.remove('highlight');
        }
        
        uploadContainer.addEventListener('drop', handleDrop, false);
        
        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            
            if (files.length > 0) {
                htmlFile.files = files;
                // Trigger change event
                const event = new Event('change');
                htmlFile.dispatchEvent(event);
            }
        }
    }
    
    // Sync color inputs with hex text inputs
    titleColor.addEventListener('input', () => {
        titleColorHex.value = titleColor.value;
    });
    
    titleColorHex.addEventListener('input', () => {
        if (isValidHexColor(titleColorHex.value)) {
            titleColor.value = titleColorHex.value;
        }
    });
    
    bgColor.addEventListener('input', () => {
        bgColorHex.value = bgColor.value;
    });
    
    bgColorHex.addEventListener('input', () => {
        if (isValidHexColor(bgColorHex.value)) {
            bgColor.value = bgColorHex.value;
        }
    });
    
    textColor.addEventListener('input', () => {
        textColorHex.value = textColor.value;
    });
    
    textColorHex.addEventListener('input', () => {
        if (isValidHexColor(textColorHex.value)) {
            textColor.value = textColorHex.value;
        }
    });
    
    // Validate duration input
    durationInput.addEventListener('input', () => {
        const value = parseInt(durationInput.value);
        if (isNaN(value) || value < 10) {
            durationInput.value = 10;
        } else if (value > 300) {
            durationInput.value = 300;
        }
    });
    
    // Handle file input change with visual feedback
    htmlFile.addEventListener('change', () => {
        if (htmlFile.files.length > 0) {
            // Clear textarea if file is selected
            htmlContent.value = '';
            htmlContent.disabled = true;
            
            // Update upload label to show filename
            const fileNameSpan = uploadContainer.querySelector('span');
            if (fileNameSpan) {
                fileNameSpan.textContent = `Selected: ${htmlFile.files[0].name}`;
            }
            
            // Add success class
            uploadContainer.classList.add('file-selected');
        } else {
            htmlContent.disabled = false;
            
            // Reset upload label
            const fileNameSpan = uploadContainer.querySelector('span');
            if (fileNameSpan) {
                fileNameSpan.textContent = 'Drop HTML file here or click to browse';
            }
            
            // Remove success class
            uploadContainer.classList.remove('file-selected');
        }
    });
    
    // Handle textarea input
    htmlContent.addEventListener('input', () => {
        if (htmlContent.value.trim() !== '') {
            // Clear file input if textarea has content
            htmlFile.value = '';
            
            // Reset upload label
            const fileNameSpan = uploadContainer.querySelector('span');
            if (fileNameSpan) {
                fileNameSpan.textContent = 'Drop HTML file here or click to browse';
            }
            
            // Remove success class
            uploadContainer.classList.remove('file-selected');
        }
    });
    
    // Handle load sample button click with improved feedback
    loadSampleBtn.addEventListener('click', (e) => {
        e.preventDefault();
        
        // Show loading state for the button
        const originalText = loadSampleBtn.textContent;
        loadSampleBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
        loadSampleBtn.disabled = true;
        
        // Fetch the sample HTML content
        fetch('/sample')
            .then(response => response.text())
            .then(html => {
                // Populate the textarea with the sample content
                htmlContent.value = html;
                
                // Clear any file input
                htmlFile.value = '';
                
                // Reset upload label
                const fileNameSpan = uploadContainer.querySelector('span');
                if (fileNameSpan) {
                    fileNameSpan.textContent = 'Drop HTML file here or click to browse';
                }
                uploadContainer.classList.remove('file-selected');
                
                // Set a default title
                document.getElementById('title').value = 'Sample Mathematics Question Paper';
                
                // Show success feedback
                loadSampleBtn.innerHTML = '<i class="fas fa-check"></i> Loaded!';
                
                // Reset button state after delay
                setTimeout(() => {
                    loadSampleBtn.innerHTML = originalText;
                    loadSampleBtn.disabled = false;
                }, 1500);
            })
            .catch(error => {
                console.error('Error loading sample content:', error);
                loadSampleBtn.innerHTML = '<i class="fas fa-exclamation-circle"></i> Error';
                setTimeout(() => {
                    loadSampleBtn.innerHTML = originalText;
                    loadSampleBtn.disabled = false;
                }, 1500);
            });
    });
    
    // Form submission with improved feedback
    videoForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Validate form
        if (!validateForm()) {
            return;
        }
        
        // Show loading state
        setLoading(true);
        
        // Hide previous results and errors
        resultsCard.classList.add('d-none');
        errorAlert.classList.add('d-none');
        
        // Create FormData object
        const formData = new FormData(videoForm);
        
        // Send AJAX request
        fetch('/api/videos/generate', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.detail || 'Server error occurred');
                });
            }
            return response.json();
        })
        .then(data => {
            // Handle successful response
            displayResults(data);
            setLoading(false);
            
            // Show success notification
            showNotification('Videos generated successfully!', 'success');
        })
        .catch(error => {
            // Handle error
            showError(error.message);
            setLoading(false);
        });
    });
    
    /**
     * Validate the form inputs
     * @returns {boolean} Whether the form is valid
     */
    function validateForm() {
        // Reset error state
        errorAlert.classList.add('d-none');
        
        // Check if either file or textarea has content
        if (htmlFile.files.length === 0 && htmlContent.value.trim() === '') {
            showError('Please upload an HTML file or paste HTML content');
            return false;
        }
        
        // Check if at least one aspect ratio is selected
        const aspectRatios = document.querySelectorAll('input[name="aspect_ratios"]:checked');
        if (aspectRatios.length === 0) {
            showError('Please select at least one video format');
            return false;
        }
        
        // Check if title is provided
        const title = document.getElementById('title').value.trim();
        if (title === '') {
            showError('Please enter a title for the video');
            return false;
        }
        
        // Validate duration
        const duration = parseInt(durationInput.value);
        if (isNaN(duration) || duration < 10 || duration > 300) {
            showError('Duration must be between 10 and 300 seconds');
            return false;
        }
        
        return true;
    }
    
    /**
     * Display the generated video results with improved styling
     * @param {Object} data - Response data from the server
     */
    function displayResults(data) {
        // Clear previous results
        videoResults.innerHTML = '';
        
        // Check if videos were generated
        if (!data.videos || Object.keys(data.videos).length === 0) {
            showError('No videos were generated. Please try again.');
            return;
        }
        
        // Create result cards for each video
        for (const [ratio, url] of Object.entries(data.videos)) {
            const card = document.createElement('div');
            card.className = 'video-card';
            
            let ratioName = '';
            let ratioIcon = '';
            switch (ratio) {
                case '16:9':
                    ratioName = 'Landscape (YouTube)';
                    ratioIcon = '<i class="fas fa-desktop"></i>';
                    break;
                case '9:16':
                    ratioName = 'Portrait (Shorts, Reels, TikTok)';
                    ratioIcon = '<i class="fas fa-mobile-alt"></i>';
                    break;
                case '1:1':
                    ratioName = 'Square (Instagram, Facebook)';
                    ratioIcon = '<i class="fas fa-square"></i>';
                    break;
                default:
                    ratioName = ratio;
                    ratioIcon = '<i class="fas fa-film"></i>';
            }
            
            card.innerHTML = `
                <div class="video-title">${ratioIcon} ${ratioName}</div>
                <div class="video-info">Aspect Ratio: ${ratio}</div>
                <video class="video-preview" controls>
                    <source src="${url}" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
                <a href="${url}" download class="download-btn">
                    <i class="fas fa-download"></i> Download Video
                </a>
            `;
            
            videoResults.appendChild(card);
        }
        
        // Show results card
        resultsCard.classList.remove('d-none');
        
        // Scroll to results
        resultsCard.scrollIntoView({ behavior: 'smooth' });
    }
    
    /**
     * Show an error message
     * @param {string} message - Error message to display
     */
    function showError(message) {
        errorMessage.textContent = message;
        errorAlert.classList.remove('d-none');
        errorAlert.scrollIntoView({ behavior: 'smooth' });
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            errorAlert.classList.add('d-none');
        }, 5000);
    }
    
    /**
     * Show a notification
     * @param {string} message - Notification message
     * @param {string} type - Notification type (success, warning, error)
     */
    function showNotification(message, type = 'info') {
        // Create notification element if it doesn't exist
        let notification = document.getElementById('notification');
        if (!notification) {
            notification = document.createElement('div');
            notification.id = 'notification';
            document.body.appendChild(notification);
            
            // Add styles if not in CSS
            notification.style.position = 'fixed';
            notification.style.bottom = '20px';
            notification.style.right = '20px';
            notification.style.padding = '15px 20px';
            notification.style.borderRadius = '8px';
            notification.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
            notification.style.zIndex = '9999';
            notification.style.transition = 'all 0.3s ease';
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(20px)';
        }
        
        // Set type-specific styles
        switch (type) {
            case 'success':
                notification.style.backgroundColor = '#00C853';
                notification.style.color = 'white';
                notification.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
                break;
            case 'warning':
                notification.style.backgroundColor = '#FFD600';
                notification.style.color = '#333';
                notification.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
                break;
            case 'error':
                notification.style.backgroundColor = '#FF1744';
                notification.style.color = 'white';
                notification.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
                break;
            default:
                notification.style.backgroundColor = '#2196F3';
                notification.style.color = 'white';
                notification.innerHTML = `<i class="fas fa-info-circle"></i> ${message}`;
        }
        
        // Show notification
        notification.style.opacity = '1';
        notification.style.transform = 'translateY(0)';
        
        // Hide after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(20px)';
            
            // Remove from DOM after animation
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
    
    /**
     * Set the loading state of the form with improved visual feedback
     * @param {boolean} isLoading - Whether the form is in loading state
     */
    function setLoading(isLoading) {
        if (isLoading) {
            generateBtn.disabled = true;
            generateSpinner.classList.remove('d-none');
            generateBtn.innerHTML = `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Creating Magic...`;
            
            // Add loading class to form
            videoForm.classList.add('loading');
        } else {
            generateBtn.disabled = false;
            generateSpinner.classList.add('d-none');
            generateBtn.innerHTML = `<i class="fas fa-magic me-2"></i> Generate Videos`;
            
            // Remove loading class
            videoForm.classList.remove('loading');
        }
    }
    
    /**
     * Check if a string is a valid hex color
     * @param {string} hex - Hex color string to validate
     * @returns {boolean} Whether the string is a valid hex color
     */
    function isValidHexColor(hex) {
        return /^#([0-9A-F]{3}){1,2}$/i.test(hex);
    }
    
    // Add CSS for file-selected state if not in stylesheet
    const style = document.createElement('style');
    style.textContent = `
        .upload-container.highlight {
            border-color: var(--secondary-color);
            background-color: rgba(3, 218, 198, 0.1);
        }
        .upload-container.file-selected {
            border-color: var(--success-color);
            background-color: rgba(0, 200, 83, 0.1);
        }
        .upload-container.file-selected .upload-icon {
            color: var(--success-color);
        }
        .form-section.loading {
            opacity: 0.7;
            pointer-events: none;
        }
    `;
    document.head.appendChild(style);
});
