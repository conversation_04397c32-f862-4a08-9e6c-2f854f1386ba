"""
Video models for the application.
"""

from typing import Dict, List, Optional
from pydantic import BaseModel, Field, validator


class VideoRequest(BaseModel):
    """Request model for video generation."""
    title: str = Field(..., description="Video title")
    bg_color: str = Field("#FFFFFF", description="Background color in hex format")
    title_color: str = Field("#000000", description="Title color in hex format")
    text_color: str = Field("#000000", description="Text color in hex format")
    duration: int = Field(60, description="Video duration in seconds", ge=10, le=300)
    aspect_ratios: List[str] = Field(
        ["16:9"], 
        description="List of aspect ratios to generate"
    )
    html_content: Optional[str] = Field(None, description="HTML content as text")
    
    @validator('bg_color', 'title_color', 'text_color')
    def validate_color(cls, v):
        """Validate color hex code."""
        if not v.startswith('#') or len(v) not in [4, 7]:
            raise ValueError('Color must be a valid hex code (e.g., #FFF or #FFFFFF)')
        return v
    
    @validator('aspect_ratios')
    def validate_aspect_ratios(cls, v):
        """Validate aspect ratios."""
        valid_ratios = ["16:9", "9:16", "1:1"]
        for ratio in v:
            if ratio not in valid_ratios:
                raise ValueError(f"Invalid aspect ratio: {ratio}. Must be one of {valid_ratios}")
        return v


class VideoResponse(BaseModel):
    """Response model for video generation."""
    success: bool = Field(..., description="Whether the operation was successful")
    videos: Dict[str, Optional[str]] = Field(
        ..., 
        description="Dictionary mapping aspect ratios to video URLs"
    )
