from langchain_huggingface import HuggingFaceEndpoint


class HuggingFaceLLM:
    def __init__(self, api_token, repo_id="Qwen/Qwen2.5-Coder-32B-Instruct", max_new_tokens=512):
        self.api_token = api_token
        self.repo_id = repo_id
        self.max_new_tokens = max_new_tokens

    def get_llm(self, llm_model):
        return HuggingFaceEndpoint(
            repo_id=llm_model if llm_model else self.repo_id,
            huggingfacehub_api_token=self.api_token
        )
