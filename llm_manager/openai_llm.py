from langchain_openai import ChatOpenAI
from config import llm_config


class OpenAILLM:
    def __init__(self, api_key, model="gpt-4.1-mini", temperature=0):
        self.api_key = api_key
        self.model = model
        self.temperature = temperature
        self.timeout = 360

    def get_llm(self, llm_model):
        from llm_manager.llm_factory import LLMFactory
        llm_factory = LLMFactory(llm_config)

        return ChatOpenAI(
            model=llm_model if llm_model else self.model,
            temperature=self.temperature,
            openai_api_key=self.api_key,
            timeout=self.timeout,
            max_retries=1
        )

