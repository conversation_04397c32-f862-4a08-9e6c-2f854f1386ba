from langchain_anthropic import ChatAnthropic


class AnthropicLLM:
    def __init__(self, api_key, model="claude-3-5-sonnet-20240620", temperature=0, max_tokens=1024):
        self.api_key = api_key
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens

    def get_llm(self, llm_model):
        return ChatAnthropic(
            model=llm_model if llm_model else self.model,
            temperature=self.temperature,
            max_tokens=self.max_tokens,
            api_key=self.api_key
        )
