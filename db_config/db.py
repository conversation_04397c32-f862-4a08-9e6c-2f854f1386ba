from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.exc import SQLAlchemyError
from db_config.env_loader import env_loader
import logging
import sys

logger = logging.getLogger(__name__)

# Schema to use for authentication and role-based access control
AUTH_SCHEMA = "wsuser"
SHOP_SCHEMA = "wsshop"
COMM_SCHEMA = "wscomm"
LOG_SCHEMA = "wslog"
CONTENT_SCHEMA = "wscontent"

# Dictionary to store engines for different schemas
engines = {}

# Dictionary to store session factories for different schemas
sessions = {}

# Base class for declarative models
Base = declarative_base()

def init_db_connections():
    """
    Initialize database connections for all schemas
    """
    # Test connection to the auth schema first
    if not env_loader.test_connection(AUTH_SCHEMA):
        logger.error(f"Failed to connect to authentication schema: {AUTH_SCHEMA}")
        logger.error("Application cannot start without database connection")

    # Test connections to other important schemas
    for schema in [SHOP_SCHEMA, COMM_SCHEMA, LOG_SCHEMA]:
        if not env_loader.test_connection(schema):
            logger.warning(f"Failed to connect to schema: {schema}")
            logger.warning(f"Some functionality may not work without {schema} schema connection")

    # Get all available schemas from the configuration
    schemas = []
    if env_loader._schemas_config and 'dataSources' in env_loader._schemas_config:
        schemas = list(env_loader._schemas_config['dataSources'].keys())

    # Initialize connections for all schemas
    for schema in schemas:
        try:
            # Get database URL for this schema
            database_url = env_loader.get_database_url(schema)
            logger.info(f"Database URL for schema {schema}: {database_url}")
            if not database_url:
                logger.warning(f"Skipping schema {schema}: invalid database URL")
                continue

            # Create engine
            engine = create_engine(
                database_url,
                pool_pre_ping=True,
                pool_recycle=3600,  # Recycle connections after 1 hour
                pool_size=20,        # Maximum number of connections in the pool
                max_overflow=50      # Maximum number of connections that can be created beyond pool_size
            )

            # Add engine ping listener to verify connections
            @event.listens_for(engine, "engine_connect")
            def ping_connection(connection, branch):
                if branch:
                    return
                try:
                    from sqlalchemy import text
                    connection.scalar(text("SELECT 1"))
                except Exception as e:
                    logger.warning(f"Connection for schema {schema} was invalid. Reconnecting... Error: {str(e)}")
                    connection.close()
                    raise

            # Create session factory
            session_factory = sessionmaker(bind=engine, autocommit=False, autoflush=False)

            # Store engine and session factory
            engines[schema] = engine
            sessions[schema] = session_factory

            logger.info(f"Successfully initialized connection for schema: {schema}")

        except SQLAlchemyError as e:
            logger.error(f"Failed to initialize connection for schema {schema}: {str(e)}")

# Initialize database connections
init_db_connections()

# Default engine and session for backward compatibility
engine = engines.get(AUTH_SCHEMA)
SessionLocal = sessions.get(AUTH_SCHEMA)

def get_engine(schema=AUTH_SCHEMA):
    """
    Get a database engine for a specific schema
    """
    engine = engines.get(schema)
    if not engine:
        logger.error(f"No engine found for schema: {schema}")
        raise ValueError(f"No engine found for schema: {schema}")

    return engine

def get_session(schema=AUTH_SCHEMA):
    """
    Get a database session for a specific schema

    This is a generator function that yields a session and ensures it's closed
    when the caller is done with it. It should be used with the `next()` function
    or in a `with` statement via a context manager.

    For better connection management, consider using the db_session context manager
    from utils.db_context instead.
    """
    session_factory = sessions.get(schema)
    if not session_factory:
        logger.error(f"No session factory found for schema: {schema}")
        raise ValueError(f"No session factory found for schema: {schema}")

    session = session_factory()
    try:
        yield session
    except Exception as e:
        logger.error(f"Error in database session: {e}")
        session.rollback()
        raise
    finally:
        logger.debug(f"Closing database session for schema: {schema}")
        session.close()
