# File: agents/schemas/response_schemas.py
from typing import List, Dict, Optional, Any, Union
from pydantic import BaseModel, Field


class QuestionOption(BaseModel):
    """Schema for a question option"""
    text: str
    identifier: str  # e.g., "A", "1", "(a)", etc.


class Question(BaseModel):
    """Schema for a question"""
    question_number: int
    text: str
    options: List[QuestionOption]
    batch_index: int


class Answer(BaseModel):
    """Schema for an answer"""
    question_number: int
    correct_answer: str
    batch_index: int


class Explanation(BaseModel):
    """Schema for an explanation"""
    question_number: int
    explanation: str
    batch_index: int


class ContentMapping(BaseModel):
    """Schema for content type mapping"""
    start: Optional[int] = None
    end: Optional[int] = None
    count: int = 0


class PageData(BaseModel):
    """Schema for individual page data"""
    is_question: bool = False
    is_answer_key: bool = False
    is_explanation: bool = False
    questions: ContentMapping = Field(default_factory=ContentMapping)
    answers: ContentMapping = Field(default_factory=ContentMapping)
    explanations: ContentMapping = Field(default_factory=ContentMapping)
    is_overflow: bool = False
    overflow_page: Optional[int] = None


class PageMappingMetadata(BaseModel):
    """Schema for page mapping metadata"""
    total_pages: int
    total_questions: int
    question_pages: List[int] = Field(default_factory=list)
    answer_key_pages: List[int] = Field(default_factory=list)
    explanation_pages: List[int] = Field(default_factory=list)


class PageMapping(BaseModel):
    """Schema for mapping content to image indices"""
    questions: Dict[str, List[int]] = Field(
        ...,
        description="Maps question number ranges to image indices, e.g. {'1-10': [0, 1], '11-20': [2, 3]}"
    )
    answer_key: List[int] = Field(
        ...,
        description="Image indices containing the answer key"
    )
    explanations: Dict[str, List[int]] = Field(
        default_factory=dict,
        description="Maps explanation number ranges to image indices, e.g. {'1-10': [10, 11]}"
    )


class FormatPattern(BaseModel):
    """Schema for format patterns"""
    directions: Dict = Field(..., description="Direction formatting pattern")
    question_pattern: Dict = Field(..., description="Question formatting pattern")
    options_pattern: Dict = Field(..., description="Options formatting pattern")
    answer_key_pattern: Dict = Field(..., description="Answer key formatting pattern")
    explanation_pattern: Dict = Field(..., description="Explanation formatting pattern")
    page_mapping: Optional[PageMapping] = Field(None, description="Mapping of content to image indices")


class ValidationFeedback(BaseModel):
    """Schema for validation feedback"""
    missing_items: List[int] = Field(default_factory=list)
    format_issues: List[Dict] = Field(default_factory=list)
    structure_issues: List[Dict] = Field(default_factory=list)


class MappedQuestion(BaseModel):
    """Schema for a mapped question with answer and explanation"""
    question_number: int
    question_text: str
    options: List[QuestionOption]
    correct_answer: str
    explanation: Optional[str] = None


class PageMappingResponse(BaseModel):
    """Response model for Page Mapper"""
    status: str
    page_mapping: Dict[str, PageData]
    metadata: PageMappingMetadata
    message: Optional[str] = None


# Response Models
class FormatIdentifierResponse(BaseModel):
    """Response model for Format Identifier"""
    status: str
    format: FormatPattern
    metadata: Dict[str, Any]
    page_mapping: Optional[Dict[str, PageData]] = None
    page_metadata: Optional[PageMappingMetadata] = None


class ExtractorResponse(BaseModel):
    """Base response model for extractors"""
    status: str
    batch_number: int
    data: List[Union[Question, Answer, Explanation]]


class ValidationResponse(BaseModel):
    """Response model for validator"""
    status: str
    is_valid: bool
    batch_number: int
    feedback: Optional[ValidationFeedback] = None


class MapperResponse(BaseModel):
    """Response model for mapper"""
    status: str
    batch_number: int
    mapped_data: List[MappedQuestion]


class AggregatorResponse(BaseModel):
    """Response model for final aggregated response"""
    status: str
    total_questions: int
    completed_questions: int
    content: List[MappedQuestion]
