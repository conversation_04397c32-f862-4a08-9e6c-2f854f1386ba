import logging
import os
import json
from json import JSONDecodeE<PERSON><PERSON>
from typing import Dict, List, Any, <PERSON>ple
import re
import config
import uuid
from agents.core.validators.answer_key_validator import Answer<PERSON>eyValidator
from agents.utils.pdf_helpers import PDFImageConverter, extract_page_number, extract_cropped_page_number, \
    extract_col_number
from agents.core.extractor import ExtractorAgent
from utils.open_utils import convert_usd_to_inr
import shutil
from pathlib import Path
from langchain_core.messages import HumanMessage
from app.content_extractor import ContentExtractor
from llm_manager.llm_factory import LLMFactory
from agents.utils.image_utils import encode_image_to_base64
from agents.utils.helpers import calculate_cost
from config import llm_config
from utils.s3_utils import upload_file_to_s3, get_s3_path
from utils.timing_tracker import timing_tracker
from utils.timing_decorators import time_workflow, time_stage

llm_factory = LLMFactory(llm_config)
logger = logging.getLogger(__name__)

# Re-usable patterns (keep these as they are)
question_start_pattern = re.compile(r'^(\d+)\.\s*(.*)')
option_pattern_multi = re.compile(r'\(\s*(\d+)\s*\)\s*(.*?)(?=\s*\(\s*\d+\s*\)\s*|$)')
# Pattern to match option numbers at the beginning of a line (for LaTeX content)
option_pattern_line_start = re.compile(r'^\s*\(\s*(\d+)\s*\)\s*(.*)$')
direction_start_pattern_line = re.compile(r'^Direction(?:s)?(?:\s*\(\s*\d+\s*-\s*\d+\s*\))?\s*:?', re.IGNORECASE)
passage_start_pattern_line = re.compile(r'^PASSAGE\s*$', re.IGNORECASE)


class TaskManager:
    """
    Manages the entire workflow of extracting MCQ content from a PDF.
    """

    def __init__(self, progress_tracker=None, task_id=None):
        """
        Initialize the TaskManager.

        Args:
            progress_tracker: Optional progress tracker instance
            task_id: Optional task ID for progress tracking
        """
        self.progress_tracker = progress_tracker
        self.task_id = task_id

    def set_progress_tracker(self, progress_tracker, task_id):
        """
        Set the progress tracker after initialization.

        Args:
            progress_tracker: Progress tracker instance
            task_id: Task ID for progress tracking
        """
        logger.info(f"Setting progress tracker for task {task_id}")
        self.progress_tracker = progress_tracker
        self.task_id = task_id

        # Send an initial update to confirm the tracker is working
        if self.progress_tracker and self.task_id:
            self.track_progress("Progress tracker initialized", 10)

    def track_progress(self, step, progress=None):
        """
        Track progress if a progress tracker is available.

        Args:
            step: Current step description
            progress: Optional progress percentage (0-100)
        """
        # Always log the step
        logger.info(f"TaskManager progress update: {step} ({progress if progress else 'N/A'}%)")

        # If progress tracker is available, add the step
        if self.progress_tracker and self.task_id:
            logger.info(f"Sending progress update to tracker for task {self.task_id}: {step}")
            try:
                # Force a small delay to ensure updates are processed separately
                import time
                time.sleep(0.1)  # 100ms delay to separate updates

                # Add the step to the progress tracker
                self.progress_tracker.add_step(self.task_id, step, progress)

                # Verify the step was added by checking the task
                task = self.progress_tracker.get_task(self.task_id)
                if task and task.get('steps'):
                    # Check if the step is in the task's steps
                    step_found = False
                    for step_obj in task.get('steps', []):
                        if step_obj.get('step') == step:
                            step_found = True
                            break

                    if step_found:
                        logger.info(f"Step '{step}' successfully added to task {self.task_id}")
                    else:
                        logger.warning(f"Step '{step}' not found in task {self.task_id} after adding")
                else:
                    logger.warning(f"No steps found in task {self.task_id} after adding step '{step}'")
            except Exception as e:
                logger.error(f"Error sending progress update: {e}")
        else:
            logger.warning(f"No progress tracker available for step: {step}")

    @time_workflow("PDF Extraction Workflow")
    async def process_pdf_and_extract_content(self, pdf_path: str, book_id: str, chapter_id: str, res_id: str, workflow_id: str = None) -> Dict:
        """
        Converts a PDF into images, uploads them, and processes content extraction.

        Args:
            pdf_path (str): Path to the PDF file.
            book_id (str): Book identifier.
            chapter_id (str): Book identifier.
            res_id (str): Book identifier.

        Returns:
            Dict: Final structured response containing extracted MCQ content.
        """
        try:
            logger.info(f"Converting PDF to images for Book ID: {book_id}...")
            self.track_progress("Converting PDF to images", 35)

            # Step 1: Convert PDF to images and upload
            timing_tracker.start_stage(workflow_id, "Convert PDF to Images")
            pdf_converter = PDFImageConverter(progress_tracker=self.progress_tracker, task_id=self.task_id)
            conversion_result = pdf_converter.convert_and_upload(pdf_path=pdf_path, book_id=book_id, chapter_id=chapter_id, res_id=res_id)
            timing_tracker.end_stage(workflow_id, "Convert PDF to Images")

            logger.info(f"PDF Conversion Result: {repr(conversion_result)}")
            self.track_progress("PDF conversion completed", 40)

            if conversion_result["status"] != "success":
                self.track_progress(f"Error: PDF processing failed - {conversion_result['message']}")
                return {"status": "error", "message": f"PDF processing failed: {conversion_result['message']}"}

            # Step 2: Fetch image URLs after upload
            full_img_urls = sorted(conversion_result["image_urls"], key=extract_page_number)
            cropped_img_urls = sorted(conversion_result["cropped_image_urls"], key=extract_cropped_page_number)
            logger.info(f"Retrieved {len(full_img_urls)} full image URLs for processing...")
            logger.info(f"Retrieved {len(cropped_img_urls)} cropped image URLs for processing...")
            self.track_progress(f"Retrieved {len(full_img_urls)} images for processing", 45)

            # Step 2.1: Initialize flags
            is_mcq = False
            is_ans_key = False
            is_exp = False
            found_explanation = False  # Flag to track if we've found an explanation page
            found_answer_key = False  # Flag to track if we've found an answer key page
            found_both_previous = False  # Flag to track if we found both in a previous iteration

            # Step 3: Initialize extractor
            logger.info("Initializing content extraction on uploaded images...")
            self.track_progress("Initializing content extraction", 50)
            timing_tracker.start_stage(workflow_id, "Initialize Extractor")
            extractor = ExtractorAgent(workflow_id=workflow_id)
            timing_tracker.end_stage(workflow_id, "Initialize Extractor")

            # Step 3.1: Initialize variables
            mcqs = []
            answer_keys = {}
            ans_key_str = ""
            explanations = []
            explanations_str = []
            total_cost = 0
            page_info_cost = 0
            question_cost = 0
            answer_key_cost = 0
            explanation_cost = 0
            ans_key_comp_cost = 0

            # Define path for storage
            img_storage_path = os.path.join(config.PDF_PAGE_IMG_OUTPUT_DIR, str(book_id), str(chapter_id), str(res_id))

            # Define a function to sort by page and column
            def sort_by_page_and_column(url):
                # Extract the filename from the path
                filename = url.split('/')[-1]
                # Remove extension
                name_without_ext = filename.split('.')[0]
                # Split by underscore to get parts
                parts = name_without_ext.split('_')
                # Extract page number (should be at index 1 in 'page_X_col_Y')
                page_num = int(parts[1])
                # Extract column number (should be at index 3 in 'page_X_col_Y')
                col_num = int(parts[3])
                # Return tuple for sorting (page first, then column)
                return (page_num, col_num)

            col_img_urls = []
            for idx, full_img_url in enumerate(full_img_urls):
                logger.info(f"Processing image: {full_img_url}")
                page_name = full_img_url.split('/')[-1]
                page_id = page_name.split('.')[0]

                for cropped_img_url in cropped_img_urls:
                    # Filter out any images with 'cropped_hires.png' in their names
                    if f"{page_id}_" in cropped_img_url and "cropped_hires.png" not in cropped_img_url:
                        col_img_urls.append(cropped_img_url)

            # Sort using the custom function that sorts by page first, then by column
            col_img_urls = sorted(col_img_urls, key=sort_by_page_and_column)

            # Step 4: Process each full page image
            total_pages = len(col_img_urls)
            self.track_progress(f"Found {total_pages} pages to process", 50)

            # Add a small delay to ensure updates are processed separately
            import asyncio
            await asyncio.sleep(0.5)

            # Check for last_updated_dtl.json to resume processing
            json_file_path = os.path.join(img_storage_path, "last_updated_dtl.json")
            start_idx = 0

            if os.path.exists(json_file_path):
                try:
                    with open(json_file_path, 'r') as f:
                        last_data = json.load(f)

                    if last_data.get("resId") == str(res_id):
                        last_page = last_data.get("last_updated_page")
                        logger.info(f"Found last processed page: {last_page}")

                        # Find the index of the last processed image
                        for i, img_url in enumerate(col_img_urls):
                            if img_url.endswith(last_page):
                                start_idx = i + 1  # Start from the next image
                                break

                        logger.info(f"Resuming from index {start_idx} out of {len(col_img_urls)}")
                        self.track_progress(f"Resuming from page {start_idx+1} of {total_pages}", 50)
                except Exception as e:
                    logger.error(f"Error reading last_updated_dtl.json: {e}")
                    start_idx = 0

            # Process images starting from start_idx
            for idx in range(start_idx, len(col_img_urls)):
                mcqs = []
                answer_keys = {}
                ans_key_str = ""
                explanations = []
                col_img_url = col_img_urls[idx]
                logger.info(f"Processing image: {col_img_url}")

                page_name = col_img_url.split('/')[-1]
                page_id = page_name.split('.')[0]

                logger.info(f"Processing page: {page_id}")
                progress = 50 + int((idx / total_pages) * 30)  # Progress from 50% to 80%
                self.track_progress(f"Processing page {idx+1} of {total_pages}", progress)

                # Add a small delay to ensure updates are processed separately
                await asyncio.sleep(0.5)

                timing_tracker.start_stage(workflow_id, f"Process Page {idx+1} - Page Info")
                page_info, cost = await extractor.process([col_img_url])
                timing_tracker.end_stage(workflow_id, f"Process Page {idx+1} - Page Info")
                page_info_cost += float(cost)
                total_cost += cost

                logger.info(f"Page Information for page: {page_id} is: {page_info}")

                # Get the current page's status
                current_page_has_exp = page_info['containsExplanations']
                current_page_has_ans = page_info['containsAnswers']

                # Logic for setting values
                if found_both_previous:
                    # In iterations after finding both explanation and answer key
                    is_exp = True
                    is_mcq = False
                    is_ans_key = False
                elif found_explanation:
                    # After finding explanation but before completing the iteration where both are found
                    is_exp = True
                    is_mcq = False
                    is_ans_key = current_page_has_ans  # Use actual value for answer key
                else:
                    # Before finding any explanation
                    is_exp = current_page_has_exp
                    is_mcq = page_info['containsMCQs']
                    is_ans_key = current_page_has_ans

                # Update our tracking flags at the end of this iteration
                if current_page_has_exp:
                    found_explanation = True

                if current_page_has_ans:
                    found_answer_key = True

                # Check if we've found both in this iteration
                found_both_current = found_explanation and found_answer_key

                logger.info(f"Has MCQ : {is_mcq}")
                logger.info(f"Has Answer key : {is_ans_key}")
                logger.info(f"Has Explanation : {is_exp}")

                # Update flag for next iteration
                found_both_previous = found_both_current

                # Extract MCQs if present
                if is_mcq:
                    parsed_question = []
                    self.track_progress("Extracting MCQs", progress)
                    timing_tracker.start_stage(workflow_id, f"Process Page {idx+1} - Extract MCQs")
                    mcq_result, mcq_cost = await extractor.extract_mcqs([col_img_url])
                    timing_tracker.end_stage(workflow_id, f"Process Page {idx+1} - Extract MCQs")
                    question_cost += mcq_cost
                    total_cost += mcq_cost
                    # Create the path to the extracted_data.txt file
                    extracted_data_file_path = os.path.join(img_storage_path, "extracted_data.txt")

                    if isinstance(mcq_result, str):
                        parsed_question = parse_questions(mcq_result, extracted_data_file_path)
                    elif isinstance(mcq_result, dict) and mcq_result.get("status") == "success":
                        extracted_mcqs = mcq_result.get("data", "")
                        parsed_question = parse_questions(extracted_mcqs, extracted_data_file_path)

                    validation_results = self.validate_content_with_images_mcq(parsed_question, [col_img_url])

                    for result in validation_results:
                            mcqs.append(result)
                    logger.info(f"Validation Results: {validation_results}")

                    mcq_full_img_storage_path = os.path.join(config.PDF_PAGE_IMG_OUTPUT_DIR, str(book_id), str(chapter_id), str(res_id))
                    mcq_local_image_paths = [
                        os.path.join(mcq_full_img_storage_path, img)
                        for img in os.listdir(mcq_full_img_storage_path)
                        if img.lower().endswith(f"{page_id}.png")
                    ]
                    mcq_local_image_paths.sort(key=lambda x: natural_sort_key(os.path.basename(x)))
                    logger.info(f"MCQ array before Image Extraction: {mcqs}")
                    logger.info(f"Extracting mcq images...")
                    extracted_img_urls = extractor.extract_quiz_images(mcq_local_image_paths, book_id, chapter_id, res_id, "mcq")
                    extractor.map_images_to_mcqs(mcqs, extracted_img_urls, "mcq")
                # Extract Answer Keys if present
                if is_ans_key:
                    self.track_progress("Extracting Answer Keys", progress)
                    timing_tracker.start_stage(workflow_id, f"Process Page {idx+1} - Extract Answer Keys")
                    answer_result, ans_cost = await extractor.extract_answer_key([col_img_url])
                    timing_tracker.end_stage(workflow_id, f"Process Page {idx+1} - Extract Answer Keys")
                    total_cost += ans_cost
                    answer_key_cost += ans_cost
                    ans_key_str += answer_result
                    if isinstance(answer_result, str):
                        cleaned_result = answer_result.strip().strip("```")
                        structured_answers = {str(index + 1): ans.strip() for index, ans in
                                              enumerate(cleaned_result.split("\n")) if ans.strip()}
                        answer_keys.update(structured_answers)
                    elif isinstance(answer_result, dict) and answer_result.get("status") == "success":
                        extracted_answers = answer_result.get("data", {})
                        answer_keys.update(extracted_answers)

                # Extract Explanations if present
                if is_exp:
                    parsed_explanation = []
                    self.track_progress("Extracting Explanations", progress)
                    timing_tracker.start_stage(workflow_id, f"Process Page {idx+1} - Extract Explanations")
                    explanation_result, exp_cost = await extractor.extract_explanation([col_img_url])
                    timing_tracker.end_stage(workflow_id, f"Process Page {idx+1} - Extract Explanations")
                    total_cost += exp_cost
                    explanation_cost += exp_cost
                    if isinstance(explanation_result, str):
                        parsed_explanation = parse_explanations(explanation_result)
                    elif isinstance(explanation_result, dict) and explanation_result.get("status") == "success":
                        extracted_explanations = explanation_result.get("data", "")
                        parsed_explanation = parse_explanations(extracted_explanations)

                    validation_results_explanation = self.validate_content_with_images_explanation(parsed_explanation,  [col_img_url])

                    for result in validation_results_explanation:
                        explanations.append(result)
                    logger.info(f"Validation Results: {validation_results_explanation}")
                    exp_full_img_storage_path = os.path.join(config.PDF_PAGE_IMG_OUTPUT_DIR, str(book_id), str(chapter_id), str(res_id))
                    exp_local_image_paths = [
                        os.path.join(exp_full_img_storage_path, img)
                        for img in os.listdir(exp_full_img_storage_path)
                        if img.lower().endswith(f"{page_id}.png")
                    ]
                    exp_local_image_paths.sort(key=lambda x: natural_sort_key(os.path.basename(x)))
                    logger.info(f"\nMCQ array before Image Extraction: {explanations}\n")
                    logger.info(f"\nExtracting mcq images...\n")
                    extracted_img_urls = extractor.extract_quiz_images(exp_local_image_paths, book_id, chapter_id, res_id, "explanation")
                    extractor.map_images_to_mcqs(explanations, extracted_img_urls, "explanation")

                # Update the last_updated_dtl.json file after processing this image
                try:
                    last_updated_data = {
                        "last_updated_page": page_name,
                        "resId": str(res_id)
                    }
                    with open(json_file_path, 'w') as f:
                        json.dump(last_updated_data, f)
                    logger.info(f"Updated last_updated_dtl.json with page: {page_name}")

                    # Update the extracted_data.txt file by appending new data
                    extracted_data_file_path = os.path.join(img_storage_path, "extracted_data.txt")

                    # Prepare the data to append - just the raw data without formatting
                    current_extracted_data = {
                        "mcqs": mcqs,
                        "answer_keys": answer_keys,
                        "explanations": explanations
                    }

                    # Convert to JSON string
                    data_to_append = json.dumps(current_extracted_data, indent=2)

                    # Append to the file
                    with open(extracted_data_file_path, 'a+') as f:
                        f.write(data_to_append + "\n")

                    logger.info(f"Appended raw data to extracted_data.txt for page {page_name}")
                except Exception as e:
                    logger.error(f"Error updating progress files: {e}")

            # Step 5: Prepare JSON for Image Extraction
            extracted_data = {
                "mcqs": mcqs,
                "answer_keys": answer_keys,
                "explanations": explanations
            }

            logger.info(f"Answer Key Str:{ans_key_str}")

            # Add a small delay to ensure updates are processed separately
            await asyncio.sleep(0.5)

            self.track_progress("Identifying MCQ pattern", 90)

            # Add a small delay to ensure updates are processed separately
            await asyncio.sleep(0.5)

            timing_tracker.start_stage(workflow_id, "Identify MCQ Pattern")
            mcq_pattern = await extractor.pattern_identifier(full_img_urls[:2])
            timing_tracker.end_stage(workflow_id, "Identify MCQ Pattern")

            self.track_progress("Calculating costs", 95)

            # Add a small delay to ensure updates are processed separately
            await asyncio.sleep(0.5)
            total_cost = convert_usd_to_inr(total_cost)
            page_info_cost = convert_usd_to_inr(page_info_cost)
            question_cost = convert_usd_to_inr(question_cost)
            explanation_cost = convert_usd_to_inr(explanation_cost)
            answer_key_cost = convert_usd_to_inr(answer_key_cost)

            logger.info(f"Total Cost: {total_cost}")
            logger.info(f"Page Info Cost: {page_info_cost}")
            logger.info(f"Question Cost: {question_cost}")
            logger.info(f"Explanation Cost: {explanation_cost}")
            logger.info(f"Answer Key Cost: {answer_key_cost}")

            self.track_progress("Validating Answer Keys", 95)

            # Add a small delay to ensure updates are processed separately
            await asyncio.sleep(0.5)
            extracted_data_file_path = os.path.join(img_storage_path, "extracted_data.txt")
            full_data = read_extracted_data(extracted_data_file_path)
            final_keys = convert_answer_keys_to_string(full_data["answer_keys"])

            timing_tracker.start_stage(workflow_id, "Validate Answer Keys")
            validator = AnswerKeyValidator(
                mcq_list=full_data["mcqs"],
                answer_key_string=final_keys,
                base_url=config.BASE_URL
            )
            mismatches = validator.validate_answers()
            timing_tracker.end_stage(workflow_id, "Validate Answer Keys")

            # Annotate each MCQ with answer_key_valid: True or False
            for mcq in full_data["mcqs"]:
                q_number = mcq.get("question_number")
                mcq["answer_key_valid"] = q_number not in mismatches

            logger.info(f"Mismatched Question Numbers: {mismatches}")
            self.track_progress("Completed Answer Key Validation", 96)
            # Prepare the final response with the path to the text file instead of the actual data
            final_response = {
                "status": "success",
                "mcqs": full_data["mcqs"],
                "answer_keys": final_keys,
                "explanations": full_data["explanations"],
                "wrong_answer_keys": mismatches,
                "pattern": mcq_pattern,
                "total_cost": total_cost,
                "page_info_cost": page_info_cost,
                "question_cost": question_cost,
                "explanation_cost": explanation_cost,
                "answer_key_cost": answer_key_cost,
                "chapter_id": chapter_id
            }

            # Save the output as a JSON file in the S3 path
            self.track_progress("Saving output.txt to S3", 98)
            timing_tracker.start_stage(workflow_id, "Save Output to S3")
            try:
                # Create a temporary file with the JSON content
                import tempfile

                # Create a serializable version of the response
                # Some objects might not be JSON serializable
                def make_serializable(obj):
                    if isinstance(obj, dict):
                        return {k: make_serializable(v) for k, v in obj.items()}
                    elif isinstance(obj, list):
                        return [make_serializable(item) for item in obj]
                    elif isinstance(obj, (int, float, str, bool, type(None))):
                        return obj
                    else:
                        return str(obj)

                serializable_response = make_serializable(final_response)

                with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json') as temp_file:
                    json.dump(serializable_response, temp_file, indent=2)
                    temp_path = temp_file.name

                logger.info(f"Created temporary JSON file at: {temp_path}")

                # Upload the file to S3
                s3_dir = f"supload/pdfextracts/{book_id}/{chapter_id}/{res_id}"
                logger.info(f"Uploading output.json to S3 directory: {s3_dir}")

                # Upload the file to S3 with the name 'output.json'
                upload_file_to_s3(
                    local_file_path=temp_path,
                    book_id=book_id,
                    chapter_id=chapter_id,
                    res_id=res_id,
                    file_name="output.json",
                    is_quiz_image=False  # This will put it in the main resId folder
                )

                # Clean up the temporary file
                os.unlink(temp_path)
                logger.info("Successfully saved extracted.txt to S3")

                # Now also upload the extracted_data.txt file to S3
                extracted_data_file_path = os.path.join(img_storage_path, "extracted_data.txt")
                if os.path.exists(extracted_data_file_path):
                    logger.info(f"Uploading extracted_data.txt to S3 directory: {s3_dir}")
                    upload_file_to_s3(
                        local_file_path=extracted_data_file_path,
                        book_id=book_id,
                        chapter_id=chapter_id,
                        res_id=res_id,
                        file_name="extracted_data.txt",
                        is_quiz_image=False  # This will put it in the main resId folder
                    )
                    logger.info("Successfully saved extracted_data.txt to S3")

                    # Delete the resource folder after successful upload to S3
                    logger.info(f"Attempting to delete resource folder for book_id: {book_id}, chapter_id: {chapter_id}, res_id: {res_id}")
                    if delete_folder_by_id(book_id, chapter_id, res_id):
                        logger.info(f"Successfully deleted resource folder for res_id: {res_id}")
                    else:
                        logger.warning(f"Failed to delete resource folder for res_id: {res_id}")
                else:
                    logger.warning(f"extracted_data.txt file not found at {extracted_data_file_path}")
                timing_tracker.end_stage(workflow_id, "Save Output to S3")
            except Exception as e:
                logger.error(f"Error saving output.json to S3: {e}")
                timing_tracker.end_stage(workflow_id, "Save Output to S3")

            self.track_progress("Extraction and S3 upload completed", 100)

            return final_response

        except Exception as e:
            logger.error(f"\nProcessing failed: {str(e)}", exc_info=True)
            return {"status": "error", "message": str(e)}

    def validate_content_with_images_mcq(self, content_array: List[Any], image_paths: List[str]) -> List[Dict]:
        """
        Validates content against images using OpenAI's vision model.

        For each content item, sends it along with all images to the vision model to:
        1. Compare the text content with what's visible in the images
        2. Determine if they match exactly
        3. If they don't match, fix the content to match the images exactly

        Args:
            content_array (List[Any]): Array of content items to validate
            image_paths (List[str]): Array of image paths to compare against

        Returns:
            List[Dict]: List of validated content items with validation status
        """
        logger.info(f"Validating {len(content_array)} content items against {len(image_paths)} images")

        # Initialize the LLM
        llm = llm_factory.get_llm("openai_admin", "gpt-4.1-mini")
        validated_content = []
        total_cost = 0

        # Process each content item
        for idx, content_item in enumerate(content_array):
            logger.info(f"Processing content item {idx+1}/{len(content_array)}")

            # Convert content item to string if it's not already
            content_text = str(content_item) if not isinstance(content_item, str) else content_item
            logger.info(f"Content to validate:\n{content_text}")

            # Construct prompt for the vision model
            prompt = f"""
            You are a validation agent.

            Your task is to:
            - ONLY validate and correct text if there is a clear mismatch between the image and the extracted text.
            - NEVER rephrase, rewrite, simplify, or beautify anything.

            You will be given:
            - An image (not shown here, already analyzed).
            - Extracted text from that image in JSON format.

            Validation Rules:
            1. Compare the text to the actual image content.
            2. If the text is perfectly accurate, return it unchanged with `"was_corrected": false`.
            3. If there is a clear mistake (missing letter, wrong word, etc.), fix it **exactly as per the image**. Do NOT make assumptions.
            4. If you are not sure, do NOT change anything. Set `"was_corrected": false`.

            VERY IMPORTANT:
            - DO NOT rewrite, improve, paraphrase, or fix grammar.
            - DO NOT fix based on what "makes sense" or "looks better".
            - DO NOT assume anything. Only trust what is CLEARLY in the image.
            - DO NOT change any words unless it is a definite visual mismatch.
            - Return response in the EXACT SAME format, no extra text.
            - DO NOT include any descriptive texts.

            Return the response in the below given json format:
            {{
              "question_number": 1,
              "option1": "Red",
              "option2": "Blue",
              "option3": "Green",
              "option4": "Yellow",
              "direction": "",
              "question_images": [],
              "option_images": {{}},
              "question_text": "What is the color of the sky?",
              "was_corrected": true
            }}
            Now validate the following:

            {content_text}
            """
            # Construct input content with images
            input_content = [
                {"type": "text", "text": prompt}
            ]

            # Add all images to the content
            for img_path in image_paths:
                try:
                    base64_image = encode_image_to_base64(img_path)
                    if base64_image:
                        input_content.append({
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}",
                                "detail": "high"
                            }
                        })
                except Exception as e:
                    logger.error(f"Error encoding image {img_path}: {str(e)}")

            try:
                # Send to OpenAI vision model
                response = llm.invoke([HumanMessage(content=input_content, additional_kwargs={"tool_choice": "vision"})])
                logger.info(f"Raw response:\n{response.content}")
                logger.info(f"Original content:\n{content_text}")

                # Calculate cost
                item_cost = calculate_cost(response.usage_metadata)
                total_cost += item_cost
                logger.info(f"Cost for content item {idx+1}: {item_cost}")

                # Parse the response
                parsed_response = self._parse_validation_response(response.content, content_text)
                validated_content.append(parsed_response)

            except Exception as e:
                logger.error(f"Error validating content item {idx+1}: {str(e)}")
                # Add a failed validation entry
                validated_content.append(content_text)

        logger.info(f"Total validation cost: {total_cost}")
        return validated_content

    def validate_content_with_images_explanation(self, content_array: List[Any], image_paths: List[str]) -> List[Dict]:
        """
        Validates content against images using OpenAI's vision model.

        For each content item, sends it along with all images to the vision model to:
        1. Compare the text content with what's visible in the images
        2. Determine if they match exactly
        3. If they don't match, fix the content to match the images exactly

        Args:
            content_array (List[Any]): Array of content items to validate
            image_paths (List[str]): Array of image paths to compare against

        Returns:
            List[Dict]: List of validated content items with validation status
        """
        logger.info(f"Validating {len(content_array)} content items against {len(image_paths)} images")

        # Initialize the LLM
        llm = llm_factory.get_llm("openai_admin", "gpt-4.1-mini")
        validated_content = []
        total_cost = 0

        # Process each content item
        for idx, content_item in enumerate(content_array):
            logger.info(f"Processing content item {idx+1}/{len(content_array)}")

            # Convert content item to string if it's not already
            content_text = str(content_item) if not isinstance(content_item, str) else content_item
            logger.info(f"Content to validate:\n{content_text}")

            # Construct prompt for the vision model
            prompt = f"""
            You will be given:
            - An image.
            - A block of text extracted from that image.

            Your task:
            1. Compare the extracted text with the text in the image.
            2. Check for spelling mistakes, missing letters, or missing words.
            3. If issues are found, correct the text using the image as reference.

            Important Instructions:
            - You must return the response in the exact same JSON format as shown below.
            - DO NOT add or remove any keys.
            - The key "was_corrected" must always appear at the end of the JSON object, as shown.
            - DO NOT include any descriptive texts.

            Return the response in the below given json format:
            {{
                "explanation_number": exp_num,
                "text": block_stripped, # The entire stripped block is the text
                "explanation_images": [], # Initialize as empty list per requirement
                "was_corrected": false
            }}

            Replace the values with the corrected version only if needed. If no corrections are needed, just return the input unchanged, with "was_corrected": false.
            Now validate the following text block:
            {content_text}
            """

            # Construct input content with images
            input_content = [
                {"type": "text", "text": prompt}
            ]

            # Add all images to the content
            for img_path in image_paths:
                try:
                    base64_image = encode_image_to_base64(img_path)
                    if base64_image:
                        input_content.append({
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}",
                                "detail": "high"
                            }
                        })
                except Exception as e:
                    logger.error(f"Error encoding image {img_path}: {str(e)}")

            try:
                # Send to OpenAI vision model
                response = llm.invoke([HumanMessage(content=input_content, additional_kwargs={"tool_choice": "vision"})])
                logger.info(f"Raw response:\n{response.content}")

                # Calculate cost
                item_cost = calculate_cost(response.usage_metadata)
                total_cost += item_cost
                logger.info(f"Cost for content item {idx+1}: {item_cost}")

                # Parse the response
                parsed_response = self._parse_validation_response(response.content, content_text)
                validated_content.append(parsed_response)

            except Exception as e:
                logger.error(f"Error validating content item {idx+1}: {str(e)}")
                # Add a failed validation entry
                validated_content.append(content_text)

        logger.info(f"Total validation cost: {total_cost}")
        return validated_content

    def _parse_validation_response(self, response_content: str, original_content: str) -> Dict:
        """
        Parse the validation response from the LLM.

        Args:
            response_content (str): Raw response from the LLM
            original_content (str): Original content that was validated

        Returns:
            Dict: Parsed validation result
        """
        try:
            # Extract JSON from the response
            import json
            import re

            # Look for JSON pattern in the response
            try:
                # Clean the response
                response = response_content.strip()
                if response.startswith("```json"):
                    response = response[7:]
                if response.endswith("```"):
                    response = response[:-3]

                # Parse JSON
                parsed_json = json.loads(response.strip())
                return parsed_json
            except json.JSONDecodeError as e:
                raise ValueError(f"Invalid JSON in validation response: {str(e)}")
            except Exception as e:
                raise ValueError(f"Failed to parse validation response: {str(e)}")
        except Exception as e:
            logger.error(f"Error parsing validation response: {str(e)}")

    def map_images_to_mcqs(self, mcq_data, extracted_image_urls):
        """
        Maps extracted image URLs to MCQ questions and options while preserving all other data.

        Args:
            mcq_data (dict): The complete MCQ data containing questions, options, answer keys, explanations
            extracted_image_urls (list): List of extracted image URLs

        Returns:
            dict: Updated MCQ data with image URLs mapped to questions and options
        """
        # Create a copy of the original data to avoid modifying it directly
        updated_data = mcq_data.copy()

        # Create a dictionary to quickly look up image URLs by their base name
        image_url_map = {}
        for url in extracted_image_urls:
            # Extract the base name from the URL (e.g., "question_10.png")
            base_name = url.split('/')[-1]
            image_name = base_name.split('.')[0]  # Remove the extension
            image_url_map[image_name] = url

        # Go through each MCQ and map relevant images
        for mcq in updated_data['mcqs']:
            question_number = mcq['question_number']

            # Map question images
            question_image_key = f"question_{question_number}"
            if question_image_key in image_url_map:
                mcq['question_images'] = [image_url_map[question_image_key]]

            option_images = {}
            for i in range(1, 5):  # Assuming 4 options
                option_image_key = f"question_{question_number}_option_{i}"
                if option_image_key in image_url_map:
                    option_key = f"option_{i}"
                    option_images[option_key] = [image_url_map[option_image_key]]
            if option_images:
                mcq['option_images'] = option_images

        return updated_data

    def get_img_response_with_prompt(self, prompt, image_data):

        content = [{"type": "text", "text": f"Context:\n{prompt}"}, {
            "type": "image_url",
            "image_url": {
                "url": f"data:image/jpeg;base64,{image_data}",
                "detail": "high"
            }
        }]

        llm = llm_factory.get_llm("openai_admin", "gpt-4.1-mini")
        response = llm.invoke([HumanMessage(content=content, additional_kwargs={"tool_choice": "vision"})])

        return response.content


def delete_folder_by_id(book_id, chapter_id=None, res_id=None):
    """
    Delete a folder by ID from the local_page_images directory

    Parameters:
    book_id (str): The book ID
    chapter_id (str, optional): The chapter ID. If provided, deletes book_id/chapter_id path
    res_id (str, optional): The resource ID. If provided, deletes book_id/chapter_id/res_id path

    Returns:
    bool: True if deletion was successful, False otherwise
    """
    try:
        base_path = Path(config.PDF_PAGE_IMG_OUTPUT_DIR)

        # Determine the path to delete based on provided parameters
        if res_id is not None and chapter_id is not None:
            # Delete specific resource folder: book_id/chapter_id/res_id
            folder_path = base_path / str(book_id) / str(chapter_id) / str(res_id)
            logger.info(f"Deleting resource folder: {folder_path}")
        elif chapter_id is not None:
            # Delete chapter folder: book_id/chapter_id
            folder_path = base_path / str(book_id) / str(chapter_id)
            logger.info(f"Deleting chapter folder: {folder_path}")
        else:
            # Delete book folder: book_id
            folder_path = base_path / str(book_id)
            logger.info(f"Deleting book folder: {folder_path}")

        if not folder_path.exists():
            logger.info(f"Folder {folder_path} does not exist")
            return False

        shutil.rmtree(folder_path)
        logger.info(f"Successfully deleted folder: {folder_path}")
        return True
    except Exception as e:
        logger.info(f"Error deleting folder: {str(e)}")
        return False


def delete_folder_by_id_extracts(book_id, chapter_id=None, res_id=None):
    """
    Delete a folder by ID from the extracted_text directory

    Parameters:
    book_id (str): The book ID
    chapter_id (str, optional): The chapter ID. If provided, deletes extracted_text/book_id/chapter_id path
    res_id (str, optional): The resource ID. If provided, deletes extracted_text/book_id/chapter_id/res_id path

    Returns:
    bool: True if deletion was successful, False otherwise
    """
    try:
        base_path = Path(config.PDF_PAGE_IMG_OUTPUT_DIR) / 'extracted_text'

        # Determine the path to delete based on provided parameters
        if res_id is not None and chapter_id is not None:
            # Delete specific resource folder: extracted_text/book_id/chapter_id/res_id
            folder_path = base_path / str(book_id) / str(chapter_id) / str(res_id)
            logger.info(f"Deleting extracted text resource folder: {folder_path}")
        elif chapter_id is not None:
            # Delete chapter folder: extracted_text/book_id/chapter_id
            folder_path = base_path / str(book_id) / str(chapter_id)
            logger.info(f"Deleting extracted text chapter folder: {folder_path}")
        else:
            # Delete book folder: extracted_text/book_id
            folder_path = base_path / str(book_id)
            logger.info(f"Deleting extracted text book folder: {folder_path}")

        if not folder_path.exists():
            logger.info(f"Folder {folder_path} does not exist")
            return False

        shutil.rmtree(folder_path)
        logger.info(f"Successfully deleted folder: {folder_path}")
        return True
    except Exception as e:
        logger.info(f"Error deleting extracted text folder: {str(e)}")
        return False

def natural_sort_key(s):
    # Extract the number from the filename using regex
    numbers = re.findall(r'(\d+)', s)
    if numbers:
        return int(numbers[0])
    return s


def parse_questions(text, extracted_data_file_path=None):
    """
    Parses questions line by line, treating Directions and Passage text
    as a single unified context, resetting for new Directions.

    Args:
        text (str): The text to parse for questions
        extracted_data_file_path (str, optional): Path to the extracted_data.txt file
                                               to handle unrecognized content
    """
    mcqs = []
    active_context = ""  # Stores combined Directions and Passage text
    current_mcq_data = None
    parsing_options_for_current_mcq = False

    lines = text.strip().splitlines()
    line_index = 0

    # Check for content at the beginning (before first question)
    unrecognized_content = ""
    first_question_index = -1

    # First, check if there's a direction in the beginning content
    for i, line in enumerate(lines):
        if direction_start_pattern_line.match(line.strip()):
            # Found a direction, process it
            context_buffer = [line.strip()]  # Start with the marker line
            context_end_index = i + 1

            # Consume all lines of this context block
            while context_end_index < len(lines):
                next_line = lines[context_end_index].strip()
                if not next_line or \
                   direction_start_pattern_line.match(next_line) or \
                   question_start_pattern.match(next_line):
                    break
                context_buffer.append(lines[context_end_index])
                context_end_index += 1

            block_text = "\n".join(context_buffer).strip()
            active_context = block_text  # Set the active context
            print(f"Found direction in beginning content: {active_context[:50]}...")
            break

    # Find the first line that matches a question pattern
    for i, line in enumerate(lines):
        if question_start_pattern.match(line.strip()):
            first_question_index = i
            break

    # If there's content before the first question, treat it as unrecognized content
    if first_question_index > 0:
        unrecognized_content = "\n".join(lines[:first_question_index])
        print(f"Found unrecognized content at the beginning: {len(unrecognized_content)} characters")
        print(f"First few lines of unrecognized content: {unrecognized_content[:100]}...")

        # If we have a path to extracted_data.txt, try to handle the unrecognized content
        if extracted_data_file_path:
            print(f"Checking if file exists: {extracted_data_file_path}")
            if os.path.exists(extracted_data_file_path):
                print(f"File exists, reading content")
                full_data = None
                try:
                    # Try reading as a single JSON object first
                    with open(extracted_data_file_path, 'r', encoding='utf-8') as f:  # Added encoding
                        try:
                            full_data = json.load(f)
                            print(f"Successfully loaded as a single JSON object.")
                        except json.JSONDecodeError as e:
                            print(
                                f"JSON decode error (single object): {e}. Trying to parse as multiple concatenated objects.")
                            f.seek(0)  # Reset file pointer to beginning
                            content = f.read()
                            print(f"File content length: {len(content)}")
                            print(f"File content start: {content[:100]}...")

                            # Approach 2: Use JSONDecoder().raw_decode iteratively
                            parsed_objects = []
                            decoder = json.JSONDecoder()
                            pos = 0
                            content_len = len(content)
                            while pos < content_len:
                                # Skip leading whitespace
                                while pos < content_len and content[pos].isspace():
                                    pos += 1
                                if pos >= content_len:
                                    break  # End of content

                                try:
                                    obj, end_pos = decoder.raw_decode(content, pos)
                                    parsed_objects.append(obj)
                                    print(f"Successfully decoded object from position {pos} to {end_pos}")
                                    pos = end_pos
                                except json.JSONDecodeError as decode_err:
                                    print(f"Could not decode JSON object starting at position {pos}: {decode_err}")
                                    print(f"Problematic content nearby: ...{content[max(0, pos - 20):pos + 20]}...")
                                    # Decide how to handle error: break, skip, or try to recover?
                                    # For now, we'll break, assuming the rest might be corrupted.
                                    break  # Stop processing on first error

                            if parsed_objects:
                                print(f"Parsed {len(parsed_objects)} JSON objects iteratively.")
                                # Merge the objects (assuming they have the same structure)
                                full_data = {
                                    "mcqs": [],
                                    "answer_keys": {},
                                    "explanations": []
                                }
                                for obj in parsed_objects:
                                    if isinstance(obj, dict):  # Basic check if it's a dictionary
                                        if "mcqs" in obj and isinstance(obj.get("mcqs"), list):
                                            full_data["mcqs"].extend(obj["mcqs"])
                                        if "answer_keys" in obj and isinstance(obj.get("answer_keys"), dict):
                                            full_data["answer_keys"].update(
                                                obj["answer_keys"])  # Use update to merge keys
                                        if "explanations" in obj and isinstance(obj.get("explanations"), list):
                                            full_data["explanations"].extend(obj["explanations"])
                                    else:
                                        print(f"Warning: Parsed object is not a dictionary: {type(obj)}")

                                print(f"Merged data: {len(full_data.get('mcqs', []))} MCQs")
                            else:
                                print("Could not parse any JSON objects iteratively.")

                    # --- Continue with processing full_data (whether loaded or created) ---
                    if full_data and "mcqs" in full_data and full_data["mcqs"]:
                        print(f"Found {len(full_data['mcqs'])} MCQs in the data")
                        last_mcq = full_data["mcqs"][-1]
                        print(f"Last MCQ: question_number={last_mcq.get('question_number')}")

                        # Combine the last MCQ's question text with the unrecognized content
                        # Make sure last_mcq["question_text"] is not None
                        combined_text = (last_mcq.get("question_text", "") or "") + "\n" + unrecognized_content
                        print(f"Combined text length: {len(combined_text)}")
                        print(f"First few lines of combined text: {combined_text[:100]}...")

                        # Actually parse the combined text and update the last MCQ
                        options = {}

                        # Regex approach for options (Keep this as it might work for some cases)
                        for line in unrecognized_content.splitlines():
                            line_stripped = line.strip()
                            # Ensure option_pattern_multi is defined and suitable
                            option_matches = list(option_pattern_multi.finditer(line_stripped))
                            if option_matches:
                                for match in option_matches:
                                    try:
                                        opt_num = int(match.group(1))
                                        opt_text = match.group(2).strip()
                                        key = f"option{opt_num}"
                                        options[key] = opt_text
                                        print(f"Extracted option {opt_num} using regex: {opt_text[:30]}...")
                                    except (IndexError, ValueError):
                                        print(f"Warning: Regex match failed for line: {line_stripped}")

                        # Line-by-line approach if regex failed
                        if not options:
                            print("No options found with regex, trying line-by-line approach")
                            unrecognized_lines = unrecognized_content.splitlines()
                            current_option = None
                            option_text = ""

                            for line in unrecognized_lines:
                                line_stripped = line.strip()
                                # print(f"Processing line: {line_stripped[:30]}...") # Can be verbose

                                found_option_start = False
                                for i in range(1, 5):
                                    prefix = f"({i})"
                                    if line_stripped.startswith(prefix):
                                        # print(f"Found option {i} start: {line_stripped}")
                                        if current_option is not None:
                                            options[f"option{current_option}"] = option_text.strip()
                                            # print(f"Saved option{current_option}: {option_text.strip()[:30]}...")
                                        current_option = i
                                        option_text = line_stripped[len(prefix):].strip()
                                        found_option_start = True
                                        break

                                if not found_option_start and current_option is not None:
                                    option_text += "\n" + line_stripped

                            if current_option is not None:
                                options[f"option{current_option}"] = option_text.strip()
                                # print(f"Saved last option{current_option}: {option_text.strip()[:30]}...")

                        print(
                            f"Extracted options: { {k: v[:30] + '...' for k, v in options.items()} }")  # Print truncated options

                        # Update the last MCQ
                        last_mcq["question_text"] = combined_text.strip()  # Strip combined text
                        for i in range(1, 5):
                            option_key = f"option{i}"
                            if option_key in options:
                                last_mcq[option_key] = options[option_key]
                                print(f"Updated {option_key}")  # Removed value printing for brevity
                            elif last_mcq.get(option_key) is None:  # Only set to empty if not already set
                                last_mcq[option_key] = ""  # Or None, depending on desired default

                        # Replace the last MCQ in the extracted data
                        full_data["mcqs"][-1] = last_mcq
                        print(f"Updated last MCQ (Q#: {last_mcq.get('question_number')}) in full_data")

                        # Write the updated data back to the file
                        try:
                            with open(extracted_data_file_path, 'w', encoding='utf-8') as f:  # Added encoding
                                json.dump(full_data, f, indent=2,
                                          ensure_ascii=False)  # Added ensure_ascii=False for unicode
                            print(f"Wrote updated data back to file")
                        except Exception as e:
                            print(f"Error writing updated data to file: {str(e)}")
                    else:
                        print("No MCQs found in the data or data is None after attempting load/fallback.")

                except Exception as e:
                    print(f"General error handling unrecognized content section: {str(e)}")
                    # Consider what state full_data should be in here - maybe None?
                    full_data = None  # Ensure full_data is None if error occurred before assignment

            else:
                print(f"File does not exist: {extracted_data_file_path}")
                # Decide if you want to create the fallback structure even if the file doesn't exist
                # (Current logic skips this section if file doesn't exist)

        else:
            print("No extracted_data_file_path provided, skipping unrecognized content handling.")

        # Start parsing from the first *actual* question in the current text chunk
        print(f"Starting to parse current text chunk from line index {first_question_index}")
        lines = lines[first_question_index:]  # Adjust lines for subsequent parsing
        # Reset line_index if it's used later in a loop processing 'lines'
        line_index = 0

    def finalize_current_mcq():
        """Finalizes the current MCQ, assigns context, and adds to list."""
        nonlocal current_mcq_data, mcqs, parsing_options_for_current_mcq
        if current_mcq_data:
            q_text_lines = current_mcq_data.get("_question_text_lines", [])
            current_mcq_data["question_text"] = "\n".join(q_text_lines).strip()
            current_mcq_data.pop("_question_text_lines", None)

            current_mcq_data["direction"] = current_mcq_data.get("_active_context_at_start", "")
            current_mcq_data.pop("_active_context_at_start", None)
            current_mcq_data.pop("passage", None)  # Ensure passage key is removed

            mcqs.append(current_mcq_data)

        current_mcq_data = None
        parsing_options_for_current_mcq = False

    while line_index < len(lines):
        line = lines[line_index]
        line_stripped = line.strip()

        if not line_stripped:
            line_index += 1
            continue

        # --- Check for Context Markers (Directions or Passage) ---
        is_direction_start = direction_start_pattern_line.match(line_stripped)
        is_passage_start = passage_start_pattern_line.match(line_stripped)
        is_context_start = is_direction_start or is_passage_start

        if is_context_start:
            finalize_current_mcq()  # Finish any question before processing context
            context_buffer = [line_stripped]  # Start with the marker line
            context_end_index = line_index + 1

            # Consume all lines of this context block
            while context_end_index < len(lines):
                next_line = lines[context_end_index].strip()
                if not next_line or \
                   direction_start_pattern_line.match(next_line) or \
                   passage_start_pattern_line.match(next_line) or \
                   question_start_pattern.match(next_line):
                    break
                context_buffer.append(lines[context_end_index])
                context_end_index += 1

            block_text = "\n".join(context_buffer).strip()

            # *** FIX: Reset context for Directions, append for Passage ***
            if is_direction_start:
                active_context = block_text  # Reset context for new Directions
            elif is_passage_start:
                # Append passage to existing context
                if active_context:
                    active_context += "\n\n" + block_text
                else:
                    # If passage appears first, it becomes the context
                    active_context = block_text

            line_index = context_end_index  # Jump loop index
            continue  # Process next line after context block

        # --- Process Question/Options/Text Lines ---
        q_match = question_start_pattern.match(line_stripped)
        option_matches = list(option_pattern_multi.finditer(line_stripped))
        is_option_line = bool(option_matches)

        if q_match:
            q_num = int(q_match.group(1))
            q_text_first_line = q_match.group(2).strip()

            start_new_question = False
            if current_mcq_data is None: start_new_question = True
            elif parsing_options_for_current_mcq: start_new_question = True

            if start_new_question:
                finalize_current_mcq()  # Finalize previous

                options_on_q_line = list(option_pattern_multi.finditer(q_text_first_line))
                initial_q_text_lines = []
                text_before_options = q_text_first_line
                if options_on_q_line:
                    first_option_start = options_on_q_line[0].start()
                    text_before_options = q_text_first_line[:first_option_start].strip()
                if text_before_options: initial_q_text_lines = [text_before_options]

                current_mcq_data = {  # Create new data
                    "question_number": q_num,
                    "_question_text_lines": initial_q_text_lines,
                    "option1": None, "option2": None, "option3": None, "option4": None,
                    "direction": "",  # Assigned on finalize
                    "passage": "",   # Dummy key to remove later
                    "_active_context_at_start": active_context,  # Store snapshot
                    "question_images": [], "option_images": {}
                }
                current_mcq_data.pop("passage")  # Remove passage key


                if options_on_q_line:
                    parsing_options_for_current_mcq = True
                    for match in options_on_q_line:
                        opt_num = int(match.group(1)); opt_text = match.group(2).strip()
                        key = f"option{opt_num}"; current_mcq_data[key] = opt_text

            else: # Internal list item
                if current_mcq_data:
                    current_mcq_data["_question_text_lines"].append(line_stripped)

        elif is_option_line:
            if current_mcq_data:
                if not parsing_options_for_current_mcq:
                    parsing_options_for_current_mcq = True
                    first_option_start = option_matches[0].start()
                    text_before = line_stripped[:first_option_start].strip()
                    if text_before: current_mcq_data["_question_text_lines"].append(text_before)

                for match in option_matches:
                    opt_num = int(match.group(1)); opt_text = match.group(2).strip()
                    key = f"option{opt_num}"; current_mcq_data[key] = opt_text

        else: # Regular text line
            if current_mcq_data and not parsing_options_for_current_mcq:
                current_mcq_data["_question_text_lines"].append(line_stripped)

        line_index += 1  # Move to next line

    finalize_current_mcq()  # Finalize the last element

    return mcqs


def parse_explanations(text):
    """
    Parses a block of text containing numbered explanations.

    Args:
        text: A string containing the explanations.

    Returns:
        A list of dictionaries, where each dictionary represents an explanation.
    """
    explanations_data = []
    # Add newline to handle potential split at the very beginning and ensure ^ works
    text_to_split = '\n' + text.strip()

    # Split using positive lookahead anchored to the start of lines (re.MULTILINE)
    # This ensures each block starts with "N. "
    blocks = re.split(r'(?=^\d+\.\s)', text_to_split, flags=re.MULTILINE)

    # Pattern to extract the explanation number from the start of the block
    number_pattern = re.compile(r'^(\d+)\.') # Match digits followed by a dot at the start

    for block in blocks:
        block_stripped = block.strip()
        if not block_stripped:
            continue # Skip empty strings resulting from split

        # Check if the block starts with the expected pattern N.
        match = number_pattern.match(block_stripped)
        if match:
            exp_num = int(match.group(1))

            # Create the desired dictionary object
            explanation_obj = {
                "explanation_number": exp_num,
                "text": block_stripped, # The entire stripped block is the text
                "explanation_images": [] # Initialize as empty list per requirement
            }
            explanations_data.append(explanation_obj)
        else:
            # This might happen for the very first chunk if the text
            # doesn't start exactly with "1. ", or malformed text.
            print(f"Warning: Skipping block not starting with 'N.': {block_stripped[:50]}...")

    return explanations_data

def read_extracted_data(file_path):
    """
    Read the extracted data from an S3 file path containing multiple JSON objects
    and merge all the data.

    Args:
        file_path: The S3 path key (relative to mount point) to the extracted_data.txt file

    Returns:
        dict: The merged data containing all mcqs, answer_keys, and explanations
    """
    try:
        logger.info(f"Reading extracted data from: {file_path}")

        # Import the S3 utilities
        from utils.s3_utils import read_file_from_s3
        from utils.pdf_utils import aggregate_data_from_json_objects

        # Read the file from S3

        full_s3_path = f"{file_path}"
        logger.info(f"Reading file from S3: {full_s3_path}")
        file_content = read_file_from_s3(full_s3_path)
        logger.info(f"File content length: {len(file_content) if file_content else 0}")

        if not file_content:
            logger.error(f"File not found: {file_path}")

        # Convert bytes to string if necessary
        if isinstance(file_content, bytes):
            try:
                file_content = file_content.decode('utf-8')
                logger.info(f"Decoded bytes to string. New length: {len(file_content)}")
            except UnicodeDecodeError as e:
                 logger.error(f"UTF-8 decoding error for S3 file content: {e}")

        # --- Start Parsing Multiple JSON Objects ---
        logger.info("Starting to parse multiple JSON objects from file content.")
        decoder = json.JSONDecoder()
        pos = 0
        parsed_objects = []
        content_to_parse = file_content.strip() # Remove leading/trailing whitespace once
        content_len = len(content_to_parse)

        while pos < content_len:
            try:
                # Find the start of the next potential JSON object (skip whitespace)
                obj_start = -1
                for i in range(pos, content_len):
                    if not content_to_parse[i].isspace():
                        obj_start = i
                        break

                if obj_start == -1: # No more non-whitespace characters found
                    logger.info("Reached end of non-whitespace content.")
                    break # Exit loop if only whitespace remains

                # Decode one JSON object from the determined start position
                obj, pos = decoder.raw_decode(content_to_parse, obj_start)
                parsed_objects.append(obj)
                # logger.info(f"Successfully parsed object ending at position {pos}. Found {len(parsed_objects)} objects so far.")


            except JSONDecodeError as e:
                # More context for JSON errors
                error_context_start = max(0, obj_start - 30) if 'obj_start' in locals() else max(0, pos-30)
                error_context_end = min(content_len, error_context_start + 60)
                error_context = content_to_parse[error_context_start:error_context_end]
                error_pos = e.pos + (obj_start if 'obj_start' in locals() else 0) # Adjust error pos relative to obj_start
                logger.error(f"Invalid JSON structure in S3 file '{file_path}' near character position {error_pos}. Error: {e}. Context: ...{error_context}...")

            except Exception as e:
                 # Catch other unexpected errors during the parsing loop
                 logger.exception(f"Unexpected error processing JSON object near position {pos} in S3 file '{file_path}': {e}") # Use logger.exception to include traceback

        logger.info(f"Finished parsing. Found {len(parsed_objects)} JSON objects in the file.")

        # --- Aggregate the data ---
        aggregated_data = aggregate_data_from_json_objects(parsed_objects)

        # --- Return the result ---
        logger.info(f"Returning aggregated data for S3 key: {file_path}")
        return aggregated_data
    except Exception as e:
        logger.error(f"Error reading extracted data from S3: {str(e)}")


def convert_answer_keys_to_string(answer_keys_dict):
    """
    Convert a dictionary of answer keys to a formatted string.

    Args:
        answer_keys_dict (dict): Dictionary with keys as line numbers and values as formatted answer strings

    Returns:
        str: Formatted string of answer keys, or empty string if the dictionary is empty
    """
    # Handle empty dictionary case
    if not answer_keys_dict:
        return ""

    # The first line is the header "ANSWERS"
    result = [answer_keys_dict.get('1', 'ANSWERS')]

    # Extract the answer lines from the dictionary
    # The keys are line numbers starting from 2
    sorted_keys = sorted([int(k) for k in answer_keys_dict.keys() if k != '1'])

    # Add each line of answers
    for key in sorted_keys:
        result.append(answer_keys_dict[str(key)])

    # Join all lines with newlines
    return "\n".join(result)

