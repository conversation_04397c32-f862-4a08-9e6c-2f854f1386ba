import logging
import json
import traceback
import asyncio
from typing import Dict, List, Optional
from contextlib import contextmanager
from sqlalchemy import text
from db_config.db import get_session, CONTENT_SCHEMA
from llm_manager.llm_factory import LLMFactory
from config import llm_config
import config
from langchain_core.messages import HumanMessage

logger = logging.getLogger(__name__)
llm_factory = LLMFactory(llm_config)

class MCQValidator:
    """
    Class for validating MCQs using LLM.
    """

    def __init__(self, max_concurrent_validations: int = None):
        """
        Initialize the MCQValidator.

        Args:
            max_concurrent_validations: Maximum number of concurrent validations to run in parallel (uses config value if None)
        """
        self.max_concurrent_validations = max_concurrent_validations or getattr(config, 'MAX_CONCURRENT_VALIDATIONS', 2)
        logger.info(f"MCQValidator initialized with max_concurrent_validations={self.max_concurrent_validations}")

    async def validate_mcqs(self, quiz_id: str) -> Dict:
        """
        Validate MCQs for a given quiz ID.

        Args:
            quiz_id: Quiz ID from resource_dtl.res_link

        Returns:
            Dict: Validation results
        """
        try:
            # Step 1: Retrieve MCQ data from the database
            mcqs = await self._get_mcqs_from_database(quiz_id)

            if isinstance(mcqs, dict) and mcqs.get("status") == "error":
                # If we got an error response, return it
                return mcqs

            # Step 2: Validate MCQs with LLM
            validated_mcqs = await self._validate_with_llm(mcqs)

            # Step 3: Update invalid answers in the database
            await self._update_invalid_answers(validated_mcqs)

            # Return the validation results
            return {
                "status": "success",
                "mcqs": validated_mcqs
            }
        except Exception as e:
            logger.error(f"Error validating MCQs: {e}")
            return {"status": "error", "message": f"Error validating MCQs: {str(e)}"}

    async def _get_mcqs_from_database(self, quiz_id: str) -> List[Dict] or Dict:
        """
        Get MCQs from the database for a given quiz ID.

        Args:
            quiz_id: Quiz ID from resource_dtl.res_link

        Returns:
            List[Dict] or Dict: List of MCQ dictionaries or error response
        """
        # Get a database session for the wscontent schema
        db_session = next(get_session(CONTENT_SCHEMA))
        try:
            # Query the objective_mst table to get all MCQs for this quiz
            mcq_query = text("""
                SELECT id, question, option1, option2, option3, option4, option5,
                       answer1, answer2, answer3, answer4, answer5,
                       answer_description, difficultylevel, direction_id
                FROM wscontent.objective_mst
                WHERE quiz_id = :quiz_id
                ORDER BY id
            """)

            mcq_result = db_session.execute(mcq_query, {"quiz_id": quiz_id})
            mcq_rows = mcq_result.fetchall()

            if not mcq_rows:
                logger.warning(f"No MCQs found for quiz ID: {quiz_id}")
                return {"status": "error", "message": f"No MCQs found for quiz ID {quiz_id}"}

            # Process each MCQ
            mcqs = []
            for row in mcq_rows:
                mcq = {
                    "id": row[0],
                    "question": row[1],
                    "option1": row[2],
                    "option2": row[3],
                    "option3": row[4],
                    "option4": row[5],
                    "option5": row[6],
                    "answer1": row[7],
                    "answer2": row[8],
                    "answer3": row[9],
                    "answer4": row[10],
                    "answer5": row[11],
                    "answer_description": row[12],
                    "difficultylevel": row[13],
                    "direction_id": row[14]
                }

                # Determine the correct answer
                correct_answer = None
                correct_option = None
                for i in range(1, 6):
                    if mcq[f"answer{i}"] == "Yes":
                        correct_answer = i
                        correct_option = mcq[f"option{i}"]
                        break

                mcq["correct_answer"] = correct_answer
                mcq["correct_option"] = correct_option

                # Get direction if present
                if mcq["direction_id"]:
                    direction_query = text("""
                        SELECT directions
                        FROM wscontent.directions_mst
                        WHERE id = :direction_id
                        LIMIT 1
                    """)

                    direction_result = db_session.execute(direction_query, {"direction_id": mcq["direction_id"]})
                    direction_row = direction_result.fetchone()

                    if direction_row:
                        mcq["directions"] = direction_row[0]

                mcqs.append(mcq)

            return mcqs
        finally:
            # Close the database session immediately after retrieving data
            db_session.close()

    async def _validate_with_llm(self, mcqs: List[Dict]) -> List[Dict]:
        """
        Validate MCQs using LLM in parallel with a maximum concurrency limit.

        Args:
            mcqs: List of MCQ dictionaries

        Returns:
            List[Dict]: Validated MCQs
        """
        # Get LLM
        llm = llm_factory.get_llm("openai", "gpt-4.1-mini")

        # Create a semaphore to limit concurrent LLM calls
        semaphore = asyncio.Semaphore(self.max_concurrent_validations)

        # Create a thread pool for running synchronous LLM calls in parallel
        from concurrent.futures import ThreadPoolExecutor
        thread_executor = ThreadPoolExecutor(max_workers=self.max_concurrent_validations)

        # Define a helper function to process a single MCQ with semaphore
        async def process_single_mcq(mcq):
            async with semaphore:
                try:
                    logger.info(f"Validating MCQ {mcq['id']}...")
                    # Build prompt
                    prompt = self._build_prompt(mcq)

                    # Run the synchronous LLM call in a thread pool to allow parallelism
                    loop = asyncio.get_event_loop()
                    response = await loop.run_in_executor(
                        thread_executor,
                        lambda: llm.invoke([HumanMessage(content=prompt)])
                    )

                    # Parse response
                    validation_result = self._parse_llm_response(response.content)

                    # Update MCQ with validation result
                    mcq.update(validation_result)

                    logger.info(f"Validated MCQ {mcq['id']}: {validation_result['is_valid_answer_key']}")
                    return mcq
                except Exception as e:
                    logger.error(f"Error validating MCQ {mcq['id']}: {e}")
                    logger.error(traceback.format_exc())
                    mcq["is_valid_answer_key"] = True  # Default to valid if error
                    mcq["llm_error"] = str(e)
                    return mcq

        # Create tasks for all MCQs
        tasks = [process_single_mcq(mcq) for mcq in mcqs]

        # Run all tasks concurrently and gather results
        logger.info(f"Starting parallel validation of {len(mcqs)} MCQs with max concurrency of {self.max_concurrent_validations}")
        validated_mcqs = await asyncio.gather(*tasks)
        logger.info(f"Completed validation of {len(validated_mcqs)} MCQs")

        # Shutdown the thread executor
        thread_executor.shutdown(wait=False)

        return validated_mcqs

    def _build_prompt(self, mcq: Dict) -> str:
        """
        Build prompt for LLM.

        Args:
            mcq: MCQ dictionary

        Returns:
            str: Prompt for LLM
        """
        # Determine the correct answer option
        correct_answer_num = mcq["correct_answer"]
        correct_option = mcq[f"option{correct_answer_num}"] if correct_answer_num else "None"

        # Build prompt
        prompt = f"""
        Directions (if present): {mcq.get('directions', 'None')}

        Question Text: {mcq['question']}

        Options:
        1. {mcq['option1'] or 'None'}
        2. {mcq['option2'] or 'None'}
        3. {mcq['option3'] or 'None'}
        4. {mcq['option4'] or 'None'}
        5. {mcq['option5'] or 'None'}

        Correct Answer: {correct_answer_num}. {correct_option}

        Answer Description (if present): {mcq.get('answer_description', 'None')}

        Please verify for the given question if the correct answer is correct or not. If not, give the correct answer option as answer1=Yes, answer2=Yes, answer3=Yes, answer4=Yes, answer5=Yes -
        whichever is correct and 'is_valid_answer_key=false'. Otherwise just return is_valid_answer_key=true.
        Give the response in valid JSON format.
        {{
        "is_valid_answer_key":true/false,
        "answer1":"Yes/No",
        "answer2":"Yes/No",
        "answer3":"Yes/No",
        "answer4":"Yes/No",
        "answer5":"Yes/No"
        }}
        """

        return prompt

    def _parse_llm_response(self, response: str) -> Dict:
        """
        Parse LLM response.

        Args:
            response: LLM response

        Returns:
            Dict: Parsed response
        """
        try:
            # Try to parse as JSON
            result = json.loads(response)

            # Ensure required fields are present
            if "is_valid_answer_key" not in result:
                result["is_valid_answer_key"] = True  # Default to valid

            # Determine corrected answer if invalid
            if not result["is_valid_answer_key"]:
                corrected_answer = None
                for i in range(1, 6):
                    if result.get(f"answer{i}") == "Yes":
                        corrected_answer = i
                        break

                result["corrected_answer"] = corrected_answer

            return result
        except json.JSONDecodeError:
            # If not valid JSON, try to extract key information
            is_valid = "is_valid_answer_key=true" in response.lower()

            result = {
                "is_valid_answer_key": is_valid,
                "raw_response": response
            }

            # Try to extract corrected answer if invalid
            if not is_valid:
                for i in range(1, 6):
                    if f"answer{i}=Yes" in response:
                        result["corrected_answer"] = i
                        break

            return result

    @contextmanager
    def get_connection(self):
        """
        Context manager to get a direct database connection.
        This ensures the connection is properly closed after use.
        """
        # Import necessary modules
        from db_config.db import get_engine

        # Get the engine
        engine = get_engine(CONTENT_SCHEMA)

        # Create a new connection
        connection = engine.raw_connection()
        try:
            cursor = connection.cursor()
            yield cursor
            connection.commit()
            logger.info("Transaction committed successfully")
        except Exception as e:
            connection.rollback()
            logger.error(f"Transaction rolled back due to error: {e}")
            raise e
        finally:
            cursor.close()
            connection.close()
            logger.info("Database connection closed")

    async def _update_invalid_answers(self, mcqs: List[Dict]) -> None:
        """
        Update invalid answers in the database using raw SQL queries.

        Args:
            mcqs: List of validated MCQ dictionaries
        """
        for mcq in mcqs:
            if not mcq.get("is_valid_answer_key", True) and "corrected_answer" in mcq:
                try:
                    # Get the corrected answer
                    mcq_id = mcq["id"]
                    corrected_answer = mcq["corrected_answer"]
                    logger.info(f"MCQ {mcq_id} - Attempting to update with corrected answer: {corrected_answer}")

                    # Validate corrected_answer
                    if not corrected_answer or not 1 <= corrected_answer <= 5:
                        logger.warning(f"Invalid corrected_answer value: {corrected_answer}. Must be between 1 and 5.")
                        continue

                    # First reset all answers to 'No'
                    with self.get_connection() as cursor:
                        # Reset all answer fields to "No"
                        reset_query = f"""
                            UPDATE {CONTENT_SCHEMA}.objective_mst
                            SET answer1 = Null, answer2 = Null, answer3 = Null, answer4 = Null, answer5 = Null
                            WHERE id = %s
                        """

                        # Log the query
                        logger.info(f"Executing reset query: {reset_query} with params: ({mcq_id},)")

                        # Execute the query
                        cursor.execute(reset_query, (mcq_id,))

                        # Log the result
                        rows_affected = cursor.rowcount
                        logger.info(f"Reset query affected {rows_affected} rows")

                    # Then set the corrected answer to 'Yes'
                    with self.get_connection() as cursor:
                        # Set the corrected answer to "Yes"
                        update_query = f"""
                            UPDATE {CONTENT_SCHEMA}.objective_mst
                            SET answer{corrected_answer} = 'Yes', is_valid_answer_key = 'false'
                            WHERE id = %s
                        """

                        # Log the query
                        logger.info(f"Executing update query: {update_query} with params: ({mcq_id},)")

                        # Execute the query
                        cursor.execute(update_query, (mcq_id,))

                        # Log the result
                        rows_affected = cursor.rowcount
                        logger.info(f"Update query affected {rows_affected} rows")

                    # Verify the update
                    with self.get_connection() as cursor:
                        verify_query = f"""
                            SELECT answer1, answer2, answer3, answer4, answer5, is_valid_answer_key
                            FROM {CONTENT_SCHEMA}.objective_mst
                            WHERE id = %s
                        """

                        cursor.execute(verify_query, (mcq_id,))
                        row = cursor.fetchone()

                        if row:
                            logger.info(f"Verification for MCQ {mcq_id}:")
                            logger.info(f"  answer1={row[0]}, answer2={row[1]}, answer3={row[2]}, answer4={row[3]}, answer5={row[4]}")
                            logger.info(f"  is_valid_answer_key={row[5]}")

                            # Check if the update was successful
                            if row[corrected_answer-1] == 'Yes':
                                logger.info(f"Update successful: answer{corrected_answer} is 'Yes'")
                            else:
                                logger.error(f"Update failed: answer{corrected_answer} should be 'Yes' but is '{row[corrected_answer-1]}'")
                        else:
                            logger.warning(f"Verification failed: No row found for MCQ {mcq_id}")

                    # Add original answer to MCQ for display
                    mcq["original_answer"] = mcq["correct_answer"]
                    logger.info(f"Updated MCQ {mcq_id} with corrected answer: {corrected_answer}")

                except Exception as e:
                    logger.error(f"Error updating MCQ {mcq.get('id')}: {e}")
                    logger.error(traceback.format_exc())  # Add full traceback for better debugging
