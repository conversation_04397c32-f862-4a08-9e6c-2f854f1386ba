# File: agents/utils/pdf_helpers.py
import logging
import os
from typing import List, Optional

import fitz
import tempfile
from PIL import Image

import config
import external_api_endpoints
from ..utils.image_utils import crop_image
from utils.s3_utils import read_file_from_s3, get_s3_path

# Get logger instance
logger = logging.getLogger(__name__)


class PDFImageConverter:
    """Utility class to convert PDF pages to images and upload to API"""

    def __init__(self, progress_tracker=None, task_id=None):
        self.base_url = config.BASE_URL
        # Keep the API URL for backward compatibility
        self.api_url = config.BASE_URL + external_api_endpoints.IMAGE_UPLOAD_API_ENDPOINT
        self.progress_tracker = progress_tracker
        self.task_id = task_id

    def track_progress(self, step, progress=None):
        """Track progress if a progress tracker is available."""
        # Always log the step
        logger.info(f"PDF Converter: {step} ({progress if progress else 'N/A'}%)")

        # If progress tracker is available, add the step
        if self.progress_tracker and self.task_id:
            logger.info(f"PDF Converter sending progress update to tracker for task {self.task_id}: {step}")
            try:
                # Force a small delay to ensure updates are processed separately
                import time
                time.sleep(0.1)  # 100ms delay to separate updates

                # Add the step to the progress tracker
                self.progress_tracker.add_step(self.task_id, step, progress)

                # Verify the step was added
                task = self.progress_tracker.get_task(self.task_id)
                if task and 'steps' in task and any(s.get('step') == step for s in task['steps']):
                    logger.info(f"Step '{step}' successfully added to task {self.task_id}")
                else:
                    logger.warning(f"Step '{step}' may not have been added to task {self.task_id}")
            except Exception as e:
                logger.error(f"Error sending progress update from PDF Converter: {e}")
        else:
            logger.warning(f"PDF Converter: No progress tracker available for step: {step}")

    def convert_and_upload(self, pdf_path: str, book_id: str, chapter_id: str, res_id: str, zoom: float = None) -> dict:
        """
        Convert PDF pages to images and save locally without API upload

        Args:
            pdf_path: Path to the PDF file
            book_id: Book identifier
            chapter_id: Chapter identifier
            res_id: Resource identifier
            zoom: Zoom factor for better resolution (uses config default if None)

        Returns:
            dict: Result containing status and local file paths
        """
        try:
            # Use config default zoom factor if not specified
            if zoom is None:
                zoom = getattr(config, 'PDF_ZOOM_FACTOR', 1)

            logger.info(f"[PDFImageConverter] Starting PDF conversion for book_id: {book_id}, chapter_id: {chapter_id}, res_id: {res_id}, zoom: {zoom}")
            # Ensure local storage directory exists
            local_storage_path = os.path.join(config.PDF_PAGE_IMG_OUTPUT_DIR, str(book_id), str(chapter_id),
                                              str(res_id))

            # Check if images already exist in the directory
            if os.path.exists(local_storage_path):
                existing_images = [f for f in os.listdir(local_storage_path) if f.endswith('.png')]

                if existing_images:
                    # Images already exist, collect their paths
                    self.track_progress(f"Found existing images in {local_storage_path}", 35)
                    logger.info(f"Images already exist in {local_storage_path}, skipping conversion")

                    # Separate regular and cropped images
                    image_paths = []
                    cropped_image_paths = []

                    for img_name in existing_images:
                        full_path = os.path.join(local_storage_path, img_name)
                        if '_col_' in img_name:
                            cropped_image_paths.append(full_path)
                        else:
                            image_paths.append(full_path)

                    # Sort the paths to ensure they're in the correct order
                    image_paths.sort()
                    cropped_image_paths.sort()

                    # Get page count from the number of regular images
                    page_count = len(image_paths)

                    self.track_progress(f"Using {page_count} existing pages", 39)

                    return {
                        "status": "success",
                        "message": f"Using {page_count} existing pages",
                        "image_urls": image_paths,
                        "cropped_image_urls": cropped_image_paths,
                        "total_pages": page_count
                    }

            logger.info(f"[PDFImageConverter] PDF path: {pdf_path}, zoom factor: {zoom}")

            # Check if the file exists locally first
            if os.path.isfile(pdf_path):
                logger.info(f"[PDFImageConverter] PDF file exists locally: {pdf_path}")
                self.track_progress(f"Using local PDF file: {pdf_path}", 32)
                temp_path = pdf_path
            else:
                # Try to get the file from S3
                logger.info(f"[PDFImageConverter] PDF file not found locally, trying S3")

                # Get the full S3 path
                full_s3_path = get_s3_path(pdf_path)
                logger.info(f"[PDFImageConverter] Full S3 mount path: {full_s3_path}")

                # Read the PDF file using sudo
                logger.info(f"[PDFImageConverter] Reading PDF from S3 mount path using sudo")
                self.track_progress(f"Reading PDF file: {pdf_path}", 32)

                # Read the file content using sudo
                self.track_progress(f"Reading PDF file from S3: {pdf_path}", 33)
                pdf_content = read_file_from_s3(full_s3_path)
                if pdf_content is None:
                    logger.error(f"[PDFImageConverter] CRITICAL ERROR: Failed to read PDF file from S3: {full_s3_path}")
                    self.track_progress(f"Failed to read PDF file from S3: {full_s3_path}", 100)
                    return {
                        "status": "error",
                        "message": f"Failed to read PDF file from S3: {full_s3_path}"
                    }

                # Create a temporary file to work with
                self.track_progress(f"Creating temporary file for PDF processing", 34)
                with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                    temp_path = temp_file.name
                    temp_file.write(pdf_content)
                    self.track_progress(f"PDF content written to temporary file", 35)

            # Open the PDF from the temporary file
            logger.info(f"[PDFImageConverter] Opening PDF from temporary file: {temp_path}")
            self.track_progress(f"Opening PDF document", 36)
            pdf_document = fitz.open(temp_path)

            # If we get here, we need to convert the PDF
            self.track_progress(f"Creating output directory", 37)
            os.makedirs(local_storage_path, exist_ok=True)

            # We already have the PDF document open from the temporary file
            logger.info(f"[PDFImageConverter] Using already opened PDF document from temporary file")

            # Store the page count before processing
            page_count = pdf_document.page_count
            logger.info(f"[PDFImageConverter] PDF has {page_count} pages to process")
            self.track_progress(f"PDF has {page_count} pages to process", 38)

            # We already created the local storage directory above
            logger.info(f"[PDFImageConverter] Using existing local storage directory: {local_storage_path}")

            # Store file paths instead of preparing for upload
            image_paths = []
            cropped_image_paths = []

            # Convert each page to image
            for page_number in range(page_count):
                progress = 33 + int((page_number / page_count) * 5)  # Progress from 33% to 38%
                self.track_progress(f"Converting page {page_number + 1} of {page_count}", progress)

                page = pdf_document[page_number]
                mat = fitz.Matrix(zoom, zoom)
                pix = page.get_pixmap(matrix=mat)
                img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)

                # Store each page image with page number locally
                self.track_progress(f"Saving page {page_number + 1} image", progress)
                local_img_path = os.path.join(local_storage_path, f"page_{page_number + 1}.png")
                img.save(local_img_path, format="PNG")
                image_paths.append(local_img_path)

                # Crop the image using the updated margins
                self.track_progress(f"Cropping page {page_number + 1}", progress)
                column_images = crop_image(img)

                # Process each column image separately
                for col_idx, col_img in enumerate(column_images):
                    # Save cropped image locally
                    self.track_progress(f"Saving cropped column {col_idx + 1} of page {page_number + 1}", progress)
                    local_cropped_path = os.path.join(local_storage_path,
                                                      f"page_{page_number + 1}_col_{col_idx + 1}.png")
                    col_img.save(local_cropped_path, format="PNG")
                    cropped_image_paths.append(local_cropped_path)

            # Close the PDF
            pdf_document.close()
            logger.info(f"[PDFImageConverter] PDF document closed")

            # Clean up the temporary file if it's not the original PDF
            if temp_path != pdf_path:
                try:
                    os.unlink(temp_path)
                    logger.info(f"[PDFImageConverter] Temporary file removed: {temp_path}")
                except Exception as e:
                    logger.warning(f"[PDFImageConverter] Failed to remove temporary file: {e}")
            else:
                logger.info(f"[PDFImageConverter] Not removing original PDF file: {temp_path}")

            logger.info(f"[PDFImageConverter] Conversion complete: {page_count} pages converted and saved locally")
            logger.info(f"[PDFImageConverter] Generated {len(image_paths)} main images and {len(cropped_image_paths)} cropped images")

            # Log a sample of the paths for debugging
            if image_paths:
                sample_paths = image_paths[:min(2, len(image_paths))]
                logger.info(f"[PDFImageConverter] Sample image paths: {sample_paths}")

            if cropped_image_paths:
                sample_cropped = cropped_image_paths[:min(2, len(cropped_image_paths))]
                logger.info(f"[PDFImageConverter] Sample cropped image paths: {sample_cropped}")

            self.track_progress(f"Completed conversion of {page_count} pages", 39)

            return {
                "status": "success",
                "message": f"Converted and saved {page_count} pages locally",
                "image_urls": image_paths,
                "cropped_image_urls": cropped_image_paths,
                "total_pages": page_count
            }

        except Exception as e:
            logger.error(f"[PDFImageConverter] Error during PDF conversion: {e}")
            return {
                "status": "error",
                "message": str(e)
            }

    def convert_specific_pages(self, pdf_path: str, book_id: str, chapter_id: str, res_id: str,
                              page_numbers: List[int], zoom: float = 2.0) -> dict:
        """
        Convert specific PDF pages to images and save locally

        Args:
            pdf_path: Path to the PDF file
            book_id: Book identifier
            chapter_id: Chapter identifier
            res_id: Resource identifier
            page_numbers: List of page numbers to convert (1-based)
            zoom: Zoom factor for better resolution (default: 2.0)

        Returns:
            dict: Result containing status and local file paths
        """
        try:
            logger.info(f"[PDFImageConverter] Starting specific pages conversion for book_id: {book_id}, chapter_id: {chapter_id}, res_id: {res_id}")
            logger.info(f"[PDFImageConverter] Converting pages: {page_numbers}")

            # Ensure local storage directory exists
            local_storage_path = os.path.join(config.PDF_PAGE_IMG_OUTPUT_DIR, str(book_id), str(chapter_id), str(res_id))
            os.makedirs(local_storage_path, exist_ok=True)

            # Check if the file exists locally first
            if os.path.isfile(pdf_path):
                logger.info(f"[PDFImageConverter] PDF file exists locally: {pdf_path}")
                self.track_progress(f"Using local PDF file: {pdf_path}", 32)
                temp_path = pdf_path
            else:
                # Try to get the file from S3
                logger.info(f"[PDFImageConverter] PDF file not found locally, trying S3")

                # Get the full S3 path
                full_s3_path = get_s3_path(pdf_path)
                logger.info(f"[PDFImageConverter] Full S3 mount path: {full_s3_path}")

                # Read the PDF file
                logger.info(f"[PDFImageConverter] Reading PDF from S3 mount path")
                self.track_progress(f"Reading PDF file: {pdf_path}", 32)

                # Read the file content
                self.track_progress(f"Reading PDF file from S3: {pdf_path}", 33)
                pdf_content = read_file_from_s3(full_s3_path)
                if pdf_content is None:
                    logger.error(f"[PDFImageConverter] CRITICAL ERROR: Failed to read PDF file from S3: {full_s3_path}")
                    self.track_progress(f"Failed to read PDF file from S3: {full_s3_path}", 100)
                    return {
                        "status": "error",
                        "message": f"Failed to read PDF file from S3: {full_s3_path}"
                    }

                # Create a temporary file to work with
                self.track_progress(f"Creating temporary file for PDF processing", 34)
                with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                    temp_path = temp_file.name
                    temp_file.write(pdf_content)
                    self.track_progress(f"PDF content written to temporary file", 35)

            # Open the PDF from the temporary file
            logger.info(f"[PDFImageConverter] Opening PDF from temporary file: {temp_path}")
            self.track_progress(f"Opening PDF document", 36)
            pdf_document = fitz.open(temp_path)

            # Store the page count
            total_page_count = pdf_document.page_count
            logger.info(f"[PDFImageConverter] PDF has {total_page_count} total pages")

            # Filter valid page numbers
            valid_page_numbers = [p for p in page_numbers if 1 <= p <= total_page_count]
            if not valid_page_numbers:
                logger.error(f"[PDFImageConverter] No valid page numbers provided. Valid range is 1-{total_page_count}")
                return {
                    "status": "error",
                    "message": f"No valid page numbers provided. Valid range is 1-{total_page_count}"
                }

            logger.info(f"[PDFImageConverter] Converting {len(valid_page_numbers)} pages: {valid_page_numbers}")
            self.track_progress(f"Converting {len(valid_page_numbers)} specific pages", 38)

            # Store file paths
            image_paths = []
            cropped_image_paths = []

            # Convert each specified page to image
            for i, page_number in enumerate(valid_page_numbers):
                # PDF pages are 0-indexed
                pdf_page_idx = page_number - 1

                progress = 40 + int((i / len(valid_page_numbers)) * 50)
                self.track_progress(f"Converting page {page_number} ({i+1}/{len(valid_page_numbers)})", progress)

                page = pdf_document[pdf_page_idx]
                mat = fitz.Matrix(zoom, zoom)
                pix = page.get_pixmap(matrix=mat)
                img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)

                # Store each page image with page number locally
                self.track_progress(f"Saving page {page_number} image", progress)
                local_img_path = os.path.join(local_storage_path, f"page_{page_number}.png")
                img.save(local_img_path, format="PNG")
                image_paths.append(local_img_path)

                # Crop the image using the updated margins
                self.track_progress(f"Cropping page {page_number}", progress)
                column_images = crop_image(img)

                # Process each column image separately
                for col_idx, col_img in enumerate(column_images):
                    # Save cropped image locally
                    self.track_progress(f"Saving cropped column {col_idx + 1} of page {page_number}", progress)
                    local_cropped_path = os.path.join(local_storage_path, f"page_{page_number}_col_{col_idx + 1}.png")
                    col_img.save(local_cropped_path, format="PNG")
                    cropped_image_paths.append(local_cropped_path)

            # Close the PDF
            pdf_document.close()
            logger.info(f"[PDFImageConverter] PDF document closed")

            # Clean up the temporary file if it's not the original PDF
            if temp_path != pdf_path:
                try:
                    os.unlink(temp_path)
                    logger.info(f"[PDFImageConverter] Temporary file removed: {temp_path}")
                except Exception as e:
                    logger.warning(f"[PDFImageConverter] Failed to remove temporary file: {e}")

            logger.info(f"[PDFImageConverter] Conversion complete: {len(valid_page_numbers)} pages converted and saved locally")
            logger.info(f"[PDFImageConverter] Generated {len(image_paths)} main images and {len(cropped_image_paths)} cropped images")

            return {
                "status": "success",
                "message": f"Converted and saved {len(valid_page_numbers)} specific pages locally",
                "image_urls": image_paths,
                "cropped_image_urls": cropped_image_paths,
                "total_pages": len(valid_page_numbers)
            }

        except Exception as e:
            logger.error(f"[PDFImageConverter] Error during specific pages conversion: {e}")
            return {
                "status": "error",
                "message": str(e)
            }


def extract_page_number(url):
    page_name = url.split('/')[-1]
    if '.' in page_name:
        page_id = page_name.split('.')[0]  # For main URLs
    else:
        page_id = page_name  # Just in case

    # Extract the number from page_X
    try:
        return int(page_id.split('_')[1])
    except (IndexError, ValueError):
        return 0


def extract_cropped_page_number(url):
    # Extract page number from paths like "/page_7/page_7_col_2.png"
    parts = url.split('/')
    for part in parts:
        if part.startswith('page_'):
            try:
                return int(part.split('_')[1])
            except (IndexError, ValueError):
                pass
    return 0


def extract_col_number(col_url):
    col_part = col_url.split('/')[-1].split('.')[0]  # Get "page_X_col_Y"
    try:
        return int(col_part.split('_')[-1])  # Extract Y from "col_Y"
    except (IndexError, ValueError):
        return 0


class PageNumberConverter:
    """Utility class to convert between PDF page numbers and actual page numbers"""

    @staticmethod
    def get_actual_page_number(pdf_page_number: int, starting_page_number: Optional[int] = None) -> int:
        """
        Convert PDF page number (0-based) to actual page number

        Args:
            pdf_page_number: 0-based PDF page number
            starting_page_number: Optional starting page number (1 if None)

        Returns:
            int: Actual page number
        """
        if starting_page_number is None:
            starting_page_number = 1

        return pdf_page_number + starting_page_number
