import json
import logging
logger = logging.getLogger(__name__)

def calculate_cost(usage_metadata):
    input_cost_per_million = 0.15
    cached_input_cost_per_million = 0.075
    output_cost_per_million = 0.60

    input_tokens = usage_metadata.get('input_tokens', 0)
    output_tokens = usage_metadata.get('output_tokens', 0)
    cache_read_tokens = usage_metadata.get('input_token_details', {}).get('cache_read', 0)

    normal_input_tokens = input_tokens - cache_read_tokens

    input_cost = (normal_input_tokens / 1_000_000) * input_cost_per_million
    cached_input_cost = (cache_read_tokens / 1_000_000) * cached_input_cost_per_million
    output_cost = (output_tokens / 1_000_000) * output_cost_per_million

    # Total cost
    total_cost = input_cost + cached_input_cost + output_cost
    return total_cost


def is_already_formatted(input_content):
    """
    Check if the JSON content is already in the expected format
    Returns True if already formatted, False if it needs formatting
    """
    try:
        # Try to parse as JSON first
        if isinstance(input_content, str):
            # Check if it looks like an escaped JSON string
            if input_content.startswith('"') and input_content.endswith('"'):
                return False  # It's an escaped string, needs formatting

            # Check if it contains escaped newlines
            if '\\n' in input_content and not '\n' in input_content:
                return False  # Contains escaped newlines, needs formatting

            # Try parsing as regular JSON
            data = json.loads(input_content)
            return True  # Successfully parsed as regular JSON
        else:
            return True  # Already a Python object
    except json.JSONDecodeError:
        return False  # Invalid JSON, might need formatting


def format_json_content(input_json_string):
    """
    Convert escaped JSON string to properly formatted JSON structure
    Handles invalid escape sequences commonly found in LaTeX expressions
    """
    try:
        # First try normal parsing
        data = json.loads(input_json_string)
    except json.JSONDecodeError as e:
        if "Invalid \\escape" in str(e):
            # Handle invalid escape sequences by using raw string literal
            try:
                # Try to fix common escape sequence issues
                fixed_string = fix_escape_sequences(input_json_string)
                data = json.loads(fixed_string)
            except json.JSONDecodeError:
                # Last resort: use ast.literal_eval for safer parsing
                import ast
                try:
                    # Convert to Python literal and then back to JSON
                    data = ast.literal_eval(input_json_string)
                except:
                    # If all else fails, try manual fixing
                    data = manual_json_fix(input_json_string)
        else:
            raise e

    # Format and return as properly indented JSON
    formatted_json = json.dumps(data, indent=2, ensure_ascii=False)
    return formatted_json


def fix_escape_sequences(json_string):
    """
    Fix common escape sequence issues in JSON strings
    """
    import re

    # Common fixes for LaTeX expressions
    fixes = [
        # Fix double backslashes that should be single
        (r'\\\\([a-zA-Z])', r'\\\1'),
        # Fix invalid escape sequences in LaTeX
        (r'\\([^"\\\/bfnrtu])', r'\\\\1'),  # Escape single backslashes
    ]

    fixed_string = json_string
    for pattern, replacement in fixes:
        fixed_string = re.sub(pattern, replacement, fixed_string)

    return fixed_string


def manual_json_fix(json_string):
    """
    Manual JSON parsing with escape sequence handling
    """
    import re

    # Try to extract and fix the JSON content
    # This is a simplified approach - you might need to customize based on your specific format
    try:
        # Replace problematic escape sequences
        fixed = json_string.replace('\\\\', '\\')  # Fix double backslashes
        fixed = re.sub(r'\\([^"\\\/bfnrtu])', r'\\\\\1', fixed)  # Escape single backslashes

        return json.loads(fixed)
    except:
        # If manual fix fails, return a basic structure
        return {
            "directions": [],
            "questions": [],
            "answerKeys": [],
            "explanations": [],
            "mappings": [],
            "explanationMappings": [],
            "error": "Failed to parse JSON due to escape sequence issues"
        }


def smart_format_json(input_content):
    """
    Intelligently format JSON content only if needed
    With enhanced error handling for escape sequences
    """
    if is_already_formatted(input_content):
        logger.info("JSON is already in the expected format. Using original content.")
        # If it's already formatted, just ensure proper indentation
        if isinstance(input_content, str):
            try:
                data = json.loads(input_content)
                return json.dumps(data, indent=2, ensure_ascii=False)
            except json.JSONDecodeError as e:
                logger.info(f"Warning: Error parsing supposedly formatted JSON: {e}")
                return format_json_content(input_content)  # Try fixing it
        else:
            return json.dumps(input_content, indent=2, ensure_ascii=False)
    else:
        logger.info("JSON needs formatting. Applying format_json_content.")
        return format_json_content(input_content)


# Enhanced version with better error handling
def safe_json_format(input_content):
    """
    Ultra-safe JSON formatting with comprehensive error handling
    """
    try:
        return smart_format_json(input_content)
    except Exception as e:
        logger.info(f"Error in smart_format_json: {e}")
        logger.info("Attempting alternative parsing methods...")

        # Try alternative methods
        try:
            # Method 1: Raw string parsing
            if isinstance(input_content, str) and input_content.startswith('{"'):
                import ast
                data = ast.literal_eval(input_content)
                return json.dumps(data, indent=2, ensure_ascii=False)
        except:
            pass

        try:
            # Method 2: Regex-based cleaning
            import re
            cleaned = re.sub(r'\\(?!["\\/bfnrtu])', r'\\\\', input_content)
            data = json.loads(cleaned)
            return json.dumps(data, indent=2, ensure_ascii=False)
        except:
            pass

        # If all methods fail, return original content with error info
        return {
            "error": f"Failed to parse JSON: {str(e)}",
            "directions": [],
            "questions": [],
            "answerKeys": [],
            "explanations": [],
            "mappings": [],
            "explanationMappings": []
        }