import os
import cv2
import base64
import numpy as np
import logging
from typing import List, Tuple, Dict
from PIL import Image, ImageEnhance
import aiohttp
import config
import external_api_endpoints

logger = logging.getLogger(__name__)


def crop_image(img: Image.Image) -> List[Image.Image]:
    resolution_factor = 2.0
    try:
        # Convert PIL Image to OpenCV format
        open_cv_image = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
        height, width = open_cv_image.shape[:2]

        # Log image dimensions for debugging
        logger.info(f"Processing image with dimensions: {width}x{height}")

        # Preprocess the image to get binary threshold
        thresh = preprocess_image(open_cv_image)

        # Detect column dividers
        dividers = detect_column_dividers(thresh, width, height)

        # Build column boundaries from dividers
        boundaries = build_column_boundaries(dividers, width)
        logger.info(f"Initial column boundaries: {boundaries}")

        # Validate and potentially split merged columns
        boundaries = validate_column_boundaries(boundaries, thresh, width)
        logger.info(f"Validated column boundaries: {boundaries}")

        # Extract columns
        columns = extract_columns(img, thresh, boundaries, width, height, resolution_factor)
        logger.info(f"Extracted {len(columns)} columns")

        # Validate columns - filter out very narrow columns
        min_width_ratio = 0.1  # Minimum width as a ratio of the original image width
        valid_columns = []

        for col in columns:
            col_width_ratio = col.width / (width * resolution_factor)
            if col_width_ratio >= min_width_ratio:
                valid_columns.append(col)

        # If we filtered out all columns, return the original image
        if not valid_columns and columns:
            logger.warning("All detected columns were too narrow, returning original image")
            return [img]

        # If we have valid columns, return them
        if valid_columns:
            # If we have too many columns (more than 3), keep only the largest ones
            # This is a safeguard against over-segmentation
            if len(valid_columns) > 3:
                logger.warning(f"Unusually high number of columns detected ({len(valid_columns)})")

                # Calculate the average column width
                avg_width = sum(col.width for col in valid_columns) / len(valid_columns)

                # If the average width is very small compared to the image width,
                # it might be incorrect segmentation
                if avg_width < (width * resolution_factor) / 5:
                    logger.warning("Average column width is too small, might be incorrect segmentation")

                    # Sort by size and keep the largest columns that make sense
                    valid_columns.sort(key=lambda col: col.width * col.height, reverse=True)

                    # Analyze the size distribution to determine how many columns to keep
                    sizes = [col.width * col.height for col in valid_columns]
                    size_ratios = [sizes[i] / sizes[0] for i in range(len(sizes))]

                    # Keep columns that are at least 40% the size of the largest column
                    # This is a heuristic to identify main content columns
                    significant_columns = [i for i, ratio in enumerate(size_ratios) if ratio >= 0.4]

                    if significant_columns:
                        keep_count = max(significant_columns) + 1
                        logger.info(f"Keeping {keep_count} significant columns based on size analysis")
                        valid_columns = valid_columns[:keep_count]

            logger.info(f"Returning {len(valid_columns)} valid columns")
            return valid_columns

        # If no columns were extracted, return the original image
        return [img]
    except Exception as e:
        logger.error(f"Error while cropping columns: {e}")
        # Return the original image in case of error
        return [img]


def preprocess_image(image: np.ndarray) -> np.ndarray:
    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Apply Gaussian blur to reduce noise
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)

    # Use adaptive thresholding for better results with varying lighting conditions
    try:
        # First try OTSU thresholding which works well for most documents
        _, thresh_otsu = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # Check if OTSU thresholding produced reasonable results
        white_pixel_ratio = np.sum(thresh_otsu) / (thresh_otsu.shape[0] * thresh_otsu.shape[1] * 255)

        # If OTSU thresholding produced too many or too few white pixels, try adaptive thresholding
        if white_pixel_ratio < 0.05 or white_pixel_ratio > 0.5:
            logger.info(f"OTSU thresholding produced {white_pixel_ratio:.2f} white pixel ratio, trying adaptive")
            thresh_adaptive = cv2.adaptiveThreshold(
                blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 11, 2
            )
            return thresh_adaptive

        return thresh_otsu
    except Exception as e:
        logger.error(f"Error in adaptive thresholding: {e}, falling back to basic thresholding")
        # Fallback to basic thresholding
        _, thresh = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY_INV)
        return thresh


def detect_column_dividers(thresh: np.ndarray, width: int, height: int) -> List[int]:
    # Calculate vertical projection (sum of white pixels in each column)
    vertical_projection = np.sum(thresh, axis=0)

    # Determine kernel size adaptively based on image width - use smaller kernel for better sensitivity
    kernel_size = max(3, width // 150)  # Reduced from width//100 to width//150

    # Smooth the projection using convolution
    smoothed = np.convolve(vertical_projection, np.ones(kernel_size) / kernel_size, mode='same')

    # Calculate statistics for adaptive thresholding
    mean_projection = np.mean(smoothed)
    std_projection = np.std(smoothed)

    # Use a more sensitive threshold ratio - lower value detects more potential dividers
    threshold_ratio = 0.6  # Reduced to detect more dividers
    threshold = mean_projection * threshold_ratio

    # Find valleys (potential column dividers)
    # Expand search area to consider more of the image
    search_start = width // 8  # Changed from width//6
    search_end = width * 7 // 8  # Changed from width*5//6

    valleys = []
    for i in range(search_start, search_end):
        # Check if this point is a local minimum
        if (smoothed[i] < smoothed[i - 1] and
            smoothed[i] < smoothed[i + 1] and
            smoothed[i] < threshold):

            # Calculate valley depth (how much lower than neighbors)
            left_depth = smoothed[i - 1] - smoothed[i]
            right_depth = smoothed[i + 1] - smoothed[i]
            depth = min(left_depth, right_depth)

            # Reduce the depth threshold to detect more subtle dividers
            if depth > mean_projection * 0.03:  # Reduced from 0.05
                valleys.append(i)

    # Log the number of valleys found for debugging
    logger.info(f"Found {len(valleys)} potential column dividers with threshold ratio {threshold_ratio:.2f}")

    # Detect vertical lines that could be column dividers
    vertical_lines = detect_vertical_lines(thresh, height)

    # Combine valleys with vertical lines
    combined_dividers = combine_valleys_and_lines(valleys, vertical_lines, width, height)

    return group_and_finalize_dividers(combined_dividers, smoothed, width, height, thresh)


def detect_vertical_lines(thresh: np.ndarray, height: int) -> List[int]:
    """
    Detect vertical lines in the image that could be column dividers.

    Args:
        thresh: Binary threshold image
        height: Height of the image

    Returns:
        List of x-coordinates of detected vertical lines
    """
    # Create a copy of the threshold image for line detection
    line_img = thresh.copy()
    width = line_img.shape[1]

    # Apply morphological operations to enhance vertical lines
    # Create a vertical kernel
    vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, height // 20))

    # Apply morphology operations to isolate vertical lines
    vertical_lines = cv2.morphologyEx(line_img, cv2.MORPH_OPEN, vertical_kernel)

    # Use Hough Line Transform to detect lines
    lines = cv2.HoughLinesP(
        vertical_lines,
        rho=1,
        theta=np.pi/180,
        threshold=height//10,  # Minimum line length as a fraction of height
        minLineLength=height * 0.5,  # Line must cover at least 50% of image height
        maxLineGap=height // 10  # Maximum allowed gap in the line
    )

    # Extract x-coordinates of vertical lines
    vertical_dividers = []
    if lines is not None:
        # Define edge margins - ignore lines near the edges
        edge_margin = max(width * 0.05, 50)  # 5% of width or 50 pixels, whichever is larger

        for line in lines:
            x1, y1, x2, y2 = line[0]
            # Check if the line is vertical (x1 is close to x2)
            if abs(x1 - x2) < 10:
                # Calculate the average x-coordinate
                x_coord = (x1 + x2) // 2

                # Ignore lines that are too close to the edges
                if x_coord < edge_margin or x_coord > width - edge_margin:
                    logger.info(f"Ignoring edge line at x={x_coord} (too close to page border)")
                    continue

                # Check if the line spans a significant portion of the image height
                line_height = abs(y2 - y1)
                if line_height > height * 0.7:  # Line must cover at least 70% of image height
                    vertical_dividers.append(x_coord)
                    logger.info(f"Found vertical line at x={x_coord} spanning {line_height/height:.2%} of image height")

    # Filter out lines that are too close to each other
    if len(vertical_dividers) > 1:
        vertical_dividers.sort()
        min_line_distance = width * 0.1  # Lines should be at least 10% of width apart

        filtered_dividers = [vertical_dividers[0]]
        for i in range(1, len(vertical_dividers)):
            if vertical_dividers[i] - filtered_dividers[-1] >= min_line_distance:
                filtered_dividers.append(vertical_dividers[i])

        # If we filtered out some lines, log it
        if len(filtered_dividers) < len(vertical_dividers):
            logger.info(f"Filtered out {len(vertical_dividers) - len(filtered_dividers)} lines that were too close to others")

        vertical_dividers = filtered_dividers

    # Check if the lines actually divide the content
    if vertical_dividers:
        # For each divider, check if there's content on both sides
        content_dividers = []
        for divider in vertical_dividers:
            # Define regions to check for content
            left_region = max(0, divider - width // 10)
            right_region = min(width, divider + width // 10)

            # Check content in left and right regions
            left_content = np.sum(thresh[:, max(0, left_region - width // 10):left_region])
            right_content = np.sum(thresh[:, right_region:min(width, right_region + width // 10)])

            # Only keep dividers that have content on both sides
            if left_content > 0 and right_content > 0:
                content_dividers.append(divider)
            else:
                logger.info(f"Ignoring line at x={divider} (no content on both sides)")

        vertical_dividers = content_dividers

    return vertical_dividers


def combine_valleys_and_lines(valleys: List[int], vertical_lines: List[int], width: int, height: int) -> List[int]:
    """
    Combine valleys from projection analysis with detected vertical lines.

    Args:
        valleys: List of valley positions from projection analysis
        vertical_lines: List of vertical line positions from line detection
        width: Width of the image
        height: Height of the image

    Returns:
        Combined list of potential divider positions
    """
    # If we have both valleys and vertical lines, prioritize vertical lines
    # that are close to valleys (these are stronger divider candidates)
    combined = []

    # If we have vertical lines, they are strong indicators of column dividers
    if vertical_lines:
        # Add all vertical lines as they are strong indicators
        combined.extend(vertical_lines)
        logger.info(f"Added {len(vertical_lines)} vertical lines as strong divider candidates")

        # Also add valleys that are not close to any vertical line
        # as they might indicate dividers without visible lines
        if valleys:
            proximity_threshold = width // 30
            for valley in valleys:
                # Check if this valley is far from all vertical lines
                if all(abs(valley - line) > proximity_threshold for line in vertical_lines):
                    combined.append(valley)
    else:
        # If no vertical lines were found, use valleys
        combined = valleys

    # Remove duplicates and sort
    if combined:
        combined = sorted(list(set(combined)))
        logger.info(f"Combined {len(combined)} potential dividers from valleys and vertical lines")

    return combined


def group_and_finalize_dividers(valleys: List[int], smoothed, width, height, thresh) -> List[int]:
    dividers = []

    # If valleys were found, group nearby valleys together
    if valleys:
        # Calculate adaptive grouping distance - use smaller distance to avoid merging distinct dividers
        grouping_distance = max(5, width // 50)  # Reduced from width//30

        # Sort valleys by position
        valleys.sort()

        # Group valleys that are close to each other
        current_group = [valleys[0]]

        for i in range(1, len(valleys)):
            if valleys[i] - valleys[i - 1] < grouping_distance:
                # Add to current group if close enough
                current_group.append(valleys[i])
            else:
                # Process the completed group
                if current_group:
                    # Find the deepest valley in the group (lowest value)
                    group_values = [smoothed[idx] for idx in current_group]
                    best_idx = current_group[np.argmin(group_values)]
                    dividers.append(best_idx)

                # Start a new group
                current_group = [valleys[i]]

        # Process the last group
        if current_group:
            group_values = [smoothed[idx] for idx in current_group]
            best_idx = current_group[np.argmin(group_values)]
            dividers.append(best_idx)

    # If we found dividers, validate them
    if dividers:
        # Calculate the minimum distance between dividers - reduce to allow closer dividers
        min_divider_distance = width // 15  # Changed from width//10

        # Filter dividers that are too close to each other
        if len(dividers) > 1:
            dividers.sort()
            filtered_dividers = [dividers[0]]

            for i in range(1, len(dividers)):
                if dividers[i] - filtered_dividers[-1] >= min_divider_distance:
                    filtered_dividers.append(dividers[i])

            dividers = filtered_dividers

    # Fallback 1: If no dividers found, analyze the middle third more carefully
    if not dividers:
        mid_start = width // 3
        mid_end = width * 2 // 3
        mid_region = smoothed[mid_start:mid_end]

        if len(mid_region) > 0:
            min_idx = mid_start + np.argmin(mid_region)
            mean_val = np.mean(smoothed)

            # Calculate the ratio of minimum to mean
            min_mean_ratio = smoothed[min_idx] / mean_val if mean_val > 0 else 1.0

            # Check for content on both sides of the potential divider
            left_content = np.sum(thresh[:, max(0, min_idx - width // 10):min_idx])
            right_content = np.sum(thresh[:, min_idx:min(width, min_idx + width // 10)])

            # Only use this divider if:
            # 1. It's significantly lower than the mean (deep valley)
            # 2. There's content on both sides
            # 3. The image is wide enough to potentially have columns
            if (min_mean_ratio < 0.3 and  # Deep valley
                left_content > 0 and right_content > 0 and  # Content on both sides
                width > 1000):  # Wide enough for columns

                # Check if there's a clear gap at this position
                # Extract a narrow vertical strip around the divider
                strip_width = max(3, width // 200)
                strip_start = max(0, min_idx - strip_width)
                strip_end = min(width, min_idx + strip_width)
                vertical_strip = thresh[:, strip_start:strip_end]

                # Calculate the percentage of white pixels in this strip
                white_pixel_ratio = np.sum(vertical_strip) / (vertical_strip.shape[0] * vertical_strip.shape[1] * 255)

                # Only use this divider if the strip has few white pixels (indicating a gap)
                if white_pixel_ratio < 0.1:
                    dividers = [min_idx]
                    logger.info(f"Using middle third minimum as divider at position {min_idx} with min/mean ratio: {min_mean_ratio:.2f}")
                else:
                    logger.info(f"Rejected middle third minimum: too many white pixels in strip ({white_pixel_ratio:.2f})")
            else:
                logger.info(f"Rejected middle third minimum: min/mean={min_mean_ratio:.2f}, left_content={left_content>0}, right_content={right_content>0}, width={width}")

    # We're now more conservative about adding dividers
    # Only add a content-based divider if there's strong evidence of multiple columns
    if not dividers:
        # Analyze content distribution
        left_half = thresh[:, :width // 2]
        right_half = thresh[:, width // 2:]

        left_content = np.sum(left_half)
        right_content = np.sum(right_half)
        total_content = left_content + right_content

        # Calculate content ratios
        if total_content > 0:
            left_ratio = left_content / total_content
            right_ratio = right_content / total_content

            # Check for horizontal spacing in the middle section
            middle_section = thresh[:, width * 2 // 5:width * 3 // 5]
            middle_projection = np.sum(middle_section, axis=0)

            # Calculate the mean and minimum values in the middle section
            if len(middle_projection) > 0:
                middle_mean = np.mean(middle_projection)
                middle_min = np.min(middle_projection)

                # Calculate the ratio of minimum to mean
                min_mean_ratio = middle_min / middle_mean if middle_mean > 0 else 1.0

                # Only add a divider if:
                # 1. Content is reasonably balanced between halves
                # 2. There's a significant gap in the middle section
                # 3. There's enough total content to make a reliable decision
                if (min(left_ratio, right_ratio) > 0.25 and
                    min_mean_ratio < 0.3 and  # Significant gap in the middle
                    total_content > width * height * 0.01):  # Enough content

                    # Find the best divider in the middle area
                    middle_start = width * 2 // 5
                    middle_end = width * 3 // 5
                    middle_region = smoothed[middle_start:middle_end]

                    if len(middle_region) > 0:
                        best_divider = middle_start + np.argmin(middle_region)
                        dividers = [best_divider]
                        logger.info(f"Using content-based divider at position {best_divider} with min/mean ratio: {min_mean_ratio:.2f}")
                else:
                    logger.info(f"No content-based divider added: balance={min(left_ratio, right_ratio):.2f}, gap={min_mean_ratio:.2f}")

    return dividers


def build_column_boundaries(dividers: List[int], width: int) -> List[Tuple[int, int]]:
    """
    Build column boundaries from divider positions.

    Args:
        dividers: List of divider positions
        width: Width of the image

    Returns:
        List of column boundaries as (start, end) tuples
    """
    if not dividers:
        # If no dividers found, return a single column
        # We no longer automatically split wide images into two columns
        # This prevents incorrect splitting of single-column pages
        return [(0, width)]

    # Sort dividers to ensure they're in order
    dividers.sort()

    # Create boundaries from dividers
    boundaries = [(0, dividers[0])]
    boundaries += [(dividers[i], dividers[i + 1]) for i in range(len(dividers) - 1)]
    boundaries.append((dividers[-1], width))

    return boundaries


def validate_columns(img: Image.Image, boundaries: List[Tuple[int, int]], width: int) -> List[Tuple[int, int]]:
    """Validate column boundaries by analyzing content distribution"""
    valid_boundaries = []

    # If only one boundary detected but image is wide, try to find a missed divider
    if len(boundaries) == 1 and width > 1000:
        start, end = boundaries[0]
        col_width = end - start

        # If column is very wide, it might be two merged columns
        if col_width > width * 0.7:
            # Convert to OpenCV format for analysis
            img_array = np.array(img)
            if len(img_array.shape) == 3:
                img_gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            else:
                img_gray = img_array

            # Analyze middle section for potential divider
            mid_point = (start + end) // 2
            search_width = col_width // 6
            left_section = max(start, mid_point - search_width)
            right_section = min(end, mid_point + search_width)

            # Check content density in middle section
            middle_section = img_gray[:, left_section:right_section]
            _, middle_thresh = cv2.threshold(middle_section, 200, 255, cv2.THRESH_BINARY)

            # Calculate vertical projection in middle section
            v_proj = np.sum(middle_thresh, axis=0)

            # Find potential divider in middle section
            if len(v_proj) > 0:
                # Smooth projection
                k_size = max(3, len(v_proj) // 10)
                smoothed = np.convolve(v_proj, np.ones(k_size) / k_size, mode='same')

                # Find minimum (potential divider)
                if len(smoothed) > 0:
                    divider_offset = np.argmax(smoothed)
                    divider_pos = left_section + divider_offset

                    # Split into two columns
                    valid_boundaries = [(start, divider_pos), (divider_pos, end)]
                    return valid_boundaries

    # Return original boundaries if no splitting was done
    return boundaries


def extract_columns(img, thresh, boundaries, width, height, factor):
    columns = []

    # Calculate minimum column width adaptively based on image width
    # For wider images, columns should be wider
    min_column_width_ratio = 0.08  # Minimum width as a percentage of total width
    min_column_width = max(width // 20, int(width * min_column_width_ratio))

    # Validate boundaries to ensure they make sense
    valid_boundaries = []
    for start, end in boundaries:
        # Skip very narrow columns
        if end - start < min_column_width:
            logger.info(f"Skipping narrow column: width {end - start} < {min_column_width}")
            continue

        # Ensure boundaries are within image dimensions
        start = max(0, start)
        end = min(width, end)

        if start < end:
            valid_boundaries.append((start, end))

    # If no valid boundaries, return the original image
    if not valid_boundaries:
        logger.warning("No valid column boundaries found, returning original image")
        return [img]

    # Process each valid column
    for i, (start, end) in enumerate(valid_boundaries):
        try:
            # Calculate horizontal projection for this column
            col_thresh = thresh[:, start:end]
            h_proj = np.sum(col_thresh, axis=1)
            non_zero = np.where(h_proj > 0)[0]

            # Calculate adaptive margins based on column width and image width
            col_width = end - start
            h_margin = max(20, min(col_width // 8, width // 20))  # Significantly increased margin

            # Apply different margin logic based on column position
            if start == 0:  # First column (left edge of image)
                # For first column, only add margin on the right side
                start_with_margin = 0
                end_with_margin = min(width, end + h_margin // 2)
            elif i == len(valid_boundaries) - 1:  # Last column
                # For last column, only add margin on the left side
                start_with_margin = max(0, start - h_margin)
                end_with_margin = width
            else:  # Middle columns
                # For middle columns, add margins on both sides
                # Add much more margin on the left to prevent cutting off content
                start_with_margin = max(0, start - h_margin * 2)  # Double the left margin for middle columns
                end_with_margin = min(width, end + h_margin // 2)

            # Determine top and bottom boundaries based on content
            if non_zero.size:
                # Add adaptive vertical margins
                v_margin = max(5, height // 50)
                top = max(0, non_zero[0] - v_margin)
                bottom = min(height - 1, non_zero[-1] + v_margin)

                # Ensure minimum height
                if bottom - top < height // 20:
                    padding = (height // 20 - (bottom - top)) // 2
                    top = max(0, top - padding)
                    bottom = min(height - 1, bottom + padding)
            else:
                # If no content found, use full height
                top, bottom = 0, height

            # Crop and enhance the column image
            col_img = img.crop((start_with_margin, top, end_with_margin, bottom))
            col_img = enhance_column_image(col_img, factor, end_with_margin - start_with_margin)

            # Validate the column - ensure it has enough content
            col_array = np.array(col_img)
            if len(col_array.shape) == 3 and col_array.shape[0] > 0 and col_array.shape[1] > 0:
                # Calculate the percentage of non-white pixels (rough content estimate)
                gray_col = cv2.cvtColor(col_array, cv2.COLOR_RGB2GRAY) if len(col_array.shape) == 3 else col_array
                non_white_ratio = np.sum(gray_col < 240) / (gray_col.shape[0] * gray_col.shape[1])

                # Only add columns with sufficient content
                if non_white_ratio > 0.01:  # At least 1% non-white pixels
                    columns.append(col_img)
                    logger.info(f"Extracted column: ({start_with_margin}, {top}) to ({end_with_margin}, {bottom})")
                else:
                    logger.info(f"Skipping column with insufficient content: {non_white_ratio:.4f} non-white ratio")
            else:
                # Add the column if we can't analyze its content
                columns.append(col_img)
                # Column extracted

        except Exception as e:
            logger.error(f"Error extracting column {start}-{end}: {e}")
            # If extraction fails, try a simpler approach
            try:
                # Use much wider margins in the fallback approach
                fallback_start = max(0, start - width // 15)  # More aggressive margin
                fallback_end = min(width, end + width // 20)
                simple_col = img.crop((fallback_start, 0, fallback_end, height))
                columns.append(simple_col)
                logger.info(f"Used fallback extraction for column {start}-{end}")
            except Exception as e2:
                logger.error(f"Fallback extraction also failed: {e2}")

    # Validate the results - if no columns were extracted, return the original image
    if not columns:
        logger.warning("No columns were successfully extracted, returning original image")
        return [img]

    return columns


def enhance_column_image(col, factor, width):
    col = ImageEnhance.Sharpness(col).enhance(2.0)
    col = ImageEnhance.Contrast(col).enhance(1.5)
    return col.resize((int(width * factor), int(col.height * factor)), Image.LANCZOS)


def encode_image_to_base64(image_path):
    try:
        cropped_path = image_path
        with open(cropped_path, "rb") as f:
            return base64.b64encode(f.read()).decode("utf-8")
    except Exception as e:
        logger.error(f"❌ Error encoding {image_path} to base64: {e}")
        return None


def crop_whitespace(image_path, output_path=None, debug=False):
    try:
        img = cv2.imread(image_path)
        if img is None:
            logger.error(f"❌ Failed to read image at {image_path}")
            return None

        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        kernel = np.ones((5, 5), np.uint8)
        thresh = cv2.dilate(thresh, kernel, iterations=2)

        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if not contours:
            logger.warning("⚠️ No contours found in the image")
            return image_path

        x_min, y_min = img.shape[1], img.shape[0]
        x_max, y_max = 0, 0
        for c in contours:
            x, y, w, h = cv2.boundingRect(c)
            x_min, y_min = min(x_min, x), min(y_min, y)
            x_max, y_max = max(x_max, x + w), max(y_max, y + h)

        margin = 10
        x_min = max(0, x_min - margin)
        y_min = max(0, y_min - margin)
        x_max = min(img.shape[1], x_max + margin)
        y_max = min(img.shape[0], y_max + margin)

        cropped = img[y_min:y_max, x_min:x_max]
        enhanced = cv2.resize(cropped, (int(cropped.shape[1]*2), int(cropped.shape[0]*2)), interpolation=cv2.INTER_CUBIC)
        sharpen = np.array([[-1, -1, -1], [-1, 9, -1], [-1, -1, -1]])
        enhanced = cv2.filter2D(enhanced, -1, sharpen)

        if output_path is None:
            filename, ext = os.path.splitext(image_path)
            output_path = f"{filename}_cropped_hires{ext}"

        cv2.imwrite(output_path, enhanced, [int(cv2.IMWRITE_JPEG_QUALITY), 100])
        logger.info(f"✓ Saved high-resolution cropped image to {output_path}")

        if debug:
            debug_dir = os.path.dirname(output_path) or "."
            cv2.imwrite(f"{debug_dir}/debug_threshold.png", thresh)
            dbg_img = img.copy()
            cv2.rectangle(dbg_img, (x_min, y_min), (x_max, y_max), (0, 255, 0), 2)
            cv2.imwrite(f"{debug_dir}/debug_bbox.png", dbg_img)

        return output_path
    except Exception as e:
        logger.error(f"❌ Error cropping image: {e}")
        return None


def analyze_horizontal_spacing(col_region: np.ndarray) -> List[int]:
    """
    Analyze horizontal spacing between text blocks to identify potential column dividers.

    Args:
        col_region: Binary image of a column region

    Returns:
        List of x-coordinates of significant horizontal gaps
    """
    # Get dimensions
    height, width = col_region.shape

    # Calculate vertical projection (sum of white pixels in each column)
    v_proj = np.sum(col_region, axis=0)

    # Smooth the projection
    kernel_size = max(3, width // 30)
    smoothed = np.convolve(v_proj, np.ones(kernel_size) / kernel_size, mode='same')

    # Calculate the mean and standard deviation
    mean_val = np.mean(smoothed)
    std_val = np.std(smoothed)

    # Find regions with significantly less content (potential gaps)
    # A gap is defined as a region where the projection is significantly below the mean
    gap_threshold = mean_val * 0.3  # 30% of the mean

    # Find continuous regions below the threshold
    gaps = []
    in_gap = False
    gap_start = 0
    min_gap_width = width // 20  # Minimum width for a gap to be considered significant

    for i in range(width):
        if smoothed[i] < gap_threshold:
            if not in_gap:
                in_gap = True
                gap_start = i
        else:
            if in_gap:
                in_gap = False
                gap_end = i
                gap_width = gap_end - gap_start

                # Only consider gaps that are wide enough
                if gap_width >= min_gap_width:
                    # Use the middle of the gap as the divider position
                    gap_middle = gap_start + gap_width // 2
                    gaps.append(gap_middle)

    # Handle case where gap extends to the end of the region
    if in_gap:
        gap_end = width
        gap_width = gap_end - gap_start
        if gap_width >= min_gap_width:
            gap_middle = gap_start + gap_width // 2
            gaps.append(gap_middle)

    # Filter gaps to ensure they're well-distributed
    if len(gaps) > 1:
        # Sort gaps
        gaps.sort()

        # Ensure gaps are not too close to each other
        min_gap_distance = width // 10
        filtered_gaps = [gaps[0]]

        for i in range(1, len(gaps)):
            if gaps[i] - filtered_gaps[-1] >= min_gap_distance:
                filtered_gaps.append(gaps[i])

        gaps = filtered_gaps

    # Log the gaps found
    if gaps:
        logger.info(f"Found {len(gaps)} significant horizontal gaps at positions: {gaps}")

    return gaps


def validate_column_boundaries(boundaries: List[Tuple[int, int]], thresh: np.ndarray, width: int) -> List[Tuple[int, int]]:
    """
    Validate column boundaries and split columns that might be merged.

    Args:
        boundaries: List of column boundaries as (start, end) tuples
        thresh: Binary threshold image
        width: Width of the image

    Returns:
        List of validated column boundaries
    """
    validated_boundaries = []
    height = thresh.shape[0]

    # If we only have one boundary (the whole image), analyze it for potential columns
    if len(boundaries) == 1 and boundaries[0] == (0, width):
        # Check for vertical lines that might indicate columns
        vertical_lines = detect_vertical_lines(thresh, height)

        # If vertical lines were found, use them to create column boundaries
        if vertical_lines:
            logger.info(f"Found {len(vertical_lines)} vertical lines in single-column validation")
            # Create new boundaries based on vertical lines
            new_boundaries = build_column_boundaries(vertical_lines, width)
            # If we found valid boundaries, use them instead
            if len(new_boundaries) > 1:
                logger.info(f"Splitting single column into {len(new_boundaries)} columns based on vertical lines")
                return new_boundaries

        # If we didn't find vertical lines, check for horizontal spacing
        horizontal_gaps = analyze_horizontal_spacing(thresh)

        # If significant horizontal gaps were found, use them to split the column
        if horizontal_gaps:
            logger.info(f"Found {len(horizontal_gaps)} significant horizontal gaps in full-page analysis")

            # Only use gaps that are in the middle section of the page
            middle_gaps = [gap for gap in horizontal_gaps if width // 4 < gap < width * 3 // 4]

            if middle_gaps:
                logger.info(f"Found {len(middle_gaps)} gaps in the middle section of the page")

                # Create new boundaries based on gaps
                if len(middle_gaps) == 1:
                    # Split into two columns
                    gap_pos = middle_gaps[0]

                    # Check if there's content on both sides of the gap
                    left_content = np.sum(thresh[:, :gap_pos])
                    right_content = np.sum(thresh[:, gap_pos:])

                    # Only split if there's significant content on both sides
                    if left_content > 0 and right_content > 0:
                        # Check if the gap is a true divider by looking at the vertical strip
                        strip_width = max(3, width // 200)
                        strip_start = max(0, gap_pos - strip_width)
                        strip_end = min(width, gap_pos + strip_width)
                        vertical_strip = thresh[:, strip_start:strip_end]

                        # Calculate the percentage of white pixels in this strip
                        white_pixel_ratio = np.sum(vertical_strip) / (vertical_strip.shape[0] * vertical_strip.shape[1] * 255)

                        # Only use this divider if the strip has few white pixels (indicating a gap)
                        if white_pixel_ratio < 0.05:
                            logger.info(f"Splitting page at gap position {gap_pos} with white pixel ratio {white_pixel_ratio:.2f}")
                            return [(0, gap_pos), (gap_pos, width)]
                        else:
                            logger.info(f"Rejected gap at {gap_pos}: too many white pixels in strip ({white_pixel_ratio:.2f})")
                    else:
                        logger.info(f"Rejected gap at {gap_pos}: not enough content on both sides")

        # If we reach here, we couldn't find any valid reason to split the page
        # Return the original single-column boundary
        logger.info("No valid reason to split the page, keeping as a single column")
        return boundaries

    # Process each boundary
    for start, end in boundaries:
        col_width = end - start

        # Skip very narrow columns
        if col_width < width * 0.05:
            logger.info(f"Skipping very narrow column: {start}-{end} (width: {col_width})")
            continue

        # If this column is very wide (more than 60% of image width), analyze it further
        if col_width > width * 0.6 and width > 800:
            # Extract this column region
            col_region = thresh[:, start:end]

            # Check for horizontal spacing between text blocks
            horizontal_gaps = analyze_horizontal_spacing(col_region)

            # If significant horizontal gaps were found, use them to split the column
            if horizontal_gaps:
                # Convert gap positions to image coordinates
                gap_positions = [start + gap for gap in horizontal_gaps]
                logger.info(f"Found {len(horizontal_gaps)} significant horizontal gaps in column {start}-{end}")

                # Create new boundaries based on gaps
                if len(gap_positions) == 1:
                    # Split into two columns
                    gap_pos = gap_positions[0]
                    validated_boundaries.append((start, gap_pos))
                    validated_boundaries.append((gap_pos, end))
                    continue
                elif len(gap_positions) > 1:
                    # Split into multiple columns
                    validated_boundaries.append((start, gap_positions[0]))
                    for i in range(len(gap_positions) - 1):
                        validated_boundaries.append((gap_positions[i], gap_positions[i + 1]))
                    validated_boundaries.append((gap_positions[-1], end))
                    continue

            # If no horizontal gaps were found, try the original method
            # Calculate vertical projection for just this column
            col_projection = np.sum(col_region, axis=0)

            # Smooth the projection
            kernel_size = max(3, col_width // 50)
            smoothed = np.convolve(col_projection, np.ones(kernel_size) / kernel_size, mode='same')

            # Find potential divider in the middle section of this column
            mid_start = col_width // 4
            mid_end = col_width * 3 // 4

            if mid_end > mid_start:
                mid_region = smoothed[mid_start:mid_end]
                if len(mid_region) > 0:
                    # Find the minimum point in the middle region
                    min_idx = mid_start + np.argmin(mid_region)

                    # Only use this divider if it's a significant valley
                    mean_val = np.mean(smoothed)
                    if smoothed[min_idx] < mean_val * 0.7:
                        # Split this column at the detected divider
                        divider_pos = start + min_idx
                        validated_boundaries.append((start, divider_pos))
                        validated_boundaries.append((divider_pos, end))
                        continue

        # If we didn't split the column, add it as is
        validated_boundaries.append((start, end))

    return validated_boundaries
