# answer_key_validator.py

import re
import logging
from openai import OpenAI

import config

# Configure logging
logger = logging.getLogger(__name__)


class AnswerKeyValidator:
    """
    This class is responsible for validating extracted MCQ answer keys using OpenAI LLM.

    Workflow:
    1. Parse the raw answer key string into a structured dictionary.
    2. Iterate through each MCQ and compare it with the provided answer key.
    3. Use OpenAI LLM to check whether the provided answer is actually correct.
    4. Log all responses and return list of question numbers where answer keys mismatch.
    """

    def __init__(self, mcq_list, answer_key_string, base_url, openai_model="gpt-4o-mini"):
        """
        Initialize the validator with MCQs, raw answer keys, and config.

        Args:
            mcq_list (List[dict]): List of MCQs in structured format.
            answer_key_string (str): Raw answer key string as extracted.
            base_url (str): Base URL to prepend to image paths.
            openai_model (str): Model to use for OpenAI API calls.
        """
        self.mcq_list = mcq_list
        self.answer_key_string = answer_key_string
        self.base_url = base_url
        self.model = openai_model
        self.client = OpenAI(api_key=config.OPENAI_API_KEY_ADMIN)

        logger.info("Initializing AnswerKeyValidator...")
        self.answer_dict = self._parse_answer_keys()
        logger.info(f"Parsed {len(self.answer_dict)} answers from answer key string.")
        logger.info(f"Parsed answer keys:{self.answer_dict}")


    def _parse_answer_keys(self):
        """
        Parses the raw answer key string into a dictionary mapping question number to answer option.

        Returns:
            dict: {question_number (int): selected_option (str)}
        """
        pattern = r"(\d+)\.\s*\((\d+)\)"
        matches = re.findall(pattern, self.answer_key_string)

        if not matches:
            logger.warning("No valid answers found in the answer key string.")

        parsed_answers = {int(q_num): option for q_num, option in matches}
        logger.info(f"Parsed answers: {parsed_answers}")
        return parsed_answers


    def validate_answers(self):
        """
        Validates each extracted answer key by checking with the LLM whether the given answer is correct.

        Returns:
            List[int]: List of question numbers where LLM says the provided answer key is incorrect.
        """
        mismatched_questions = []

        for mcq in self.mcq_list:
            q_number = mcq.get("question_number")
            question_text = mcq.get("question_text", "").strip()
            question_images = mcq.get("question_images", [])
            option_images = mcq.get("option_images", {})

            extracted_answer = self.answer_dict.get(q_number)
            if not extracted_answer:
                logger.warning(f"Answer not found for question number: {q_number}. Skipping.")
                continue

            prompt = self._build_prompt(
                question_text,
                question_images,
                option_images,
                extracted_answer,
                q_number
            )

            try:
                assistant_answer, extracted_from_llm, match_status = self._ask_llm(prompt)
                if match_status.lower() != "yes":
                    mismatched_questions.append(q_number)
                    logger.info(f"Q{q_number} - MISMATCH: Extracted={extracted_from_llm}, Assistant Answer={assistant_answer}")
                else:
                    logger.info(f"Q{q_number} - MATCH: Extracted={extracted_from_llm}, Assistant Answer={assistant_answer}")
            except Exception as e:
                logger.error(f"LLM call failed for Q{q_number}: {str(e)}")

        return mismatched_questions


    def _build_prompt(self, question_text, question_images, option_images, answer_option, question_number):
        """
        Builds a complete prompt string with question, options, images and selected answer.

        Args:
            question_text (str): Text of the MCQ.
            question_images (List[str]): Image URLs for the question.
            option_images (Dict[str, List[str]]): Image URLs for options.
            answer_option (str): Extracted answer option (e.g., "1", "2")

        Returns:
            str: Final prompt to send to OpenAI.
        """
        question_images_text = ""
        if question_images:
            question_images_text = "\n[Attached Question Images]:\n" + "\n".join(
                f"{self.base_url}{img_url}" for img_url in question_images
            )

        option_images_text = ""
        if option_images:
            option_images_text = "\n[Attached Option Images]:\n"
            for opt, img_list in option_images.items():
                for img_url in img_list:
                    option_images_text += f"{opt}: {self.base_url}{img_url}\n"

        prompt = f"""You are an AI assistant that solves multiple-choice questions (MCQs) independently. An MCQ has a question and options. These options denote the answers to the question. In order to answer the question in the MCQ, corret option needs to be chosen.

    Your task:
    1. Read the question and its options carefully.
    2. The questions and options in the MCQ have formula in Latex format.
    2. Solve the question using your own reasoning to determine the correct option.
    3. Compare the option you arrived at with the Extracted Option provided below.
    4. Return the option which you arrived at along with the extracted option provided below and Yes or No based on whether the options match or not, in the following format exactly:

    Assistant's Answer: <option number>
    Extracted Answer: <option number>
    Match: Yes or No

    Rules:
    - Think, solve the question and choose the option yourself, correctly.
    - Do not assume the extracted answer is correct.
    - Respond only in the above format. No explanations. No punctuation.

    ---

    Question:
    {question_text}
    {question_images_text}
    {option_images_text}

    Extracted Option: ({answer_option})
    """

        logger.info(f"Built prompt for question #{question_number}: {prompt}...")
        return prompt


    def _ask_llm(self, prompt):
        """
        Sends the prompt to OpenAI LLM and parses the response into structured fields.

        Returns:
            Tuple[str, str, str]: (assistant_answer, extracted_answer, match_status)
                - assistant_answer (str): The option number LLM solved and selected (e.g., "2")
                - extracted_answer (str): The answer option we provided (e.g., "3")
                - match_status (str): "Yes" or "No"
        """
        response = self.client.chat.completions.create(
            model=self.model,
            messages=[
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            temperature=0
        )

        content = response.choices[0].message.content.strip()
        logger.info(f"Raw LLM response:\n{content}")

        # Regex parsing
        assistant_ans_match = re.search(r"Assistant's Answer:\s*(\d+)", content)
        extracted_ans_match = re.search(r"Extracted Answer:\s*(\d+)", content)
        match_status_match = re.search(r"Match:\s*(Yes|No)", content, re.IGNORECASE)

        assistant_answer = assistant_ans_match.group(1) if assistant_ans_match else None
        extracted_answer = extracted_ans_match.group(1) if extracted_ans_match else None
        match_status = match_status_match.group(1).capitalize() if match_status_match else "No"

        # Log parsed values
        logger.info(f"Parsed LLM Response - Assistant: {assistant_answer}, Extracted: {extracted_answer}, Match: {match_status}")

        return assistant_answer, extracted_answer, match_status
