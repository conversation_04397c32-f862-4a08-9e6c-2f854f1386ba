# File: agents/base/agent_interface.py

from typing import Dict, Any
from abc import ABC, abstractmethod


class AgentInterface(ABC):
    """Base interface that all agents must implement"""

    @abstractmethod
    async def process(self, *args: Any, **kwargs: Any) -> Dict:
        """
        Process method that all agents must implement

        Args:
            *args: Variable length argument list
            **kwargs: Arbitrary keyword arguments

        Returns:
            Dict containing the processing result with at least:
            {
                "status": str,  # "success" or "error"
                "message": Optional[str],  # Error message if status is "error"
                "data": Optional[Any]  # Process result if status is "success"
            }
        """
        raise NotImplementedError

    async def validate_response(self, response: Dict) -> bool:
        """
        Validate the response format

        Args:
            response: Response dictionary to validate

        Returns:
            bool: True if response is valid, False otherwise
        """
        required_keys = {"status"}
        return all(key in response for key in required_keys)