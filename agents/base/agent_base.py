# agents/base/agent_base.py

from abc import ABC, abstractmethod
from typing import Any


class AgentBase(ABC):
    """Base agent class defining common interface"""

    @abstractmethod
    def process(self, *args: Any, **kwargs: Any) -> Any:
        """
        Abstract method that all agents must implement

        Args:
            *args: Variable length argument list
            **kwargs: Arbitrary keyword arguments

        Returns:
            Any: Process result
        """
        pass
