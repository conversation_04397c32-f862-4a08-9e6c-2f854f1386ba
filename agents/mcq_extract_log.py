"""
Module for handling MCQ extraction log operations.
"""

import logging
import uuid
import datetime
from contextlib import contextmanager
from typing import Optional

from db_config.db import get_engine, LOG_SCHEMA

# Get logger instance
logger = logging.getLogger(__name__)

class MCQExtractLog:
    """
    Class for handling MCQ extraction log operations.
    """

    @contextmanager
    def get_connection(self):
        """
        Context manager to get a direct database connection.
        This ensures the connection is properly closed after use.
        """
        # Get the engine
        engine = get_engine(LOG_SCHEMA)

        # Create a new connection
        connection = engine.raw_connection()
        try:
            cursor = connection.cursor()
            yield cursor
            connection.commit()
            logger.info("Transaction committed successfully")
        except Exception as e:
            connection.rollback()
            logger.error(f"Transaction rolled back due to error: {e}")
            raise e
        finally:
            cursor.close()
            connection.close()
            logger.info("Database connection closed")

    def create_extraction_log(self, res_id: str, username: str) -> str:
        """
        Create a new extraction log record with status "inprogress".

        Args:
            res_id: Resource ID
            username: Username of the user performing the extraction

        Returns:
            str: Generated UUID for the extraction
        """
        try:
            # Generate a UUID for the extraction
            task_id = str(uuid.uuid4())

            # Get current time
            start_time = datetime.datetime.now()

            # Insert a new record
            with self.get_connection() as cursor:
                insert_query = f"""
                    INSERT INTO {LOG_SCHEMA}.mcq_extract_log
                    (version, end_time, res_id, start_time, status, task_id, username)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """

                # Log the query
                logger.info(f"Executing insert query: {insert_query} with params: (0, NULL, {res_id}, {start_time}, 'inprogress', {task_id}, {username})")

                # Execute the query
                cursor.execute(insert_query, (0, None, res_id, start_time, 'inprogress', task_id, username))

                # Log the result
                rows_affected = cursor.rowcount
                logger.info(f"Insert query affected {rows_affected} rows")

            return task_id
        except Exception as e:
            logger.error(f"Error creating extraction log: {e}")
            return None

    def update_extraction_log(self, task_id: str, status: str = "completed") -> bool:
        """
        Update an existing extraction log record.

        Args:
            task_id: Generated UUID for the extraction
            status: Status to set (default: "completed")

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get current time
            end_time = datetime.datetime.now()

            # Update the record
            with self.get_connection() as cursor:
                update_query = f"""
                    UPDATE {LOG_SCHEMA}.mcq_extract_log
                    SET status = %s, end_time = %s, version = version + 1
                    WHERE task_id = %s
                """

                # Log the query
                logger.info(f"Executing update query: {update_query} with params: ({status}, {end_time}, {task_id})")

                # Execute the query
                cursor.execute(update_query, (status, end_time, task_id))

                # Log the result
                rows_affected = cursor.rowcount
                logger.info(f"Update query affected {rows_affected} rows")

                if rows_affected == 0:
                    logger.warning(f"No record found with task_id: {task_id}")
                    return False

            return True
        except Exception as e:
            logger.error(f"Error updating extraction log: {e}")
            return False

    def get_extraction_status(self, task_id: str) -> dict:
        """
        Get the status of an extraction.

        Args:
            task_id: Generated UUID for the extraction

        Returns:
            dict: Extraction status details
        """
        try:
            with self.get_connection() as cursor:
                query = f"""
                    SELECT res_id, start_time, end_time, status, username, version
                    FROM {LOG_SCHEMA}.mcq_extract_log
                    WHERE task_id = %s
                    LIMIT 1
                """

                # Log the query
                logger.info(f"Executing query: {query} with params: ({task_id},)")

                # Execute the query
                cursor.execute(query, (task_id,))

                # Fetch the result
                result = cursor.fetchone()

                if not result:
                    logger.warning(f"No extraction log found with task_id: {task_id}")
                    return {"status": "not_found", "message": f"No extraction log found with ID {task_id}"}

                # Extract the result
                res_id, start_time, end_time, status, username, version = result

                # Format the response
                response = {
                    "task_id": task_id,
                    "res_id": res_id,
                    "start_time": start_time.isoformat() if start_time else None,
                    "end_time": end_time.isoformat() if end_time else None,
                    "status": status,
                    "username": username,
                    "version": version
                }

                return response
        except Exception as e:
            logger.error(f"Error getting extraction status: {e}")
            return {"status": "error", "message": f"Error getting extraction status: {str(e)}"}
