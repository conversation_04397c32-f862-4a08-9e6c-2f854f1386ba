"""
Service for pattern extraction functionality.
"""

import os
import logging
import tempfile
import base64
import asyncio
from typing import List, Tuple, Optional, Any
from fastapi import UploadFile

from llm_manager.llm_factory import LLMFactory
from config import llm_config
from langchain_core.messages import HumanMessage
from langchain_core.prompts import ChatPromptTemplate
from agents.utils.image_utils import encode_image_to_base64

# Initialize logger
logger = logging.getLogger(__name__)

# Initialize LLM factory
llm_factory = LLMFactory(llm_config)


async def create_solution_with_sample(
    sample_text: Optional[str] = None,
    sample_images: Optional[List[Any]] = None,
    question_text: Optional[str] = None,
    question_images: Optional[List[Any]] = None
) -> str:
    """
    Create a solution using a sample question/solution and a new question.

    Args:
        sample_text: Text input for the sample question/solution
        sample_images: List of image files for the sample question/solution
        question_text: Text input for the question to solve
        question_images: List of image files for the question to solve

    Returns:
        Generated solution with LaTeX formatting
    """
    logger.info(f"Creating solution with sample and question inputs")

    # Get LLM with temperature 0.1
    llm = llm_factory.get_llm("openai", "gpt-4.1-mini")

    # Generate solution based on input types
    if sample_text and question_text:
        # Both inputs are text
        solution = await _generate_solution_with_text_sample(sample_text, question_text, llm)
    elif sample_images and question_text:
        # Sample is images, question is text
        solution = await _generate_solution_with_image_sample_text_question(sample_images, question_text, llm)
    elif sample_text and question_images:
        # Sample is text, question is images
        solution = await _generate_solution_with_text_sample_image_question(sample_text, question_images, llm)
    elif sample_images and question_images:
        # Both inputs are images
        solution = await _generate_solution_with_image_sample_image_question(sample_images, question_images, llm)
    else:
        # This should not happen due to validation in the API endpoint
        raise ValueError("Invalid input combination")

    logger.info(f"Generated solution: {solution[:100]}...")

    return solution


async def _generate_solution_with_text_sample(sample_text: str, question_text: str, llm) -> str:
    """
    Generate a solution using a text sample and text question.

    Args:
        sample_text: Text content of the sample question/solution
        question_text: Text content of the question to solve
        llm: LLM instance

    Returns:
        Generated solution with LaTeX formatting
    """
    # Parse sample text to extract question and solution
    sample_parts = sample_text.split("\n\nSolution:\n", 1)
    sample_question = sample_parts[0].strip()
    sample_solution = sample_parts[1].strip() if len(sample_parts) > 1 else sample_text

    # Create prompt template with the exact specified prompt
    prompt_template = ChatPromptTemplate.from_template(
        """
        You are an editorial assistant for a textbook publisher.
        Your only job is to imitate the exact style, wording, notation,
        language, and brevity of the SAMPLE SOLUTION that follows.
        * Preserve every LaTeX/MathML element exactly as shown—no conversion.
        * Do not add headings like "Step 1", "Answer:", or explanatory text.
        * Use the minimum words/symbols needed for a complete, correct answer.
        * Output the solution text only—nothing else.

        SAMPLE QUESTION
        {sample_question}

        SAMPLE SOLUTION
        {sample_solution}

        NEW QUESTION
        {new_question}
        
        Instructions:
        - Create solution for NEW QUESTION in the style of SAMPLE SOLUTION.
        - If correction option is mentioned in the sample follow the same structure.
            For Example: 
            - If the solution starts with  (2) Enhance (Verb)... follow the same
            - If the solution starts with Option (A) is correct... follow the same
        TASK
        Write the solution for NEW QUESTION in the identical style and language of SAMPLE SOLUTION.
        """
    )

    # Format the prompt
    formatted_prompt = prompt_template.format_messages(
        sample_question=sample_question,
        sample_solution=sample_solution,
        new_question=question_text
    )

    # Call LLM
    response = llm.invoke(formatted_prompt)

    return response.content


async def _generate_solution_with_image_sample_text_question(sample_images: List[Any], question_text: str, llm) -> str:
    """
    Generate a solution using image sample and text question.

    Args:
        sample_images: List of image files for the sample question/solution
        question_text: Text content of the question to solve
        llm: LLM instance

    Returns:
        Generated solution with LaTeX formatting
    """
    # Save images to temporary files
    image_paths = []
    try:
        for image in sample_images:
            with tempfile.NamedTemporaryFile(delete=False, suffix=".jpg") as temp_file:
                # Handle different types of file objects
                if hasattr(image, 'seek') and callable(getattr(image, 'seek')):
                    # Reset file position to beginning if possible
                    try:
                        if asyncio.iscoroutinefunction(image.seek):
                            await image.seek(0)
                        else:
                            image.seek(0)
                    except Exception as e:
                        logger.warning(f"Error seeking file: {e}")

                # Handle different types of file objects
                if hasattr(image, 'read'):
                    # If it's a file-like object with a read method
                    content = await image.read() if callable(getattr(image, 'read')) and asyncio.iscoroutinefunction(image.read) else image.read()
                    temp_file.write(content)
                elif isinstance(image, bytes):
                    # If it's already bytes
                    temp_file.write(image)
                else:
                    # Try to convert to string and then bytes
                    temp_file.write(str(image).encode('utf-8'))

                image_paths.append(temp_file.name)
                logger.info(f"Saved sample image to temporary file: {temp_file.name}")

        # Construct content for LLM
        content = [
            {
                "type": "text",
                "text": """
                You are an editorial assistant for a textbook publisher.
                Your only job is to imitate the exact style, wording, notation,
                language, and brevity of the SAMPLE SOLUTION that follows.
                * Preserve every LaTeX/MathML element exactly as shown—no conversion.
                * Do not add headings like "Step 1", "Answer:", or explanatory text.
                * Use the minimum words/symbols needed for a complete, correct answer.
                * Output the solution text only—nothing else.

                SAMPLE QUESTION
                (See the attached images)

                SAMPLE SOLUTION
                (See the attached images)

                NEW QUESTION
                """ + question_text + """
                
                Instructions:
                - Create solution for NEW QUESTION in the style of SAMPLE SOLUTION.
                - If correction option is mentioned in the sample follow the same structure.
                    For Example: 
                    - If the solution starts with  (2) Enhance (Verb)... follow the same
                    - If the solution starts with Option (A) is correct... follow the same
                TASK
                Write the solution for NEW QUESTION in the identical style and language of SAMPLE SOLUTION.
                """
            }
        ]

        # Add images to content
        for path in image_paths:
            content.append({
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{encode_image_to_base64(path)}",
                    "detail": "high"
                }
            })

        # Call LLM
        response = llm.invoke([HumanMessage(content=content)])

        return response.content

    finally:
        # Clean up temporary files
        for path in image_paths:
            if os.path.exists(path):
                try:
                    os.unlink(path)
                except Exception as e:
                    logger.error(f"Error deleting temporary file {path}: {e}")


async def _generate_solution_with_text_sample_image_question(sample_text: str, question_images: List[Any], llm) -> str:
    """
    Generate a solution using text sample and image question.

    Args:
        sample_text: Text content of the sample question/solution
        question_images: List of image files for the question to solve
        llm: LLM instance

    Returns:
        Generated solution with LaTeX formatting
    """
    # Save images to temporary files
    image_paths = []
    try:
        for image in question_images:
            with tempfile.NamedTemporaryFile(delete=False, suffix=".jpg") as temp_file:
                # Handle different types of file objects
                if hasattr(image, 'seek') and callable(getattr(image, 'seek')):
                    # Reset file position to beginning if possible
                    try:
                        if asyncio.iscoroutinefunction(image.seek):
                            await image.seek(0)
                        else:
                            image.seek(0)
                    except Exception as e:
                        logger.warning(f"Error seeking file: {e}")

                # Handle different types of file objects
                if hasattr(image, 'read'):
                    # If it's a file-like object with a read method
                    content = await image.read() if callable(getattr(image, 'read')) and asyncio.iscoroutinefunction(image.read) else image.read()
                    temp_file.write(content)
                elif isinstance(image, bytes):
                    # If it's already bytes
                    temp_file.write(image)
                else:
                    # Try to convert to string and then bytes
                    temp_file.write(str(image).encode('utf-8'))

                image_paths.append(temp_file.name)
                logger.info(f"Saved question image to temporary file: {temp_file.name}")

        # Parse sample text to extract question and solution
        sample_parts = sample_text.split("\n\nSolution:\n", 1)
        sample_question = sample_parts[0].strip()
        sample_solution = sample_parts[1].strip() if len(sample_parts) > 1 else sample_text

        # Construct content for LLM
        content = [
            {
                "type": "text",
                "text": """
                You are an editorial assistant for a textbook publisher.
                Your only job is to imitate the exact style, wording, notation,
                language, and brevity of the SAMPLE SOLUTION that follows.
                * Preserve every LaTeX/MathML element exactly as shown—no conversion.
                * Do not add headings like "Step 1", "Answer:", or explanatory text.
                * Use the minimum words/symbols needed for a complete, correct answer.
                * Output the solution text only—nothing else.

                SAMPLE QUESTION
                """ + sample_question + """

                SAMPLE SOLUTION
                """ + sample_solution + """

                NEW QUESTION
                (See the attached images)
                
                Instructions:
                - Create solution for NEW QUESTION in the style of SAMPLE SOLUTION.
                - If correction option is mentioned in the sample follow the same structure.
                    For Example: 
                    - If the solution starts with  (2) Enhance (Verb)... follow the same
                    - If the solution starts with Option (A) is correct... follow the same
                TASK
                Write the solution for NEW QUESTION in the identical style and language of SAMPLE SOLUTION.
                """
            }
        ]

        # Add images to content
        for path in image_paths:
            content.append({
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{encode_image_to_base64(path)}",
                    "detail": "high"
                }
            })

        # Call LLM
        response = llm.invoke([HumanMessage(content=content)])

        return response.content

    finally:
        # Clean up temporary files
        for path in image_paths:
            if os.path.exists(path):
                try:
                    os.unlink(path)
                except Exception as e:
                    logger.error(f"Error deleting temporary file {path}: {e}")


async def _generate_solution_with_image_sample_image_question(sample_images: List[Any], question_images: List[Any], llm) -> str:
    """
    Generate a solution using image sample and image question.

    Args:
        sample_images: List of image files for the sample question/solution
        question_images: List of image files for the question to solve
        llm: LLM instance

    Returns:
        Generated solution with LaTeX formatting
    """
    # Save sample images to temporary files
    sample_image_paths = []
    question_image_paths = []
    try:
        # Process sample images
        for image in sample_images:
            with tempfile.NamedTemporaryFile(delete=False, suffix=".jpg") as temp_file:
                # Handle different types of file objects
                if hasattr(image, 'seek') and callable(getattr(image, 'seek')):
                    # Reset file position to beginning if possible
                    try:
                        if asyncio.iscoroutinefunction(image.seek):
                            await image.seek(0)
                        else:
                            image.seek(0)
                    except Exception as e:
                        logger.warning(f"Error seeking file: {e}")

                # Handle different types of file objects
                if hasattr(image, 'read'):
                    # If it's a file-like object with a read method
                    content = await image.read() if callable(getattr(image, 'read')) and asyncio.iscoroutinefunction(image.read) else image.read()
                    temp_file.write(content)
                elif isinstance(image, bytes):
                    # If it's already bytes
                    temp_file.write(image)
                else:
                    # Try to convert to string and then bytes
                    temp_file.write(str(image).encode('utf-8'))

                sample_image_paths.append(temp_file.name)
                logger.info(f"Saved sample image to temporary file: {temp_file.name}")

        # Process question images
        for image in question_images:
            with tempfile.NamedTemporaryFile(delete=False, suffix=".jpg") as temp_file:
                # Handle different types of file objects
                if hasattr(image, 'seek') and callable(getattr(image, 'seek')):
                    # Reset file position to beginning if possible
                    try:
                        if asyncio.iscoroutinefunction(image.seek):
                            await image.seek(0)
                        else:
                            image.seek(0)
                    except Exception as e:
                        logger.warning(f"Error seeking file: {e}")

                # Handle different types of file objects
                if hasattr(image, 'read'):
                    # If it's a file-like object with a read method
                    content = await image.read() if callable(getattr(image, 'read')) and asyncio.iscoroutinefunction(image.read) else image.read()
                    temp_file.write(content)
                elif isinstance(image, bytes):
                    # If it's already bytes
                    temp_file.write(image)
                else:
                    # Try to convert to string and then bytes
                    temp_file.write(str(image).encode('utf-8'))

                question_image_paths.append(temp_file.name)
                logger.info(f"Saved question image to temporary file: {temp_file.name}")

        # Construct content for LLM
        content = [
            {
                "type": "text",
                "text": """
                You are an editorial assistant for a textbook publisher.
                Your only job is to imitate the exact style, wording, notation,
                language, and brevity of the SAMPLE SOLUTION that follows.
                * Preserve every LaTeX/MathML element exactly as shown—no conversion.
                * Do not add headings like "Step 1", "Answer:", or explanatory text.
                * Use the minimum words/symbols needed for a complete, correct answer.
                * Output the solution text only—nothing else.

                SAMPLE QUESTION
                (See the first set of images)
                """
            }
        ]

        # Add sample images to content
        for path in sample_image_paths:
            content.append({
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{encode_image_to_base64(path)}",
                    "detail": "high"
                }
            })

        # Add sample solution text
        content.append({
            "type": "text",
            "text": """
            SAMPLE SOLUTION
            (See the first set of images)

            NEW QUESTION
            (See the next set of images)
            """
        })

        # Add question images to content
        for path in question_image_paths:
            content.append({
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{encode_image_to_base64(path)}",
                    "detail": "high"
                }
            })

        # Add final instructions
        content.append({
            "type": "text",
            "text": """
            
            Instructions:
            - Create solution for NEW QUESTION in the style of SAMPLE SOLUTION.
            - If correction option is mentioned in the sample follow the same structure.
                For Example: 
                - If the solution starts with  (2) Enhance (Verb)... follow the same
                - If the solution starts with Option (A) is correct... follow the same
            TASK
            Write the solution for NEW QUESTION in the identical style and language of SAMPLE SOLUTION.
            """
        })

        # Call LLM
        response = llm.invoke([HumanMessage(content=content)])

        return response.content

    finally:
        # Clean up temporary files
        for path in sample_image_paths + question_image_paths:
            if os.path.exists(path):
                try:
                    os.unlink(path)
                except Exception as e:
                    logger.error(f"Error deleting temporary file {path}: {e}")

