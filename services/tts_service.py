from google.cloud import texttospeech_v1beta1 as texttospeech
import os
import re

# Set the path to your credentials file
os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "credentials.json"


def is_ssml(text):
    """Detect if the input text contains SSML tags."""
    return bool(re.search(r"<[^>]+>", text))


def get_indian_standard_voices(selected_language):
    """Fetch and return all Indian Standard voices in a human-readable format."""
    client = texttospeech.TextToSpeechClient()

    # Get all available voices
    voices = client.list_voices()

    # Filter voices for Indian Standard (names containing "Standard" and -IN region)
    indian_standard_voices = []
    for voice in voices.voices:
        if any("-IN" in lang for lang in voice.language_codes) and "Standard" in voice.name:
            indian_standard_voices.append(voice)

    # Create a human-readable mapping
    readable_voices = []
    for voice in indian_standard_voices:
        language_name = "Other Indian Voice"
        for lang_code in voice.language_codes:
            # Extract the base code (e.g., "en-IN" -> "en")
            base_code = lang_code.split("-")[0]
            if base_code in selected_language:
                language_name = selected_language
                readable_voice = {
                    "name": voice.name,  # Voice ID
                    "language": language_name,  # Human-readable language name
                    "gender": "MALE",  # Voice gender
                }
                readable_voices.append(readable_voice)
                break

    return readable_voices


def text_to_speech(text, voice_name, language_code, encoding="MP3"):
    """
    Convert text to speech using the specified voice and return audio content.
    Automatically detects if input text is SSML or plain text.
    """
    client = texttospeech.TextToSpeechClient()
    print(text)
    updated_text = remove_ssml_tags(text)
    print(updated_text)
    # Determine whether the text is SSML
    if is_ssml(updated_text):
        input_text = texttospeech.SynthesisInput(ssml=updated_text)
    else:
        input_text = texttospeech.SynthesisInput(text=updated_text)

    # Select the appropriate audio encodings
    if encoding == "MP3":
        audio_encoding = texttospeech.AudioEncoding.MP3
    elif encoding == "OGG_OPUS":
        audio_encoding = texttospeech.AudioEncoding.OGG_OPUS
    else:
        raise ValueError(f"Unsupported encoding: {encoding}")

    # Perform the text-to-speech request
    request = texttospeech.SynthesizeSpeechRequest(
        input=input_text,
        voice=texttospeech.VoiceSelectionParams(
            language_code=language_code,
            name=voice_name,
            ssml_gender=texttospeech.SsmlVoiceGender.MALE
        ),
        audio_config=texttospeech.AudioConfig(
            audio_encoding=audio_encoding
        ),
        enable_time_pointing=["SSML_MARK"]
    )

    # Perform the text-to-speech request
    response = client.synthesize_speech(request=request)
    audio_content = response.audio_content
    timepoints = response.timepoints
    print(timepoints)
    return audio_content, timepoints


def get_voice_language_code(voice_name):
    """Extract language code from the voice name (e.g., 'en-IN-Standard-A')."""
    return "-".join(voice_name.split("-")[:2])


def remove_ssml_tags(content):
    """Removes ```ssml from the start and end of the content."""
    if content.startswith("```ssml") or content.startswith("```xml"):
        return content.split("\n", 1)[1].rsplit("\n", 1)[0].strip()
    return content.strip()
