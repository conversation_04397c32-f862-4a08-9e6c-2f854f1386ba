"""
Server-Sent Events (SSE) implementation for real-time progress updates.
"""
import asyncio
import json
import logging
import time
from typing import AsyncGenerator, Dict, List, Any

from fastapi import APIRouter, Request, HTTPException, Depends
from fastapi.responses import StreamingResponse
from auth.dependencies import require_login

from utils.progress_tracker import progress_tracker, ProgressTracker

logger = logging.getLogger(__name__)

router = APIRouter()

# Store active connections for debugging
active_connections: Dict[str, int] = {}

async def event_generator(request: Request, task_id: str) -> AsyncGenerator[bytes, None]:
    """
    Generate SSE events for a task.

    Args:
        request: FastAPI request
        task_id: Task ID to track

    Yields:
        bytes: SSE event data
    """
    # Create a queue for this client
    queue = asyncio.Queue(maxsize=100)  # Allow up to 100 messages in the queue

    # Get the current task state
    task = progress_tracker.get_task(task_id)
    if not task:
        # Create a default task for missing tasks
        logger.warning(f"Task {task_id} not found in event_generator, creating default task")
        task = {
            "status": "not_found",
            "message": f"Task {task_id} not found",
            "task_id": task_id,
            "progress": 0,
            "current_step": "Task not found",
            "steps": []
        }
        # Store the default task in memory
        progress_tracker._tasks[task_id] = task
        # Send an error event but don't return (continue with the stream)
        yield f"event: warning\ndata: {{\"warning\": \"Task {task_id} not found, using default task\"}}\n\n".encode('utf-8')

    # Register this client
    if task_id not in active_connections:
        active_connections[task_id] = 0
    active_connections[task_id] += 1
    logger.info(f"New SSE connection for task {task_id}. Active connections: {active_connections[task_id]}")

    # Register with progress tracker BEFORE sending any events
    # This ensures the client is registered to receive updates
    progress_tracker.register_sse_client(task_id, queue)
    logger.info(f"Registered SSE client for task {task_id}")

    # Small delay to ensure registration is complete
    await asyncio.sleep(0.1)

    # Send initial connection event
    yield b"event: connected\ndata: {\"status\": \"connected\"}\n\n"

    # Send current state immediately
    current_state = json.dumps(task)
    yield f"event: update\ndata: {current_state}\n\n".encode('utf-8')

    # Log the steps that are being sent
    steps = task.get('steps', [])
    if steps:
        logger.info(f"Sending {len(steps)} steps to client for task {task_id}")
        for step in steps:
            logger.info(f"Step in initial update: {step.get('step')}")
    else:
        logger.warning(f"No steps to send in initial update for task {task_id}")

    # Send a test message to verify the connection is working
    test_message = {
        "test": "Connection established",
        "id": task_id,
        "time": time.time(),
        "message": "If you see this message, the SSE connection is working correctly."
    }
    yield f"event: update\ndata: {json.dumps(test_message)}\n\n".encode('utf-8')

    try:
        while True:
            if await request.is_disconnected():
                logger.info(f"Client disconnected from SSE stream for task {task_id}")
                break

            try:
                # Wait for an update with a short timeout
                data = await asyncio.wait_for(queue.get(), timeout=1.0)
                yield f"event: update\ndata: {data}\n\n".encode('utf-8')

                # Check if task is completed or failed
                update = json.loads(data)
                if update.get("status") in ["completed", "failed"]:
                    logger.info(f"Task {task_id} {update.get('status')}, ending SSE stream")
                    yield f"event: {update.get('status')}\ndata: {data}\n\n".encode('utf-8')
                    break
            except asyncio.TimeoutError:
                # Send a keep-alive comment
                yield b": keep-alive\n\n"
                continue
            except Exception as e:
                logger.error(f"Error in SSE event generator: {e}")
                yield f"event: error\ndata: {{\"error\": \"{str(e)}\"}}\n\n".encode('utf-8')
                break
    finally:
        # Unregister this client
        progress_tracker.unregister_sse_client(task_id, queue)

        # Update active connections count
        if task_id in active_connections:
            active_connections[task_id] -= 1
            if active_connections[task_id] <= 0:
                del active_connections[task_id]
            logger.info(f"Closed SSE connection for task {task_id}. Remaining connections: {active_connections.get(task_id, 0)}")

@router.get("/progress/{task_id}")
async def sse_endpoint(request: Request, task_id: str, login_check=Depends(require_login)):
    """
    SSE endpoint for tracking task progress.

    Args:
        request: FastAPI request
        task_id: Task ID to track
        login_check: Login check dependency

    Returns:
        StreamingResponse: SSE stream
    """
    # Check login result
    if login_check:
        return login_check

    # Check if task exists
    task = progress_tracker.get_task(task_id)
    if not task:
        # Log the missing task but continue with the SSE stream
        logger.warning(f"Task {task_id} not found in progress tracker for SSE endpoint, but continuing with SSE stream")
        # Create a default task in memory to allow the SSE stream to work
        default_task = {
            "status": "not_found",
            "message": f"Task {task_id} not found",
            "task_id": task_id,
            "progress": 0,
            "current_step": "Task not found",
            "steps": []
        }
        # Store the default task in memory
        progress_tracker._tasks[task_id] = default_task

    return StreamingResponse(
        event_generator(request, task_id),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
            "X-Accel-Buffering": "no",  # Disable buffering in nginx
        },
    )
