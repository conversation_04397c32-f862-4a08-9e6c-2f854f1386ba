"""
Server-Sent Events (SSE) implementation for FastAPI.
"""
import asyncio
import json
import logging
from typing import AsyncGenerator

from fastapi import APIRouter, Request, Depends, HTTPException
from fastapi.responses import StreamingResponse
from starlette.concurrency import run_in_threadpool

from utils.progress_tracker import progress_tracker
from auth.dependencies import require_login

logger = logging.getLogger(__name__)

router = APIRouter()

async def event_generator(request: Request, task_id: str) -> AsyncGenerator[bytes, None]:
    """
    Generate SSE events for a task.

    Args:
        request: FastAPI request
        task_id: Task ID to track

    Yields:
        bytes: SSE event data
    """
    # Get a queue for this client
    queue = await progress_tracker.listen_for_updates(task_id)

    # Send initial event to establish connection
    yield b"event: connected\ndata: {\"status\": \"connected\"}\n\n"

    # Get the current task state and send it immediately
    task = progress_tracker.get_task(task_id)
    if task:
        yield f"event: update\ndata: {json.dumps(task)}\n\n".encode('utf-8')

    try:
        while True:
            if await request.is_disconnected():
                logger.info(f"Client disconnected from SSE stream for task {task_id}")
                break

            # Wait for the next update with a shorter timeout
            try:
                data = await asyncio.wait_for(queue.get(), timeout=5.0)
                logger.info(f"Sending update for task {task_id}")
                yield f"event: update\ndata: {data}\n\n".encode('utf-8')

                # Immediately check if there are more updates
                while not queue.empty():
                    data = queue.get_nowait()
                    logger.info(f"Sending additional update for task {task_id}")
                    yield f"event: update\ndata: {data}\n\n".encode('utf-8')
            except asyncio.TimeoutError:
                # Send a keep-alive comment to prevent connection timeout
                yield b": keep-alive\n\n"

                # Check if task has been updated since last event
                current_task = progress_tracker.get_task(task_id)
                if current_task:
                    yield f"event: update\ndata: {json.dumps(current_task)}\n\n".encode('utf-8')

                continue
            except Exception as e:
                logger.error(f"Error in SSE event generator: {e}")
                yield f"event: error\ndata: {{\"error\": \"{str(e)}\"}}\n\n".encode('utf-8')
                break

            # If task is completed or failed, end the stream
            task = progress_tracker.get_task(task_id)
            if task and task.get("status") in ["completed", "failed"]:
                logger.info(f"Task {task_id} {task.get('status')}, ending SSE stream")
                # Send one more event to ensure client gets the final state
                yield f"event: {task.get('status')}\ndata: {json.dumps(task)}\n\n".encode('utf-8')
                break
    finally:
        # Clean up the listener when done
        progress_tracker.remove_listener(task_id, queue)

@router.get("/sse/{task_id}")
async def sse_endpoint(
    request: Request,
    task_id: str,
    login_check=Depends(require_login)
):
    """
    SSE endpoint for tracking task progress.

    Args:
        request: FastAPI request
        task_id: Task ID to track

    Returns:
        StreamingResponse: SSE stream
    """
    if login_check:
        return login_check

    # Check if task exists
    task = progress_tracker.get_task(task_id)
    if not task:
        raise HTTPException(status_code=404, detail=f"Task {task_id} not found")

    return StreamingResponse(
        event_generator(request, task_id),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
            "X-Accel-Buffering": "no",  # Disable buffering in nginx
        },
    )


@router.get("/sse/{task_id}/result")
async def get_task_result(
    request: Request,
    task_id: str,
    login_check=Depends(require_login)
):
    """
    Get the result of a completed task.

    Args:
        request: FastAPI request
        task_id: Task ID

    Returns:
        dict: Task result
    """
    if login_check:
        return login_check

    # Check if task exists
    task = progress_tracker.get_task(task_id)
    if not task:
        raise HTTPException(status_code=404, detail=f"Task {task_id} not found")

    # Check if task is completed
    if task["status"] != "completed":
        raise HTTPException(
            status_code=400,
            detail=f"Task {task_id} is not completed (current status: {task['status']})"
        )

    # Return the task result
    return task["result"]
