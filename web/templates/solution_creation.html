{% extends "base.html" %}

{% block title %}Create Solution | PYQs Admin | GPT Sir{% endblock %}

{% block head %}
<style>
  .pyqs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
  }

  .back-btn {
    background-color: #f0f0f0;
    color: #333;
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s;
  }

  .back-btn:hover {
    background-color: #e0e0e0;
  }

  .form-sections {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .form-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
  }

  .section-heading {
    margin-bottom: 0.5rem;
    color: #333;
    font-size: 1.2rem;
    font-weight: 600;
  }

  .section-description {
    margin-bottom: 1rem;
    color: #6c757d;
    font-size: 0.9rem;
  }

  .form-group {
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .form-group label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    font-size: 0.9rem;
  }

  .divider {
    height: 1px;
    background-color: #e0e0e0;
    margin: 1.5rem 0;
  }

  .action-group {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .button-group {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
  }

  .clear-btn {
    background-color: #6c757d;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .clear-btn:hover {
    background-color: #5a6268;
  }

  .clear-all-btn {
    background-color: #dc3545;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .clear-all-btn:hover {
    background-color: #c82333;
  }

  .input-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }

  @media (max-width: 768px) {
    .input-section {
      grid-template-columns: 1fr;
    }
  }

  .image-upload-container {
    border: 1px dashed #ccc;
    padding: 1rem;
    border-radius: 6px;
    text-align: center;
    background-color: #f9f9f9;
    height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow-y: auto;
  }

  .image-preview-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
    justify-content: center;
    max-height: 120px;
    overflow-y: auto;
    padding: 5px;
  }

  .image-preview-wrapper {
    position: relative;
    cursor: grab;
    transition: transform 0.2s;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 5px;
  }

  .image-preview-wrapper:hover {
    transform: scale(1.05);
  }

  .image-preview-wrapper.dragging {
    opacity: 0.4;
    cursor: grabbing;
  }

  .image-preview-wrapper.drag-over {
    transform: scale(1.05);
    background-color: rgba(102, 16, 242, 0.1);
    border: 2px dashed #6610f2;
    border-radius: 6px;
    padding: 3px;
  }

  .image-preview {
    width: 120px;
    height: 120px;
    object-fit: contain;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
  }

  .image-order-number {
    font-size: 12px;
    color: #6610f2;
    font-weight: bold;
    margin-top: 3px;
  }

  .image-remove-btn {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    z-index: 10;
    transition: transform 0.2s, background-color 0.2s;
  }

  .image-remove-btn:hover {
    transform: scale(1.1);
    background-color: #bd2130;
  }

  .image-reorder-hint {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 5px;
    text-align: center;
    display: none;
  }

  .text-input-container {
    border: 1px solid #ccc;
    border-radius: 6px;
    padding: 0;
    background-color: #fff;
    height: 150px;
    display: flex;
  }

  textarea {
    width: 100%;
    padding: 0.75rem;
    font-size: 1rem;
    border: none;
    border-radius: 8px;
    resize: none;
  }

  button {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  button:hover {
    background-color: #45a049;
  }

  button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
  }

  #createSolutionButton {
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
    background-color: #6610f2;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    min-width: 200px;
  }

  #createSolutionButton:hover:not(:disabled) {
    background-color: #520dc2;
  }

  .solution-result {
    margin-top: 2rem;
    display: none;
  }

  .result-title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }

  .result-title-container h3 {
    margin: 0;
    font-size: 1.3rem;
    color: #333;
  }

  .solution-copy-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    font-size: 0.9rem;
    background-color: #f8f9fa;
    color: #495057;
    border: 1px solid #ced4da;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .solution-copy-btn:hover {
    background-color: #e9ecef;
    color: #212529;
  }

  .solution-container {
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 1.5rem;
  }

  .solution-content {
    background-color: #fff;
    padding: 1rem;
    border: 1px solid #eee;
    border-radius: 4px;
    min-height: 200px;
    max-height: 500px;
    overflow-y: auto;
    line-height: 1.5;
    font-family: monospace;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-size: 14px;
  }

  .loading {
    display: none;
    text-align: center;
    margin: 2rem 0;
  }

  .spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border-left-color: #09f;
    animation: spin 1s linear infinite;
    display: inline-block;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .error-message {
    color: #d9534f;
    margin-top: 0.5rem;
    display: none;
  }

  .copy-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    color: #6c757d;
    border-radius: 4px;
    transition: all 0.2s;
  }

  .copy-btn:hover {
    color: #212529;
    background-color: #e9ecef;
  }

  .katex-display>.katex{
    text-align: unset !important;
  }
  .katex-display{
    text-align: unset !important;
  }
</style>
{% endblock %}

{% block content %}
<div class="container">
  <div class="pyqs-header">
    <h1>Create Solution</h1>
    <a href="/home" class="back-btn">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
        <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z"/>
      </svg>
      Back to Home
    </a>
  </div>
  <p class="mb-4">Create solutions for problems using sample questions and solutions.</p>

  <form id="createSolutionForm">
    <div class="form-sections">
      <!-- Sample Question Section -->
      <div class="form-section">
        <h3 class="section-heading">Sample Question with Solution</h3>
        <p class="section-description">Provide a sample question with solution to help generate better results.</p>

        <div class="input-section">
          <div class="form-group">
            <label for="sampleImageUpload"><strong>Upload Image</strong></label>
            <div class="image-upload-container">
              <input type="file" id="sampleImageUpload" accept="image/*" onchange="previewImages(event, 'sampleImagePreviewContainer', 'sampleImages')">
              <div class="image-preview-container" id="sampleImagePreviewContainer"></div>
              <!-- Single image upload, no reordering needed -->
            </div>
            <div class="error-message" id="sampleImageError"></div>
          </div>

          <div class="form-group">
            <label for="sampleTextInput"><strong>OR Enter Text</strong></label>
            <div class="text-input-container">
              <textarea id="sampleTextInput" placeholder="Enter sample question with solution..." oninput="validateInputs()"></textarea>
            </div>
            <div class="error-message" id="sampleTextError"></div>
          </div>
        </div>
      </div>

      <!-- Question to Solve Section -->
      <div class="form-section">
        <h3 class="section-heading">Question to Create Solution For</h3>
        <p class="section-description">Provide the question you want to generate a solution for.</p>

        <div class="input-section">
          <div class="form-group">
            <label for="questionImageUpload"><strong>Upload Image</strong></label>
            <div class="image-upload-container">
              <input type="file" id="questionImageUpload" accept="image/*" onchange="previewImages(event, 'questionImagePreviewContainer', 'questionImages')">
              <div class="image-preview-container" id="questionImagePreviewContainer"></div>
              <!-- Single image upload, no reordering needed -->
            </div>
            <div class="error-message" id="questionImageError"></div>
          </div>

          <div class="form-group">
            <label for="questionTextInput"><strong>OR Enter Text</strong></label>
            <div class="text-input-container">
              <textarea id="questionTextInput" placeholder="Enter the question you want to solve..." oninput="validateInputs()"></textarea>
            </div>
            <div class="error-message" id="questionTextError"></div>
          </div>
        </div>
      </div>
    </div>

    <div class="divider"></div>

    <div class="form-group action-group">
      <div class="button-group">
        <button type="button" id="createSolutionButton" onclick="createSolution()" disabled>Create Solution</button>
        <button type="button" id="clearSolutionButton" onclick="clearInputFields()" class="clear-btn">Clear Question</button>
        <button type="button" id="clearAllButton" onclick="clearAllInputFields()" class="clear-all-btn">Clear All</button>
      </div>
      <div class="error-message" id="solutionFormError"></div>
    </div>
  </form>

  <div class="loading" id="solutionLoadingIndicator">
    <div class="spinner"></div>
    <p>Creating solution. This may take a moment...</p>
  </div>

  <div class="solution-result" id="solutionResultSection">
    <div class="result-title-container">
      <h3>Solution</h3>
      <button onclick="copyText('solutionResult', event)" class="copy-btn solution-copy-btn" title="Copy solution to clipboard">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
          <path d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1h1a1 1 0 0 1 1 1V14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3.5a1 1 0 0 1 1-1h1v-1z"/>
          <path d="M9.5 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5h3zm-3-1A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3z"/>
        </svg>
        Copy Solution
      </button>
    </div>
    <div class="solution-container">
      <pre class="solution-content" id="solutionResult"></pre>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Include KaTeX for LaTeX rendering -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.4/dist/katex.min.css" integrity="sha384-vKruj+a13U8yHIkAyGgK1J3ArTLzrFGBbBc0tDp4ad/EyewESeXE/Iv67Aj8gKZ0" crossorigin="anonymous">
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.4/dist/katex.min.js" integrity="sha384-PwRUT/YqbnEjkZO0zZxNqcxACrXe+j766U2amXcgMg5457rve2Y7I6ZJSm2A0mS4" crossorigin="anonymous"></script>
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.4/dist/contrib/auto-render.min.js" integrity="sha384-+VBxd3r6XgURycqtZ117nYw44OOcIax56Z4dCRWbxyPt0Koah1uHoK0o4+/RRE05" crossorigin="anonymous"></script>

<script>
  // Solution variables
  let sampleImages = [];
  let questionImages = [];
  let rawSolutionText = "";

  // Preview image for both sample and question
  function previewImages(event, containerId, imageArrayName) {
    const files = event.target.files;
    const previewContainer = document.getElementById(containerId);

    // Clear previous previews
    previewContainer.innerHTML = '';

    // Reset the appropriate image array
    if (imageArrayName === 'sampleImages') {
      sampleImages = [];
    } else if (imageArrayName === 'questionImages') {
      questionImages = [];
    }

    // Only process the first image
    if (files.length === 0) {
      return;
    }

    // Hide error message
    const errorId = imageArrayName === 'sampleImages' ? 'sampleImageError' : 'questionImageError';
    document.getElementById(errorId).style.display = 'none';

    // No reorder hint needed for single image

    // Process only the first image
    const file = files[0];
    if (!file.type.startsWith('image/')) {
      return;
    }

    // Add to the appropriate array
    if (imageArrayName === 'sampleImages') {
      sampleImages.push(file);
    } else if (imageArrayName === 'questionImages') {
      questionImages.push(file);
    }

    const reader = new FileReader();
    reader.onload = function(e) {
      // Create wrapper div for draggable functionality
      const wrapper = document.createElement('div');
      wrapper.className = 'image-preview-wrapper';
      wrapper.draggable = true;
      wrapper.dataset.index = 0;
      wrapper.dataset.arrayName = imageArrayName;

      // Add drag event listeners
      wrapper.addEventListener('dragstart', handleDragStart);
      wrapper.addEventListener('dragover', handleDragOver);
      wrapper.addEventListener('dragenter', handleDragEnter);
      wrapper.addEventListener('dragleave', handleDragLeave);
      wrapper.addEventListener('drop', handleDrop);
      wrapper.addEventListener('dragend', handleDragEnd);

      // Create a container for the image and remove button
      const imageContainer = document.createElement('div');
      imageContainer.style.position = 'relative';

      // Create image element
      const img = document.createElement('img');
      img.src = e.target.result;
      img.className = 'image-preview';
      imageContainer.appendChild(img);

      // Add remove button (X)
      const removeBtn = document.createElement('div');
      removeBtn.className = 'image-remove-btn';
      removeBtn.textContent = '×';
      removeBtn.title = 'Remove image';
      removeBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        removeImage(imageArrayName);
      });
      imageContainer.appendChild(removeBtn);

      // Add the image container to the wrapper
      wrapper.appendChild(imageContainer);

      // Add order number text below image
      const orderNumber = document.createElement('div');
      orderNumber.className = 'image-order-number';
      orderNumber.textContent = 'Image 1';
      wrapper.appendChild(orderNumber);

      previewContainer.appendChild(wrapper);
    };
    reader.readAsDataURL(file);

    validateInputs();
  }

  // Improved drag and drop functionality
  let draggedElement = null;
  let dragSourceIndex = -1;
  let dragArrayName = '';

  function handleDragStart(e) {
    // Store the dragged element and its data
    draggedElement = this;
    dragSourceIndex = parseInt(this.dataset.index);
    dragArrayName = this.dataset.arrayName;

    // Add visual feedback
    this.classList.add('dragging');

    // Set drag effect and data
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', dragSourceIndex);

    // For better performance in some browsers
    setTimeout(() => {
      this.style.opacity = '0.4';
    }, 0);
  }

  function handleDragOver(e) {
    if (e.preventDefault) {
      e.preventDefault(); // Necessary to allow dropping
    }
    e.dataTransfer.dropEffect = 'move';
    return false;
  }

  function handleDragEnter(e) {
    // Only add highlight if this is a valid drop target (same array)
    if (this.dataset.arrayName === dragArrayName) {
      this.classList.add('drag-over');
    }
  }

  function handleDragLeave(e) {
    this.classList.remove('drag-over');
  }

  function handleDrop(e) {
    // Stop the browser from performing default action
    if (e.stopPropagation) {
      e.stopPropagation();
    }
    e.preventDefault();

    // Only process if dropping on a different element in the same array
    if (draggedElement !== this && this.dataset.arrayName === dragArrayName) {
      const targetIndex = parseInt(this.dataset.index);

      // Don't proceed if indices are invalid
      if (isNaN(dragSourceIndex) || isNaN(targetIndex)) {
        console.error('Invalid indices for drag and drop');
        return false;
      }

      // Perform the reordering based on array name
      if (dragArrayName === 'sampleImages') {
        moveArrayElement(sampleImages, dragSourceIndex, targetIndex);
        // Small delay to improve performance
        setTimeout(() => {
          refreshImagePreviews('sampleImagePreviewContainer', sampleImages, 'sampleImages');
        }, 50);
      } else if (dragArrayName === 'questionImages') {
        moveArrayElement(questionImages, dragSourceIndex, targetIndex);
        // Small delay to improve performance
        setTimeout(() => {
          refreshImagePreviews('questionImagePreviewContainer', questionImages, 'questionImages');
        }, 50);
      }
    }

    // Remove highlighting
    this.classList.remove('drag-over');
    return false;
  }

  function handleDragEnd(e) {
    // Reset the opacity
    if (draggedElement) {
      draggedElement.style.opacity = '1';
      draggedElement.classList.remove('dragging');
    }

    // Remove all drag-over highlights
    document.querySelectorAll('.image-preview-wrapper').forEach(item => {
      item.classList.remove('drag-over');
    });

    // Reset drag state
    draggedElement = null;
    dragSourceIndex = -1;
    dragArrayName = '';
  }

  // Helper function to move an element within an array
  function moveArrayElement(array, fromIndex, toIndex) {
    // Make sure indices are valid
    if (fromIndex < 0 || fromIndex >= array.length ||
        toIndex < 0 || toIndex >= array.length) {
      console.error('Invalid array indices:', fromIndex, toIndex, array.length);
      return;
    }

    // Extract the element to move
    const element = array[fromIndex];

    // Use array splice for more reliable reordering
    array.splice(fromIndex, 1); // Remove from original position
    array.splice(toIndex, 0, element); // Insert at new position
  }

  // Function to remove an image
  function removeImage(arrayName) {
    // Clear the appropriate array
    if (arrayName === 'sampleImages') {
      sampleImages = [];
      document.getElementById('sampleImagePreviewContainer').innerHTML = '';
      document.getElementById('sampleImageUpload').value = '';
    } else if (arrayName === 'questionImages') {
      questionImages = [];
      document.getElementById('questionImagePreviewContainer').innerHTML = '';
      document.getElementById('questionImageUpload').value = '';
    }

    // Validate inputs to update button state
    validateInputs();
  }

  // Improved refresh image previews function
  function refreshImagePreviews(containerId, imagesArray, arrayName) {
    const previewContainer = document.getElementById(containerId);

    // Clear the container
    while (previewContainer.firstChild) {
      previewContainer.removeChild(previewContainer.firstChild);
    }

    // Hide reorder hint as it's not needed for single image
    const reorderHintId = arrayName === 'sampleImages' ? 'sampleReorderHint' : 'questionReorderHint';
    const reorderHint = document.getElementById(reorderHintId);
    if (reorderHint) {
      reorderHint.style.display = 'none';
    }

    // If no images, exit early
    if (imagesArray.length === 0) {
      return;
    }

    // Create new preview for the single image
    const file = imagesArray[0];
    const reader = new FileReader();
    reader.onload = function(e) {
      // Create wrapper div for draggable functionality
      const wrapper = document.createElement('div');
      wrapper.className = 'image-preview-wrapper';
      wrapper.draggable = true;
      wrapper.dataset.index = 0;
      wrapper.dataset.arrayName = arrayName;

      // Create a container for the image and remove button
      const imageContainer = document.createElement('div');
      imageContainer.style.position = 'relative';

      // Create image element
      const img = document.createElement('img');
      img.src = e.target.result;
      img.className = 'image-preview';
      imageContainer.appendChild(img);

      // Add remove button (X)
      const removeBtn = document.createElement('div');
      removeBtn.className = 'image-remove-btn';
      removeBtn.textContent = '×';
      removeBtn.title = 'Remove image';
      removeBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        removeImage(arrayName);
      });
      imageContainer.appendChild(removeBtn);

      // Add the image container to the wrapper
      wrapper.appendChild(imageContainer);

      // Add order number text below image
      const orderNumber = document.createElement('div');
      orderNumber.className = 'image-order-number';
      orderNumber.textContent = 'Image 1';
      wrapper.appendChild(orderNumber);

      // Add drag event listeners
      wrapper.addEventListener('dragstart', handleDragStart);
      wrapper.addEventListener('dragover', handleDragOver);
      wrapper.addEventListener('dragenter', handleDragEnter);
      wrapper.addEventListener('dragleave', handleDragLeave);
      wrapper.addEventListener('drop', handleDrop);
      wrapper.addEventListener('dragend', handleDragEnd);

      previewContainer.appendChild(wrapper);
    };
    reader.readAsDataURL(file);
  }

  // Validate all inputs
  function validateInputs() {
    const sampleTextInput = document.getElementById('sampleTextInput').value.trim();
    const questionTextInput = document.getElementById('questionTextInput').value.trim();
    const createSolutionButton = document.getElementById('createSolutionButton');
    const formError = document.getElementById('solutionFormError');

    // Reset error messages
    document.getElementById('sampleTextError').style.display = 'none';
    document.getElementById('questionTextError').style.display = 'none';
    document.getElementById('sampleImageError').style.display = 'none';
    document.getElementById('questionImageError').style.display = 'none';
    formError.style.display = 'none';

    // Reset border colors
    document.querySelectorAll('.image-upload-container, .text-input-container').forEach(container => {
      container.style.borderColor = '#ccc';
    });

    // Validate sample inputs
    let sampleValid = true;
    if (sampleImages.length > 0 && sampleTextInput !== '') {
      document.getElementById('sampleTextError').textContent = 'Please provide either images OR text for the sample, not both.';
      document.getElementById('sampleTextError').style.display = 'block';
      document.querySelector('#sampleImageUpload').closest('.image-upload-container').style.borderColor = '#dc3545';
      document.querySelector('#sampleTextInput').closest('.text-input-container').style.borderColor = '#dc3545';
      sampleValid = false;
    }

    // Validate question inputs
    let questionValid = true;
    if (questionImages.length > 0 && questionTextInput !== '') {
      document.getElementById('questionTextError').textContent = 'Please provide either images OR text for the question, not both.';
      document.getElementById('questionTextError').style.display = 'block';
      document.querySelector('#questionImageUpload').closest('.image-upload-container').style.borderColor = '#dc3545';
      document.querySelector('#questionTextInput').closest('.text-input-container').style.borderColor = '#dc3545';
      questionValid = false;
    }

    // Check if at least one sample and one question input is provided
    const hasSampleInput = sampleImages.length > 0 || sampleTextInput !== '';
    const hasQuestionInput = questionImages.length > 0 || questionTextInput !== '';

    if (!hasSampleInput) {
      document.getElementById('sampleTextError').textContent = 'Please provide either images or text for the sample question.';
      document.getElementById('sampleTextError').style.display = 'block';
    }

    if (!hasQuestionInput) {
      document.getElementById('questionTextError').textContent = 'Please provide either images or text for the question to solve.';
      document.getElementById('questionTextError').style.display = 'block';
    }

    // Highlight active input methods
    if (sampleImages.length > 0) {
      document.querySelector('#sampleImageUpload').closest('.image-upload-container').style.borderColor = '#28a745';
    } else if (sampleTextInput !== '') {
      document.querySelector('#sampleTextInput').closest('.text-input-container').style.borderColor = '#28a745';
    }

    if (questionImages.length > 0) {
      document.querySelector('#questionImageUpload').closest('.image-upload-container').style.borderColor = '#28a745';
    } else if (questionTextInput !== '') {
      document.querySelector('#questionTextInput').closest('.text-input-container').style.borderColor = '#28a745';
    }

    // Enable button if all validations pass
    createSolutionButton.disabled = !(sampleValid && questionValid && hasSampleInput && hasQuestionInput);
  }

  // Create solution
  async function createSolution() {
    const sampleTextInput = document.getElementById('sampleTextInput').value.trim();
    const questionTextInput = document.getElementById('questionTextInput').value.trim();
    const formError = document.getElementById('solutionFormError');
    const loadingIndicator = document.getElementById('solutionLoadingIndicator');
    const resultSection = document.getElementById('solutionResultSection');

    // Validate inputs again
    validateInputs();

    if (document.getElementById('createSolutionButton').disabled) {
      return; // Validation failed
    }

    // Show loading indicator
    loadingIndicator.style.display = 'block';
    resultSection.style.display = 'none';
    formError.style.display = 'none';

    try {
      const formData = new FormData();

      // Add sample data
      if (sampleImages.length > 0) {
        console.log('Adding sample image: ' + sampleImages[0].name);
        formData.append('sample_image_0', sampleImages[0]);
      } else {
        formData.append('sample_text', sampleTextInput);
      }

      // Add question data
      if (questionImages.length > 0) {
        console.log('Adding question image: ' + questionImages[0].name);
        formData.append('question_image_0', questionImages[0]);
      } else {
        formData.append('question_text', questionTextInput);
      }

      // Send request to API
      const response = await fetch('/api/create-solution', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'An error occurred while processing your request.');
      }

      const data = await response.json();

      // Store the raw solution text for copying
      rawSolutionText = data.solution;

      // Display result in pre tag
      const solutionResult = document.getElementById('solutionResult');
      solutionResult.textContent = data.solution;

      // Render LaTeX expressions after a short delay to ensure content is fully loaded
      setTimeout(() => {
        if (window.renderMathInElement) {
          window.renderMathInElement(solutionResult, {
            delimiters: [
              {left: '$$', right: '$$', display: true},
              {left: '$', right: '$', display: false},
              {left: '\\(', right: '\\)', display: false},
              {left: '\\[', right: '\\]', display: true}
            ],
            throwOnError: false,
            output: 'html',
            ignoredTags: []
          });
        }
      }, 100);

      resultSection.style.display = 'block';

      // Clear only the question input fields, preserve sample
      clearInputFields();
    } catch (error) {
      formError.textContent = error.message;
      formError.style.display = 'block';
    } finally {
      loadingIndicator.style.display = 'none';
    }
  }

  // Function to clear input fields
  function clearInputFields() {
    // Only clear question inputs, preserve sample inputs
    document.getElementById('questionTextInput').value = '';

    // Clear question image upload
    document.getElementById('questionImageUpload').value = '';
    document.getElementById('questionImagePreviewContainer').innerHTML = '';
    questionImages = [];

    // Reset border colors for question inputs only
    document.querySelector('#questionImageUpload').closest('.image-upload-container').style.borderColor = '#ccc';
    document.querySelector('#questionTextInput').closest('.text-input-container').style.borderColor = '#ccc';

    // Disable the create solution button
    document.getElementById('createSolutionButton').disabled = true;

    // Clear any error messages
    document.getElementById('questionImageError').style.display = 'none';
    document.getElementById('questionTextError').style.display = 'none';
    document.getElementById('solutionFormError').style.display = 'none';
  }

  // Function to clear all input fields (including sample)
  function clearAllInputFields() {
    // Clear all text inputs
    document.getElementById('sampleTextInput').value = '';
    document.getElementById('questionTextInput').value = '';

    // Clear all image uploads
    document.getElementById('sampleImageUpload').value = '';
    document.getElementById('questionImageUpload').value = '';
    document.getElementById('sampleImagePreviewContainer').innerHTML = '';
    document.getElementById('questionImagePreviewContainer').innerHTML = '';
    sampleImages = [];
    questionImages = [];

    // Reset all border colors
    document.querySelectorAll('.image-upload-container, .text-input-container').forEach(container => {
      container.style.borderColor = '#ccc';
    });

    // Disable the create solution button
    document.getElementById('createSolutionButton').disabled = true;

    // Clear all error messages
    document.getElementById('sampleImageError').style.display = 'none';
    document.getElementById('sampleTextError').style.display = 'none';
    document.getElementById('questionImageError').style.display = 'none';
    document.getElementById('questionTextError').style.display = 'none';
    document.getElementById('solutionFormError').style.display = 'none';
  }

  // Copy text to clipboard
  function copyText(elementId, event) {
    // Get the button that was clicked
    const button = event ? event.currentTarget : document.querySelector(`button[onclick*="${elementId}"]`);
    const element = document.getElementById(elementId);

    // Determine what text to copy
    let textToCopy;
    if (elementId === 'solutionResult' && rawSolutionText) {
      // For solution, use the raw text with LaTeX notation
      textToCopy = rawSolutionText;
    } else {
      // For other elements, use the text content
      textToCopy = element.textContent;
    }

    // Store the original button HTML
    const originalHTML = button.innerHTML;

    // Try to copy the text
    try {
      // Create a temporary textarea element
      const textarea = document.createElement('textarea');
      textarea.value = textToCopy;
      textarea.setAttribute('readonly', '');
      textarea.style.position = 'absolute';
      textarea.style.left = '-9999px';
      document.body.appendChild(textarea);

      // Select and copy the text
      textarea.select();
      document.execCommand('copy');
      document.body.removeChild(textarea);

      // Show success feedback
      button.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"/></svg>';
      button.style.color = '#28a745';

      // Reset the button after a delay
      setTimeout(() => {
        button.innerHTML = originalHTML;
        button.style.color = '';
      }, 2000);
    } catch (err) {
      console.error('Could not copy text: ', err);
      alert('Failed to copy text to clipboard');
    }
  }
</script>
{% endblock %}
