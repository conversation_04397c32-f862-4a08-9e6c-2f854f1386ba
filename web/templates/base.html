<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{% block title %}GPT Sir{% endblock %}</title>
  <link rel="stylesheet" href="/static/styles.css">
  <link href="https://fonts.googleapis.com/css2?family=Alata&display=swap" rel="stylesheet">
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">
  {% block head %}{% endblock %}
</head>
<body>
  <header class="site-header">
    <div class="header-container">
      <div class="logo-container">
        <a href="/home" class="logo-link">
          <img src="/static/assets/gptsir-logo.jpeg" alt="GPT Sir Logo" class="logo">
          <h1 class="site-title">GPT Sir</h1>
        </a>
      </div>
      <div class="nav-container">
        {% if request.session.get("user") and request.url.path != "/" %}
          <a href="/logout" class="logout-btn">Logout</a>
        {% endif %}
      </div>
    </div>
  </header>

  <main class="content-container">
    {% block content %}{% endblock %}
  </main>

  <footer class="site-footer">
    <div class="footer-container">
      <p>© 2025 GPT Sir. All rights reserved.</p>
      <div class="footer-powered">
        <span>Powered by</span>
        <img src="/static/assets/ws-logo.png" alt="Wonderslate Logo" class="ws-logo">
      </div>
    </div>
  </footer>

  {% block scripts %}{% endblock %}

  <script>
    // Handle back navigation
    window.onpageshow = function(event) {
      if (event.persisted) {
        // Page was loaded from cache (back/forward button)
        // Only reload if we're not on the home page
        if (window.location.pathname !== '/home') {
          window.location.reload();
        }
      }
    };

    // Custom back navigation handling
    window.addEventListener('popstate', function(event) {
      // If we're not on the home page, redirect to home
      if (window.location.pathname !== '/home') {
        window.location.href = '/home';
      }
    });
  </script>
</body>
</html>
