{% extends "base.html" %}

{% block title %}TOC Extractor | GPT Sir{% endblock %}

{% block head %}
<style>
    :root {
        --primary: #4361ee;
        --secondary: #3f37c9;
        --success: #4cc9f0;
        --info: #4895ef;
        --warning: #f72585;
        --danger: #e63946;
        --light: #f8f9fa;
        --dark: #212529;
        --gray: #6c757d;
        --light-gray: #dee2e6;
    }

    .test-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px;
    }

    .test-card {
        background-color: white;
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        padding: 30px;
        width: 100%;
        max-width: 800px;
    }

    .test-description {
        color: var(--gray);
        margin-bottom: 20px;
    }

    .upload-form {
        display: flex;
        flex-direction: column;
        gap: 15px;
        margin-bottom: 20px;
    }

    .input-container {
        margin-bottom: 20px;
    }

    .input-container label {
        display: block;
        margin-bottom: 8px;
        font-weight: bold;
        color: var(--dark);
    }

    .input-container input[type="text"],
    .input-container input[type="number"] {
        width: 100%;
        padding: 12px;
        border: 1px solid var(--light-gray);
        border-radius: 5px;
        font-size: 16px;
        transition: border-color 0.3s ease;
    }

    .input-container input[type="text"]:focus,
    .input-container input[type="number"]:focus {
        border-color: var(--primary);
        outline: none;
        box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);
    }

    .input-container input[type="text"]::placeholder,
    .input-container input[type="number"]::placeholder {
        color: var(--gray);
    }

    .input-help {
        margin-top: 5px;
        font-size: 14px;
        color: var(--gray);
    }

    .submit-button {
        background-color: var(--primary);
        color: white;
        border: none;
        border-radius: 5px;
        padding: 10px 20px;
        font-size: 16px;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }

    .submit-button:hover {
        background-color: var(--secondary);
    }

    .submit-button:disabled {
        background-color: var(--light-gray);
        cursor: not-allowed;
    }

    .spinner {
        display: none;
        width: 40px;
        height: 40px;
        margin: 20px auto;
        border: 4px solid rgba(0, 0, 0, 0.1);
        border-left-color: var(--primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }

    .result-container {
        display: none;
        margin-top: 30px;
        padding: 20px;
        border-radius: 8px;
        background-color: var(--light);
    }

    .result-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 15px;
        color: var(--dark);
    }

    .resource-info {
        margin-bottom: 20px;
        padding: 15px;
        background-color: var(--light);
        border-radius: 5px;
        border-left: 4px solid var(--primary);
    }

    .resource-info p {
        margin: 5px 0;
        font-size: 14px;
    }

    .toc-list {
        max-height: 400px;
        overflow-y: auto;
        padding: 10px;
        border: 1px solid var(--light-gray);
        border-radius: 5px;
        background-color: white;
    }

    .toc-item {
        display: flex;
        justify-content: space-between;
        padding: 8px 10px;
        border-bottom: 1px solid var(--light-gray);
    }

    .toc-item:last-child {
        border-bottom: none;
    }

    .toc-header {
        display: flex;
        justify-content: space-between;
        padding: 10px;
        background-color: var(--light);
        font-weight: bold;
        border-bottom: 2px solid var(--primary);
        margin-bottom: 5px;
    }

    .toc-title {
        flex-grow: 2;
        margin-right: 10px;
        width: 50%;
    }

    .toc-page {
        color: var(--primary);
        font-weight: bold;
        width: 16%;
        text-align: center;
    }

    .toc-page-end {
        color: var(--info);
        font-weight: bold;
        width: 16%;
        text-align: center;
    }

    .toc-page-actual {
        color: var(--success);
        font-weight: bold;
        width: 16%;
        text-align: center;
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        margin-top: 15px;
    }

    .action-button {
        padding: 8px 15px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.3s ease;
    }

    .download-button {
        background-color: var(--success);
        color: white;
    }

    .download-button:hover {
        background-color: #3aa8d8;
    }

    .copy-button {
        background-color: var(--info);
        color: white;
    }

    .copy-button:hover {
        background-color: #3a7bd8;
    }

    .timer {
        margin-top: 10px;
        font-size: 14px;
        color: var(--gray);
        text-align: center;
    }

    .error-message {
        color: var(--danger);
        margin-top: 10px;
        padding: 10px;
        border-radius: 5px;
        background-color: rgba(230, 57, 70, 0.1);
        display: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="test-container">
    <div class="test-card">
        <h1>Table of Contents Extractor</h1>
        <p class="test-description">Enter an S3 path to extract its table of contents.</p>

        <form id="tocExtractorForm" class="upload-form">
            <div class="input-container">
                <label for="s3Path">S3 Path:</label>
                <input type="text" id="s3Path" name="s3Path" placeholder="Enter S3 path to PDF file" required>
                <div class="input-help">Enter the S3 path to the PDF file (e.g., supload/pdfextracts/123/456/789.pdf)</div>
            </div>
            <div class="input-container">
                <label for="tocPageNumbers">Table of Content page numbers (comma separated):</label>
                <input type="text" id="tocPageNumbers" name="tocPageNumbers" placeholder="e.g., 1,2,3">
                <div class="input-help">Enter specific page numbers containing the table of contents, separated by commas</div>
            </div>
            <div class="input-container">
                <label for="startingPageNumber">Starting page number:</label>
                <input type="number" id="startingPageNumber" name="startingPageNumber" placeholder="e.g., 1" min="1">
                <div class="input-help">Enter the actual starting page number of the PDF.</div>
            </div>
            <button type="submit" class="submit-button" id="submitButton">Extract Table of Contents</button>
        </form>

        <div id="spinner" class="spinner"></div>
        <div id="timer" class="timer"></div>
        <div id="errorMessage" class="error-message"></div>

        <div id="resultContainer" class="result-container">
            <div class="result-title">Extracted Table of Contents</div>
            <div id="tocList" class="toc-list"></div>
            <div class="action-buttons">
                <button id="downloadButton" class="action-button download-button">Download JSON</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // DOM elements
    const form = document.getElementById('tocExtractorForm');
    const s3PathInput = document.getElementById('s3Path');
    const tocPageNumbersInput = document.getElementById('tocPageNumbers');
    const startingPageNumberInput = document.getElementById('startingPageNumber');
    const submitButton = document.getElementById('submitButton');
    const spinner = document.getElementById('spinner');
    const timer = document.getElementById('timer');
    const errorMessage = document.getElementById('errorMessage');
    const resultContainer = document.getElementById('resultContainer');
    const tocList = document.getElementById('tocList');
    const downloadButton = document.getElementById('downloadButton');

    // Global variables
    let tocData = [];
    let extractionId = '';
    let timerInterval;
    let startTime;

    // Format duration for timer
    function formatDuration(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs < 10 ? '0' + secs : secs}`;
    }

    // Form submit handler
    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        // Reset UI
        resultContainer.style.display = 'none';
        errorMessage.style.display = 'none';
        errorMessage.textContent = '';

        // Show spinner and start timer
        spinner.style.display = 'block';
        startTime = new Date();
        timerInterval = setInterval(() => {
            const elapsed = Math.floor((new Date() - startTime) / 1000);
            timer.textContent = `Processing time: ${formatDuration(elapsed)}`;
        }, 1000);

        // Get the form values
        const s3Path = s3PathInput.value.trim();
        const tocPageNumbers = tocPageNumbersInput.value.trim();
        const startingPageNumberStr = startingPageNumberInput.value.trim();
        const startingPageNumber = startingPageNumberStr ? parseInt(startingPageNumberStr) : null;

        if (!s3Path) {
            errorMessage.textContent = 'Please enter an S3 path';
            errorMessage.style.display = 'block';
            spinner.style.display = 'none';
            clearInterval(timerInterval);
            return;
        }

        try {
            // Prepare request body
            const requestBody = {
                s3_path: s3Path
            };

            // Add optional parameters if provided
            if (tocPageNumbers) {
                requestBody.toc_page_numbers = tocPageNumbers;
            }

            if (startingPageNumber) {
                requestBody.starting_page_number = startingPageNumber;
            }

            // Send request to API
            const response = await fetch('/api/extract-toc-by-resource', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            // Clear timer
            clearInterval(timerInterval);

            if (response.ok) {
                const result = await response.json();

                if (result.status === 'success') {
                    // Store data
                    tocData = result.results || [];
                    extractionId = result.extraction_id;

                    // Log the number of TOC entries
                    console.log(`Received ${tocData.length} TOC entries`);

                    // Add S3 path information to the UI
                    const resourceInfo = document.createElement('div');
                    resourceInfo.className = 'resource-info';

                    // Build the HTML content
                    let infoHTML = `
                        <p><strong>S3 Path:</strong> ${result.s3_path}</p>
                        <p><strong>Extraction ID:</strong> ${result.extraction_id}</p>
                    `;

                    // Add TOC page numbers if provided
                    if (tocPageNumbers) {
                        infoHTML += `<p><strong>TOC Page Numbers:</strong> ${tocPageNumbers}</p>`;
                    }

                    // Add starting page number if provided
                    if (startingPageNumber) {
                        infoHTML += `<p><strong>Starting Page Number:</strong> ${startingPageNumber}</p>`;
                    }

                    // Add page calculation information if available
                    if (result.results && result.results.length > 0) {
                        const firstEntry = result.results[0];
                        if (firstEntry.first_page_number !== undefined && firstEntry.first_page_number !== null) {
                            infoHTML += `<p><strong>First TOC Page Number:</strong> ${firstEntry.first_page_number}</p>`;
                        }
                        if (firstEntry.page_difference !== undefined) {
                            infoHTML += `<p><strong>Page Difference:</strong> ${firstEntry.page_difference}</p>`;
                        }
                    }

                    // Add note about filtering
                    infoHTML += `<p><em>Note: Entries with non-numeric page numbers (like "(i)") or empty page values are automatically filtered out.</em></p>`;

                    // Check if this is a special case where all entries have the same starting page
                    if (result.results && result.results.length > 0 && result.results[0].special_case === true) {
                        infoHTML += `<p style="color: var(--warning);"><strong>⚠️ Special Case:</strong> <em>Multiple entries with the same starting page number detected. This appears to be a document where each chapter or section starts from page 1.</em></p>`;
                        infoHTML += `<p style="color: #4cc9f0;"><strong>Sequential Page Numbers:</strong> <em>For this special case, sequential page numbers (shown in blue italic) have been calculated by assuming each section follows the previous one.</em></p>`;
                    }

                    resourceInfo.innerHTML = infoHTML;

                    // Clear any previous resource info
                    const existingInfo = document.querySelector('.resource-info');
                    if (existingInfo) {
                        existingInfo.remove();
                    }

                    // Add the resource info before the TOC list
                    resultContainer.insertBefore(resourceInfo, tocList);

                    // Display TOC entries
                    displayTOCEntries(tocData);

                    // Show result container
                    resultContainer.style.display = 'block';
                } else {
                    // Show error message
                    errorMessage.textContent = result.message || 'Failed to extract table of contents';
                    errorMessage.style.display = 'block';
                }
            } else {
                // Show error message
                const errorData = await response.json();
                errorMessage.textContent = errorData.detail || 'Failed to extract table of contents';
                errorMessage.style.display = 'block';
            }
        } catch (error) {
            // Show error message
            errorMessage.textContent = error.message || 'An error occurred';
            errorMessage.style.display = 'block';
        } finally {
            // Hide spinner
            spinner.style.display = 'none';
        }
    });

    // Display TOC entries
    function displayTOCEntries(entries) {
        tocList.innerHTML = '';

        if (!entries || entries.length === 0) {
            tocList.innerHTML = '<div class="toc-item" style="text-align: center; padding: 20px; color: var(--warning);">No valid table of contents entries found. Entries with non-numeric page numbers (like "(i)") or empty page values have been filtered out.</div>';
            return;
        }

        // Log the entries for debugging
        console.log("TOC entries:", entries);

        // Add header row
        const header = document.createElement('div');
        header.className = 'toc-header';

        const titleHeader = document.createElement('div');
        titleHeader.className = 'toc-title';
        titleHeader.textContent = 'Chapter Name';

        const startPageHeader = document.createElement('div');
        startPageHeader.className = 'toc-page';
        startPageHeader.textContent = 'Start Page';

        const endPageHeader = document.createElement('div');
        endPageHeader.className = 'toc-page-end';
        endPageHeader.textContent = 'End Page';

        const actualPageHeader = document.createElement('div');
        actualPageHeader.className = 'toc-page-actual';
        actualPageHeader.textContent = 'Actual Page';

        header.appendChild(titleHeader);
        header.appendChild(startPageHeader);
        header.appendChild(endPageHeader);
        header.appendChild(actualPageHeader);

        tocList.appendChild(header);

        // Add entries
        entries.forEach(entry => {
            const item = document.createElement('div');
            item.className = 'toc-item';

            // Check if entry has title and page properties
            if (entry && typeof entry === 'object' && 'title' in entry) {
                // Title column
                const title = document.createElement('div');
                title.className = 'toc-title';
                title.textContent = entry.title;
                item.appendChild(title);

                // Start page column
                const startPage = document.createElement('div');
                startPage.className = 'toc-page';
                if ('start_page' in entry) {
                    startPage.textContent = entry.start_page;
                } else if ('page' in entry) {
                    // Fallback to original page if start_page is not available
                    const pageText = entry.page.split('-')[0];
                    startPage.textContent = pageText;
                } else {
                    startPage.textContent = '-';
                }
                item.appendChild(startPage);

                // End page column
                const endPage = document.createElement('div');
                endPage.className = 'toc-page-end';
                if ('actual_end_page' in entry) {
                    endPage.textContent = entry.actual_end_page;
                } else {
                    // If no end page, show dash
                    endPage.textContent = '-';
                }
                item.appendChild(endPage);

                // Actual page column
                const actualPage = document.createElement('div');
                actualPage.className = 'toc-page-actual';

                // For special case, the actual_start_page and actual_end_page already contain
                // the sequential values (they were replaced in the backend)
                if ('actual_start_page' in entry) {
                    if ('actual_end_page' in entry) {
                        actualPage.textContent = `${entry.actual_start_page}`;
                    } else {
                        actualPage.textContent = entry.actual_start_page;
                    }

                    // If this is a special case, add a special style
                    if (entry.special_case === true) {
                        actualPage.style.color = '#4cc9f0'; // Different color for sequential numbers
                        actualPage.style.fontStyle = 'italic';
                    }
                } else {
                    actualPage.textContent = '-';
                }
                item.appendChild(actualPage);
            } else {
                // Handle unexpected entry format
                item.textContent = JSON.stringify(entry);
            }

            tocList.appendChild(item);
        });

        // Check if this is a special case where all entries have the same starting page
        const isSpecialCase = entries.length > 0 && entries[0].special_case === true;

        // If it's a special case, show a warning
        if (isSpecialCase) {
            const warningItem = document.createElement('div');
            warningItem.className = 'toc-item';
            warningItem.style.marginTop = '20px';
            warningItem.style.backgroundColor = 'rgba(247, 37, 133, 0.1)'; // Light warning color
            warningItem.style.padding = '15px';
            warningItem.style.borderRadius = '5px';
            warningItem.style.border = '1px solid var(--warning)';

            const warningText = document.createElement('div');
            warningText.style.width = '100%';
            warningText.style.textAlign = 'center';
            warningText.style.color = 'var(--warning)';
            warningText.style.fontWeight = 'bold';

            warningText.innerHTML = `
                <p>⚠️ Special Case Detected: Multiple entries with the same starting page number.</p>
                <p style="font-weight: normal; font-size: 14px;">This appears to be a document where each chapter or section starts from page 1.
                Sequential page numbers (shown in <span style="color: #4cc9f0; font-style: italic;">blue italic</span>) have been calculated by assuming each section follows the previous one.</p>
            `;

            warningItem.appendChild(warningText);
            tocList.appendChild(warningItem);
        }

        // Add page number information if available
        if (entries.length > 0 && entries[0].page_difference !== undefined) {
            const infoItem = document.createElement('div');
            infoItem.className = 'toc-item';
            infoItem.style.marginTop = '20px';
            infoItem.style.backgroundColor = 'var(--light)';
            infoItem.style.padding = '10px';
            infoItem.style.borderRadius = '5px';

            const infoText = document.createElement('div');
            infoText.style.width = '100%';
            infoText.style.textAlign = 'center';

            const firstPage = entries[0].first_page_number;
            const startingPage = entries[0].starting_page_number;
            const difference = entries[0].page_difference;

            let infoContent = '';
            if (firstPage !== null) {
                infoContent += `First TOC page: ${firstPage}`;
            }
            if (startingPage !== null) {
                infoContent += ` | Starting page: ${startingPage}`;
            }
            if (difference !== undefined) {
                infoContent += ` | Page difference: ${difference}`;
            }

            // Add a note about the special case if detected
            if (isSpecialCase) {
                infoContent += ' | Note: Page numbers may not be accurate across different sections';
            }

            infoText.textContent = infoContent;
            infoItem.appendChild(infoText);

            tocList.appendChild(infoItem);
        }
    }

    // Download button click handler
    downloadButton.addEventListener('click', function() {
        if (tocData.length === 0) return;

        const dataStr = JSON.stringify(tocData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `toc_${extractionId}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    });
</script>
{% endblock %}
