{% extends "base.html" %}

{% block title %}Test Page | GPT Sir{% endblock %}

{% block content %}
<div class="test-container">
    <div class="test-card">
        <h1>Welcome to GPT Sir</h1>
        <p class="test-description">Your personalized AI learning assistant is live!</p>

        <div class="status-container">
            <div class="status-item success">
                <span class="status-icon">✅</span>
                <span class="status-text">Server is up and running</span>
            </div>

            <div class="status-item">
                <span class="status-icon">🔄</span>
                <span class="status-text">Database connection</span>
            </div>

            <div class="status-item">
                <span class="status-icon">📁</span>
                <span class="status-text">File system access</span>
            </div>
        </div>

        <div class="test-actions">
            <a href="/home" class="test-button primary">Go to Dashboard</a>
            <button class="test-button secondary" id="refreshStatus">Refresh Status</button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.getElementById('refreshStatus').addEventListener('click', function() {
        // Simulate status refresh
        const statusItems = document.querySelectorAll('.status-item');

        statusItems.forEach(item => {
            // Add loading state
            item.classList.add('loading');
            const icon = item.querySelector('.status-icon');
            const originalIcon = icon.textContent;
            icon.textContent = '⏳';

            // Simulate API check with random timing
            setTimeout(() => {
                item.classList.remove('loading');

                // Randomly determine success or failure for demo
                const success = Math.random() > 0.3;
                if (success) {
                    item.classList.add('success');
                    item.classList.remove('failure');
                    icon.textContent = '✅';
                } else {
                    item.classList.add('failure');
                    item.classList.remove('success');
                    icon.textContent = '❌';
                }
            }, 500 + Math.random() * 2000);
        });
    });
</script>
{% endblock %}
