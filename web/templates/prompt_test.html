{% extends "base.html" %}

{% block title %}AI Image & Text Prompt Tester{% endblock %}

{% block head %}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #e63462;
            --primary-dark: #fe5f55;
            --success-color: #c7efcf;
            --error-color: #e74c3c;
            --bg-color: #f9fafb;
            --card-bg: #ffffff;
            --text-color: #333745;
            --border-radius: 10px;
            --shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        .test-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        .test-card {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 2rem;
            margin-bottom: 2rem;
            transition: transform 0.3s ease;
        }

        .test-card:hover {
            transform: translateY(-5px);
        }

        .test-card h1 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            font-weight: 600;
            font-size: 1.8rem;
            text-align: center;
        }

        .test-description {
            text-align: center;
            color: var(--text-color);
            margin-bottom: 2rem;
            font-size: 1.1rem;
            line-height: 1.5;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--text-color);
            font-size: 1rem;
        }

        .form-control {
            width: 100%;
            padding: 0.8rem 1rem;
            border: 1px solid #e1e1e1;
            border-radius: var(--border-radius);
            background-color: #f8f9fa;
            transition: var(--transition);
            font-size: 1rem;
            font-family: inherit;
        }

        textarea.form-control {
            min-height: 120px;
            resize: vertical;
            width: 96% !important;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(230, 52, 98, 0.2);
        }

        .image-upload-container {
            border: 2px dashed #cbd5e0;
            border-radius: var(--border-radius);
            background-color: #f8f9fa;
            padding: 2rem 1rem;
            text-align: center;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            min-height: 150px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
        }

        .image-upload-container:hover {
            border-color: var(--primary-color);
            background-color: rgba(230, 52, 98, 0.05);
        }

        .image-upload-container.active {
            border-color: var(--primary-color);
            background-color: rgba(230, 52, 98, 0.1);
        }

        .upload-icon {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
            opacity: 0.8;
        }

        .upload-text {
            color: var(--text-color);
            font-size: 1.1rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .upload-subtext {
            color: #718096;
            font-size: 0.9rem;
        }

        .file-input {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .image-preview-area {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 1rem;
        }

        .image-preview {
            width: 120px;
            height: 120px;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            display: none;
            background-color: #f0f0f0;
            position: relative;
        }

        .image-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }

        .remove-image {
            position: absolute;
            top: 5px;
            right: 5px;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: var(--error-color);
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .remove-image:hover {
            background-color: var(--error-color);
            color: white;
        }

        .selected-file-info {
            margin-top: 0.5rem;
            font-size: 0.9rem;
            color: var(--text-color);
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 0.8rem 2rem;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            max-width: 300px;
            margin: 0 auto;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
        }

        .btn-primary:disabled {
            background-color: #cbd5e0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-container {
            display: flex;
            justify-content: center;
            margin-top: 1rem;
        }

        .spinner {
            margin-right: 10px;
            width: 18px;
            height: 18px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 0.8s linear infinite;
            display: none;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .response-container {
            margin-top: 2rem;
            display: none;
        }

        .response-card {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 1.5rem;
            transition: transform 0.3s ease;
        }

        .response-card:hover {
            transform: translateY(-5px);
        }

        .response-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid #eee;
        }

        .response-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-color);
            display: flex;
            align-items: center;
        }

        .response-title i {
            margin-right: 0.5rem;
            color: var(--primary-color);
        }

        .response-actions {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            background-color: #f1f1f1;
            color: var(--text-color);
            border: none;
            border-radius: 4px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background-color: #e1e1e1;
        }

        .action-btn.copy:hover {
            background-color: var(--success-color);
            color: #0d6832;
        }

        .response-content {
            background-color: #f7fafc;
            border-radius: 8px;
            padding: 1.25rem;
            white-space: pre-wrap;
            word-wrap: break-word;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 0.95rem;
            color: #2d3748;
            border: 1px solid #edf2f7;
            overflow-x: auto;
            max-height: 400px;
            overflow-y: auto;
            line-height: 1.6;
        }

        .status-badge {
            font-size: 0.8rem;
            padding: 0.3rem 0.8rem;
            border-radius: 50px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
        }

        .status-badge i {
            margin-right: 0.4rem;
        }

        .status-success {
            background-color: var(--success-color);
            color: #0d6832;
        }

        .status-error {
            background-color: #fee2e2;
            color: #b91c1c;
        }

        .copy-notification {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: var(--success-color);
            color: #0d6832;
            padding: 0.75rem 1.5rem;
            border-radius: 50px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            font-weight: 500;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .copy-notification.show {
            opacity: 1;
            transform: translateY(0);
        }

        .copy-notification i {
            margin-right: 0.5rem;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .test-card {
                padding: 1.5rem;
            }

            .test-card h1 {
                font-size: 1.5rem;
            }

            .image-preview {
                width: 100px;
                height: 100px;
            }

            .response-content {
                padding: 1rem;
                font-size: 0.85rem;
            }
        }
    </style>
{% endblock %}

{% block content %}
    <div class="test-container">
        <div class="test-card">
            <h1>AI Image & Text Prompt Tester</h1>
            <p class="test-description">Test AI responses with text prompts and optional images</p>

            <div class="form-group">
                <label for="textareaField" class="form-label">Your Prompt</label>
                <textarea id="textareaField" class="form-control" placeholder="Enter your prompt here..."></textarea>
            </div>

            <div class="form-group">
                <label for="imageInput" class="form-label">Upload Image (Optional)</label>
                <div id="imageUploadArea" class="image-upload-container">
                    <i class="fas fa-cloud-upload-alt upload-icon"></i>
                    <div class="upload-text">Drop your image here or click to browse</div>
                    <div class="upload-subtext">Supports JPG, PNG or GIF up to 5MB</div>
                    <input type="file" id="imageInput" class="file-input" accept="image/*">
                </div>

                <div class="image-preview-area">
                    <div id="imagePreview" class="image-preview">
                        <img id="previewImg" src="" alt="Preview">
                        <div id="removeImage" class="remove-image">
                            <i class="fas fa-times"></i>
                        </div>
                    </div>
                </div>
                <div id="selectedFileInfo" class="selected-file-info"></div>
            </div>

            <div class="btn-container">
                <button id="askButton" class="btn-primary">
                    <span class="spinner" id="spinner"></span>
                    <span id="buttonText">Generate Response</span>
                </button>
            </div>
        </div>

        <div id="responseContainer" class="response-container">
            <div class="response-card">
                <div class="response-header">
                    <div class="response-title">
                        <i class="fas fa-robot"></i> AI Response
                    </div>
                    <div class="response-actions">
                        <button id="copyButton" class="action-btn copy" title="Copy to clipboard">
                            <i class="fas fa-copy"></i>
                        </button>
                        <span id="statusBadge" class="status-badge"></span>
                    </div>
                </div>
                <div id="responseOutput" class="response-content"></div>
            </div>
        </div>

        <div id="copyNotification" class="copy-notification">
            <i class="fas fa-check-circle"></i> Copied to clipboard!
        </div>
    </div>
{% endblock %}

{% block scripts %}
<script>
   document.addEventListener('DOMContentLoaded', function() {
        // DOM elements
        const askButton = document.getElementById('askButton');
        const buttonText = document.getElementById('buttonText');
        const spinner = document.getElementById('spinner');
        const textareaField = document.getElementById('textareaField');
        const imageInput = document.getElementById('imageInput');
        const imageUploadArea = document.getElementById('imageUploadArea');
        const imagePreview = document.getElementById('imagePreview');
        const previewImg = document.getElementById('previewImg');
        const removeImageBtn = document.getElementById('removeImage');
        const selectedFileInfo = document.getElementById('selectedFileInfo');
        const responseContainer = document.getElementById('responseContainer');
        const responseOutput = document.getElementById('responseOutput');
        const statusBadge = document.getElementById('statusBadge');
        const copyButton = document.getElementById('copyButton');
        const copyNotification = document.getElementById('copyNotification');

        // Image upload handling
        imageInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                handleFileSelection(file);
            }
        });

        // Remove image button
        removeImageBtn.addEventListener('click', function(e) {
            e.stopPropagation(); // Prevent event bubbling
            resetImageUpload();
        });

        // Drag and drop functionality
        ['dragenter', 'dragover'].forEach(eventName => {
            imageUploadArea.addEventListener(eventName, function(e) {
                e.preventDefault();
                imageUploadArea.classList.add('active');
            }, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            imageUploadArea.addEventListener(eventName, function(e) {
                e.preventDefault();
                if (eventName === 'dragleave') {
                    imageUploadArea.classList.remove('active');
                }
                if (eventName === 'drop') {
                    const file = e.dataTransfer.files[0];
                    if (file && file.type.match('image.*')) {
                        imageInput.files = e.dataTransfer.files;
                        handleFileSelection(file);
                    }
                }
            }, false);
        });

        // Copy response to clipboard
        copyButton.addEventListener('click', function() {
            const textToCopy = responseOutput.textContent;
            navigator.clipboard.writeText(textToCopy).then(() => {
                showCopyNotification();
            }).catch(err => {
                console.error('Could not copy text: ', err);
            });
        });

        // Handle file selection
        function handleFileSelection(file) {
            // Format file size
            const fileSize = formatFileSize(file.size);
            selectedFileInfo.textContent = `${file.name} (${fileSize})`;

            // Show image preview
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                imagePreview.style.display = 'block';
            };
            reader.readAsDataURL(file);

            imageUploadArea.classList.add('active');
        }

        // Format file size
        function formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' bytes';
            else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
            else return (bytes / 1048576).toFixed(1) + ' MB';
        }

        // Reset image upload
        function resetImageUpload() {
            imageInput.value = '';
            selectedFileInfo.textContent = '';
            imagePreview.style.display = 'none';
            previewImg.src = '';
            imageUploadArea.classList.remove('active');
        }

        // Show copy notification
        function showCopyNotification() {
            copyNotification.classList.add('show');
            setTimeout(() => {
                copyNotification.classList.remove('show');
            }, 2000);
        }

        // API Request
        askButton.addEventListener('click', async function() {
            // Validate input
            if (!textareaField.value.trim()) {
                alert('Please enter a prompt before submitting.');
                textareaField.focus();
                return;
            }

            // Show loading state
            askButton.disabled = true;
            spinner.style.display = 'inline-block';
            buttonText.textContent = 'Processing...';

            try {
                let imageBase64 = null;

                // Convert image to base64 if one is selected
                if (imageInput.files[0]) {
                    imageBase64 = await convertImageToBase64(imageInput.files[0]);
                }

                // Prepare data with base64 image
                const data = {
                    message: textareaField.value,
                    image: imageBase64
                };

                // Make API call with JSON containing base64 image
                const response = await fetch('/api/chat-img', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                // Parse response
                const result = await response.json();

                // Display response with success styling
                responseOutput.textContent = result.response;
                statusBadge.innerHTML = `<i class="fas fa-check-circle"></i> Success`;
                statusBadge.className = 'status-badge status-success';
                responseContainer.style.display = 'block';

                // Smooth scroll to response
                responseContainer.scrollIntoView({ behavior: 'smooth' });

            } catch (error) {
                // Handle errors with error styling
                responseOutput.textContent = `Error: ${error.message}`;
                statusBadge.innerHTML = `<i class="fas fa-exclamation-circle"></i> Failed`;
                statusBadge.className = 'status-badge status-error';
                responseContainer.style.display = 'block';
            } finally {
                // Reset button state
                askButton.disabled = false;
                spinner.style.display = 'none';
                buttonText.textContent = 'Generate Response';
            }
        });

        // Helper function to convert image file to base64
        function convertImageToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => {
                    // Get the base64 string (remove the data URL prefix)
                    const base64String = reader.result.split(',')[1];
                    resolve(base64String);
                };
                reader.onerror = (error) => reject(error);
                reader.readAsDataURL(file);
            });
        }
   });
</script>
{% endblock %}