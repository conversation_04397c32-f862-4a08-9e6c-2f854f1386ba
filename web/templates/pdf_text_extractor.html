{% extends "base.html" %}

{% block title %}PDF Text Extractor{% endblock %}

{% block head %}
<style>
    :root {
        --primary: #4361ee;
        --secondary: #3f37c9;
        --success: #4cc9f0;
        --info: #4895ef;
        --warning: #f72585;
        --danger: #e63946;
        --light: #f8f9fa;
        --dark: #212529;
        --gray: #6c757d;
        --light-gray: #dee2e6;
    }

    .test-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px;
    }

    .test-card {
        background-color: white;
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        padding: 30px;
        width: 100%;
        max-width: 800px;
    }

    .test-description {
        color: var(--slate-gray);
        margin-bottom: 20px;
    }

    .form-option {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .form-option input[type="checkbox"] {
        margin-right: 10px;
        width: auto;
    }

    .form-option label {
        margin-bottom: 0;
    }

    .progress-bar-container {
        display: none; /* Will be set to block via JavaScript when needed */
        width: 100%;
        height: 5px;
        background-color: #f0f0f0;
        border-radius: 10px;
        margin-bottom: 10px;
        overflow: hidden;
    }

    .progress-bar {
        height: 100%;
        background-color: var(--warning);
        width: 0%;
        transition: width 0.3s ease;
    }

    .progress-percentage {
        text-align: center;
        font-weight: bold;
    }

    .progress-step {
        text-align: center;
        font-style: italic;
        color: #666;
        margin-bottom: 15px;
    }

    .progress-log-container {
        margin-top: 20px;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 10px;
        background-color: #f9f9f9;
    }

    .progress-log-container details {
        margin: 0;
        padding: 0;
        border: none;
        background-color: transparent;
    }

    .progress-log-container summary {
        font-weight: bold;
        color: var(--warning);
        cursor: pointer;
        padding: 5px 0;
        margin-bottom: 10px;
    }

    .progress-log-container summary:hover {
        color: var(--dark);
    }

    .log-entry-count {
        display: inline-block;
        background-color: var(--warning);
        color: white;
        border-radius: 10px;
        padding: 2px 8px;
        font-size: 12px;
        margin-left: 8px;
    }

    .progress-log {
        max-height: 300px;
        overflow-y: auto;
        font-family: monospace;
        font-size: 13px;
        line-height: 1.6;
        padding: 10px;
        background-color: #f0f0f0;
        border-radius: 5px;
        scroll-behavior: smooth;
    }

    .progress-log-entry {
        margin-bottom: 8px;
        padding-bottom: 8px;
        border-bottom: 1px dashed #ddd;
        animation: fadeIn 0.5s;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .progress-log-entry:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .progress-log-time {
        color: #666;
        margin-right: 10px;
    }

    .status-container {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-top: 20px;
    }

    .status-item {
        flex: 1;
        min-width: 300px;
    }

    .status-content {
        background-color: #f5f5f5;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 10px;
        font-family: monospace;
        font-size: 14px;
        line-height: 1.5;
        white-space: pre-wrap;
        word-break: break-word;
    }

    .loading-message {
        font-style: italic;
        color: #666;
        margin: 10px 0;
    }
    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #ccc;
      border-top: 4px solid #1d72b8;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 50px auto;
      display: none;
    }

    /* Table styles */
    .data-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
        font-size: 14px;
    }

    .data-table th,
    .data-table td {
        padding: 10px;
        text-align: left;
        border: 1px solid #ddd;
    }

    .data-table th {
        background-color: #f2f2f2;
        font-weight: bold;
    }

    .data-table tr:nth-child(even) {
        background-color: #f9f9f9;
    }

    .data-table tr:hover {
        background-color: #f0f0f0;
    }

    /* Button styles */
    .primary-button {
        background-color: var(--primary);
        color: white;
        border: none;
        padding: 10px 15px;
        border-radius: 5px;
        cursor: pointer;
        font-weight: bold;
    }

    .primary-button:hover {
        background-color: var(--secondary);
    }

    .primary-button:disabled {
        background-color: #ccc;
        cursor: not-allowed;
    }

    .view-button {
        background-color: var(--info);
        color: white;
        border: none;
        padding: 5px 10px;
        border-radius: 3px;
        cursor: pointer;
    }

    .view-button:hover {
        background-color: var(--primary);
    }

    .view-button:disabled {
        background-color: #ccc;
        cursor: not-allowed;
    }

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

</style>
{% endblock %}

{% block content %}
<div class="test-container">
    <div class="test-card">
        <h1>PDF Text Extractor</h1>
        <p class="test-description">Enter a book ID to extract text from PDF files for all chapters</p>

        <form id="extractorForm" class="login-form">
            <label for="bookId">Enter Book ID</label>
            <input type="text" id="bookId" name="bookId" required>
            <button type="submit">Fetch Chapters</button>
        </form>
        <div id="spinner" class="spinner"></div>

        <div id="chaptersContainer" style="display: none; margin-top: 20px;">
            <div class="button-container" style="margin-bottom: 20px;">
                <button id="extractAllButton" class="primary-button">Extract Text for All Chapters</button>
            </div>
            <h3 style="color: var(--dark); margin-bottom: 10px;">Chapters:</h3>
            <div class="table-container">
                <table id="chaptersTable" class="data-table" style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr>
                            <th>Chapter ID</th>
                            <th>Chapter Name</th>
                            <th>Status</th>
                            <th>View Content</th>
                        </tr>
                    </thead>
                    <tbody id="chaptersTableBody">
                        <!-- Chapters will be added here dynamically -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Modal for displaying content -->
        <div id="contentModal" class="modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.4);">
            <div class="modal-content" style="background-color: #fefefe; margin: 10% auto; padding: 20px; border: 1px solid #888; width: 80%; max-height: 70vh; overflow-y: auto;">
                <span class="close" style="color: #aaa; float: right; font-size: 28px; font-weight: bold; cursor: pointer;">&times;</span>
                <h3 id="modalTitle">Chapter Content</h3>
                <pre id="modalContent" style="white-space: pre-wrap; word-break: break-word;"></pre>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Get the base URL from the current location
const BASE_URL = window.location.origin;
console.log('BASE_URL:', BASE_URL);
const form = document.getElementById('extractorForm');
const chaptersContainer = document.getElementById('chaptersContainer');
const chaptersTableBody = document.getElementById('chaptersTableBody');
const extractAllButton = document.getElementById('extractAllButton');
const spinner = document.getElementById('spinner');
const contentModal = document.getElementById('contentModal');
const modalContent = document.getElementById('modalContent');
const modalTitle = document.getElementById('modalTitle');
const closeModalButton = document.querySelector('.close');

// Close the modal when clicking the close button
closeModalButton.addEventListener('click', function() {
    contentModal.style.display = 'none';
});

// Close the modal when clicking outside of it
window.addEventListener('click', function(event) {
    if (event.target === contentModal) {
        contentModal.style.display = 'none';
    }
});

// Function to show the modal with content
function showContentModal(chapterName, content) {
    modalTitle.textContent = `Chapter Content: ${chapterName}`;
    modalContent.textContent = content;
    contentModal.style.display = 'block';
}

// Function to fetch chapters for a book
async function fetchChapters(bookId) {
    try {
        spinner.style.display = 'block';
        const response = await fetch(`/api/get-chapters-by-book/${bookId}`);

        if (!response.ok) {
            throw new Error('Failed to fetch chapters');
        }

        const data = await response.json();
        spinner.style.display = 'none';

        if (data.status === 'success') {
            displayChapters(data.chapters);
            chaptersContainer.style.display = 'block';
        } else {
            alert(`Error: ${data.message}`);
        }
    } catch (error) {
        spinner.style.display = 'none';
        console.error('Error fetching chapters:', error);
        alert(`Error fetching chapters: ${error.message}`);
    }
}

// Function to display chapters in the table
function displayChapters(chapters) {
    chaptersTableBody.innerHTML = '';

    chapters.forEach(chapter => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${chapter.id}</td>
            <td>${chapter.name}</td>
            <td id="status-${chapter.id}">Pending</td>
            <td>
                <button id="view-${chapter.id}" class="view-button" disabled>View</button>
            </td>
        `;
        chaptersTableBody.appendChild(row);
    });
}

// Function to set up view button for a chapter
function setupViewButton(chapterId, chapterName, resourceId) {
    const viewButton = document.getElementById(`view-${chapterId}`);
    if (!viewButton) return;

    // Enable the button
    viewButton.disabled = false;

    // Remove any existing event listeners (to prevent duplicates)
    const newButton = viewButton.cloneNode(true);
    viewButton.parentNode.replaceChild(newButton, viewButton);

    // Add event listener to the new button
    newButton.addEventListener('click', async function() {
        try {
            // Fetch the extracted text
            const contentResponse = await fetch(`/api/read-extracted-text?res_id=${resourceId}`);

            if (!contentResponse.ok) {
                throw new Error('Failed to fetch content');
            }

            const contentData = await contentResponse.json();

            if (contentResponse.ok && contentData.content) {
                // Show the content in the modal
                showContentModal(chapterName, contentData.content);
            } else {
                alert(`Error: ${contentData.message || 'No content available'}`);
            }
        } catch (error) {
            console.error('Error viewing content:', error);
            alert(`Error viewing content: ${error.message}`);
        }
    });
}

// Function to extract text for a chapter
async function extractTextForChapter(chapterId, chapterName) {
    try {
        // Update status
        const statusCell = document.getElementById(`status-${chapterId}`);
        statusCell.textContent = 'Processing...';

        // Call the API to extract text
        const response = await fetch('/api/pdf-text-extract-chapter', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                chapterId: chapterId
            })
        });

        if (!response.ok) {
            throw new Error('Failed to extract text');
        }

        const data = await response.json();

        if (data.status === 'success') {
            // Update status
            statusCell.textContent = 'Completed';

            // Set up the view button with the resource ID from the response
            setupViewButton(chapterId, chapterName, data.resource_id);

            return true;
        } else {
            // Update status with more descriptive message
            if (data.message && data.message.includes('No PDF resource found')) {
                statusCell.textContent = 'No PDF found';
            } else {
                statusCell.textContent = `Failed: ${data.message}`;
            }
            return false;
        }
    } catch (error) {
        // Update status
        const statusCell = document.getElementById(`status-${chapterId}`);
        statusCell.textContent = `Error: ${error.message}`;
        console.error(`Error extracting text for chapter ${chapterId}:`, error);
        return false;
    }
}

// Function to extract text for all chapters
async function extractTextForAllChapters() {
    // Disable the extract all button
    extractAllButton.disabled = true;
    extractAllButton.textContent = 'Processing...';

    // Get all chapters from the table
    const rows = chaptersTableBody.querySelectorAll('tr');

    // Process each chapter sequentially
    for (const row of rows) {
        const chapterId = row.cells[0].textContent;
        const chapterName = row.cells[1].textContent;

        await extractTextForChapter(chapterId, chapterName);
    }

    // Re-enable the extract all button
    extractAllButton.disabled = false;
    extractAllButton.textContent = 'Extract Text for All Chapters';
}

// Event listener for the form submission
form.addEventListener('submit', async function(e) {
    e.preventDefault();
    const formData = new FormData(form);
    const bookId = formData.get('bookId');

    if (bookId) {
        await fetchChapters(bookId);
    } else {
        alert('Please enter a Book ID');
    }
});

// Event listener for the extract all button
extractAllButton.addEventListener('click', extractTextForAllChapters);
</script>
{% endblock %}
