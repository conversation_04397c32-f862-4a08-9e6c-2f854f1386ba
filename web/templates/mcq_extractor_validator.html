{% extends "base.html" %}

{% block title %}MCQ Extractor & Validator{% endblock %}

{% block content %}
<div class="test-container">
    <div class="test-card">
        <h1>MCQ Extractor & Validator</h1>
        <p class="test-description">Enter a resource ID to extract and validate MCQs</p>

        <form id="extractorValidatorForm" class="login-form">
            <label for="resId">Enter Res ID</label>
            <input type="text" id="resId" name="resId" required>
            <button type="submit">Extract & Validate</button>
        </form>
        <div id="spinner" class="spinner"></div>
        <div id="progressLog" class="progress-log" style="display: none;"></div>

        <div id="resultsContainer" style="display: none; margin-top: 20px;">
            <h3>Validation Results:</h3>
            <div id="validationSummary" class="validation-summary"></div>
            <div id="validationDetails" class="validation-details"></div>
        </div>

        <style>
            .progress-log {
                background-color: #f5f5f5;
                padding: 15px;
                border-radius: 5px;
                margin: 20px 0;
                max-height: 200px;
                overflow-y: auto;
                font-family: monospace;
            }

            .validation-summary {
                background-color: #f5f5f5;
                padding: 15px;
                border-radius: 5px;
                margin-bottom: 20px;
            }

            .validation-details {
                margin-top: 20px;
            }

            .mcq-item {
                border: 1px solid #ddd;
                border-radius: 5px;
                margin-bottom: 15px;
                overflow: hidden;
            }

            .mcq-header {
                padding: 10px 15px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-weight: bold;
            }

            .status-valid {
                background-color: #d4edda;
                color: #155724;
            }

            .status-invalid {
                background-color: #f8d7da;
                color: #721c24;
            }

            .mcq-content {
                padding: 15px;
                background-color: white;
            }

            .spinner {
                display: none;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #3498db;
                border-radius: 50%;
                width: 30px;
                height: 30px;
                animation: spin 1s linear infinite;
                margin: 20px auto;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .skipped-extractions {
                background-color: #fff3cd;
                color: #856404;
                padding: 15px;
                border-radius: 5px;
                margin-top: 15px;
                border: 1px solid #ffeeba;
            }

            .log-warning {
                color: #856404;
                background-color: #fff3cd;
                padding: 5px;
                border-radius: 3px;
                margin: 5px 0;
            }
        </style>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="/static/mcq_extractor_validator.js"></script>
{% endblock %}
