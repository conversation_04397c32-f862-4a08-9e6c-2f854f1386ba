{% extends "base.html" %}

{% block title %}Extractor Test | GPT Sir{% endblock %}

{% block head %}
<!-- KaTeX CSS and JavaScript -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" integrity="sha384-nB0miv6/jRmo5UMMR1wu3Gz6NLsoTkbqJghGIsx//Rlm+ZU03BU6SQNC66uf4l5+" crossorigin="anonymous">
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" integrity="sha384-7zkQWkzuo3B5mTepMUcHkMB5jZaolc2xDwL6VFqjFALcbeS9Ggm/Yr2r3Dy4lfFg" crossorigin="anonymous"></script>
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-browser/0.1.0/jquery.browser.min.js" defer></script>

<style>
    :root {
  --primary: #4361ee;
  --secondary: #3f37c9;
  --success: #4cc9f0;
  --info: #4895ef;
  --warning: #f72585;
  --danger: #e63946;
  --light: #f8f9fa;
  --dark: #212529;
  --gray: #6c757d;
  --light-gray: #dee2e6;
}
    details {
        margin: 10px 0;
        padding: 10px;
        background-color: #f9f9f9;
        border-radius: 5px;
        border: 1px solid #ddd;
    }

    summary {
        cursor: pointer;
        font-weight: bold;
        padding: 5px;
        color: var(--cerise);
    }

    details pre {
        margin: 10px 0 0 0;
        padding: 10px;
        background-color: #f0f0f0;
        border-radius: 5px;
        overflow-x: auto;
        max-height: 300px;
    }

    .loading-message {
        font-style: italic;
        color: #666;
        margin: 10px 0;
    }

    .item-wrap {
        margin-bottom: 30px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        background-color: white;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .item-wrap pre {
        white-space: pre-wrap;
        word-break: break-word;
        font-family: inherit;
        margin: 0 0 15px 0;
        padding: 10px;
        background-color: #f9f9f9;
        border-radius: 5px;
        font-size: 14px;
        line-height: 1.5;
    }

    .mcq-container {
        max-height: 600px;
        overflow-y: auto;
        padding: 10px;
        border: 1px solid #eee;
        border-radius: 5px;
        margin-top: 15px;
    }

    .mcq-pagination {
        display: flex;
        justify-content: space-between;
        margin: 10px 0;
    }

    .mcq-pagination button {
        background-color: var(--cerise);
        color: white;
        border: none;
        padding: 5px 15px;
        border-radius: 5px;
        cursor: pointer;
    }

    .mcq-pagination button:disabled {
        background-color: #ccc;
        cursor: not-allowed;
    }

    .mcq-pagination-info {
        line-height: 30px;
    }

    .form-option {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .form-option input[type="checkbox"] {
        margin-right: 10px;
        width: auto;
    }

    .form-option label {
        margin-bottom: 0;
    }

    .progress-bar-container {
        display: none;
        width: 100%;
        height: 20px;
        background-color: #f0f0f0;
        border-radius: 10px;
        margin-bottom: 10px;
        overflow: hidden;
    }

    .progress-bar {
        height: 100%;
        background-color: var(--cerise);
        width: 0%;
        transition: width 0.3s ease;
    }

    .progress-percentage {
        text-align: center;
        font-weight: bold;
    }

    .progress-step {
        text-align: center;
        font-style: italic;
        color: #666;
        margin-bottom: 15px;
    }

    .progress-log-container {
        margin-top: 20px;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 10px;
        background-color: #f9f9f9;
    }

    .progress-log-container details {
        margin: 0;
        padding: 0;
        border: none;
        background-color: transparent;
    }

    .progress-log-container summary {
        font-weight: bold;
        color: var(--cerise);
        cursor: pointer;
        padding: 5px 0;
        margin-bottom: 10px;
    }

    .progress-log-container summary:hover {
        color: var(--gunmetal);
    }

    .log-entry-count {
        display: inline-block;
        background-color: var(--cerise);
        color: white;
        border-radius: 10px;
        padding: 2px 8px;
        font-size: 12px;
        margin-left: 8px;
    }

    .progress-log {
        max-height: 300px;
        overflow-y: auto;
        font-family: monospace;
        font-size: 13px;
        line-height: 1.6;
        padding: 10px;
        background-color: #f0f0f0;
        border-radius: 5px;
        scroll-behavior: smooth;
    }

    .progress-log-entry {
        margin-bottom: 8px;
        padding-bottom: 8px;
        border-bottom: 1px dashed #ddd;
        animation: fadeIn 0.5s;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .progress-log-entry:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .progress-log-time {
        color: #666;
        margin-right: 10px;
    }
    .quiz-container {
  padding: 20px;
}
.item-wrap {
  border-bottom: 1px dashed;
  padding-bottom: 16px;
  padding-top: 16px;
}
.item-wrap pre {
  white-space: break-spaces;
}
.item-wrap:last-child {
  border-bottom: none;
}
    .data-table {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--light-gray);
}
.table-title {
  font-size: 1.1rem;
  font-weight: 600;
}
table {
  width: auto;
  border-collapse: collapse;
}
th {
  padding: 5px 10px;
  text-align: left;
  font-size: 0.9rem;
  text-transform: uppercase;
  color: var(--gray);
  font-weight: 600;
  letter-spacing: 0.5px;
}
td {
  padding: 5px 10px;
  text-align: left;
  font-size: 0.95rem;
}
tr {
  border-bottom: 1px solid var(--light-gray);
}
tr:last-child {
  border-bottom: none;
}
.katex-display>.katex{
    white-space: pre-wrap !important;
    text-align: unset;
}
.katex-display{
    text-align: unset !important;
}
</style>
{% endblock %}

{% block content %}
<div class="test-container">
    <div class="test-card">
        <h1>Extractor Test</h1>
        <p class="test-description">Enter a resource ID to extract data</p>

        <form id="extractorForm" class="login-form">
            <label for="resId">Enter Res ID</label>
            <input type="text" id="resId" name="resId" required>

            <div class="form-option">
                <input type="checkbox" id="showProcessStatus" name="showProcessStatus" checked>
                <label for="showProcessStatus" style="margin-bottom: 1.2rem;">Show real-time progress updates</label>
            </div>

            <div class="form-option" style="display: none;">
                <input type="checkbox" id="debugMode" name="debugMode">
                <label for="debugMode">Debug mode (show raw updates)</label>
            </div>

            <button type="submit">Extract</button>
        </form>

        <div id="progress" style="display: none; margin-top: 20px;">
            <h3 style="color: var(--gunmetal); margin-bottom: 10px; display: flex; align-items: center">Progress:
                <span class="progress-percentage" id="progressPercentage">0%</span>
            </h3>
            <div class="progress-bar-container">
                <div class="progress-bar" id="progressBar"></div>
            </div>

            <div class="progress-step" id="progressStep">Initializing...</div>

            <div class="progress-log-container">
                <details open>
                    <summary>Status Log <span id="logEntryCount" class="log-entry-count">0</span></summary>
                    <div id="progressLog" class="progress-log"></div>
                </details>
            </div>
        </div>

        <div id="result" style="display: none; margin-top: 20px;">
            <h3 style="color: var(--gunmetal); margin-bottom: 10px;">Result:</h3>
            <div class="status-container">
                <div class="status-item">
                    <div id="resultContent" style="margin: 0; width: 100%;"></div>
                </div>
            </div>
        </div>
        <div class="data-table">
            <div class="table-header">
                <div class="table-title"></div>
            </div>
            <div id="quiz-container" class="quiz-container"></div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Get the base URL from the current location
const BASE_URL = "https://qa.wonderslate.com/";
console.log('BASE_URL:', BASE_URL);

// Add event listener to save and restore the status log details state
document.addEventListener('DOMContentLoaded', function() {
    const statusLogDetails = document.querySelector('.progress-log-container details');
    if (statusLogDetails) {
        // Restore state from localStorage
        const savedState = localStorage.getItem('statusLogDetailsOpen');
        if (savedState !== null) {
            statusLogDetails.open = savedState === 'true';
        }

        // Save state to localStorage when toggled
        statusLogDetails.addEventListener('toggle', function() {
            localStorage.setItem('statusLogDetailsOpen', this.open);
        });
    }
});
</script>
<script src="/static/fixed_display_results.js"></script>
<script>
// Add a global error handler to catch any unhandled errors
window.addEventListener('error', function(event) {
    console.error('Global error caught:', event.error);
});

// Directly add the code to override the displayResults function
document.addEventListener('DOMContentLoaded', function() {
    console.log('Adding direct override for displayResults');

    // Override the displayResults function
    window.displayResults = async function(data) {
        console.log('Direct override displayResults called with:', data);

        // Check if the response contains the extracted_data_path
        if (data.extracted_data_path) {
            console.log('Found extracted_data_path:', data.extracted_data_path);

            try {
                // Call the API to read the extracted data
                const response = await fetch(`/api/read-extracted-data?file_path=${encodeURIComponent(data.extracted_data_path)}`);

                if (!response.ok) {
                    // Try to get the error message from the response
                    let errorDetail = '';
                    try {
                        const errorJson = await response.json();
                        errorDetail = errorJson.detail || '';
                    } catch (e) {
                        // If we can't parse the JSON, try to get the text
                        try {
                            errorDetail = await response.text();
                        } catch (e2) {
                            // If we can't get the text either, just use the status
                            errorDetail = 'Unknown error';
                        }
                    }
                    throw new Error(`Failed to fetch extracted data: ${response.status} ${response.statusText} - ${errorDetail}`);
                }

                // Parse the response as JSON
                let extractedData;
                try {
                    extractedData = await response.json();
                    console.log('Received extracted data:', extractedData);
                } catch (jsonError) {
                    console.error('Error parsing JSON response:', jsonError);
                    console.log('Response text:', await response.text());
                    throw jsonError;
                }
                processContent({...data, ...extractedData})
                addLogEntry('Results displayed successfully');
                // Check if we got any data
                if (extractedData.mcqs && extractedData.mcqs.length > 0) {
                    // Replace the MCQs, answer keys, and explanations with the data from the text file
                    data.mcqs = extractedData.mcqs;
                    data.answer_keys = extractedData.answer_keys;
                    data.explanations = extractedData.explanations;

                    console.log('Successfully loaded extracted data:',
                        `MCQs: ${extractedData.mcqs.length}`,
                        `Answer Keys: ${Object.keys(extractedData.answer_keys).length}`,
                        `Explanations: ${extractedData.explanations.length}`);
                } else {
                    console.warn('No MCQs found in the extracted data');

                    // Check if we got raw file content for debugging
                    if (extractedData.raw_file_content) {
                        console.log('Raw file content:', extractedData.raw_file_content);

                        // Try to parse the raw file content as JSON
                        try {
                            const rawData = JSON.parse(extractedData.raw_file_content);
                            console.log('Successfully parsed raw file content as JSON:', rawData);

                            // If it has mcqs, use them
                            if (rawData.mcqs && rawData.mcqs.length > 0) {
                                data.mcqs = rawData.mcqs;
                                data.answer_keys = rawData.answer_keys || {};
                                data.explanations = rawData.explanations || [];

                                console.log('Using data from raw file content:',
                                    `MCQs: ${data.mcqs.length}`,
                                    `Answer Keys: ${Object.keys(data.answer_keys).length}`,
                                    `Explanations: ${data.explanations.length}`);
                            }
                        } catch (e) {
                            console.error('Error parsing raw file content as JSON:', e);
                        }
                    }
                }
            } catch (error) {
                console.error('Error loading extracted data:', error);
            }
        }
    };
});
</script>
<script>
// Function to format timestamp
function formatTime(timestamp) {
    const date = new Date();
    return date.toLocaleTimeString('en-US', { hour12: false });
}

// Function to add a log entry
function addLogEntry(message) {
    const progressLog = document.getElementById('progressLog');
    const entry = document.createElement('div');
    entry.className = 'progress-log-entry';

    const time = document.createElement('span');
    time.className = 'progress-log-time';
    time.textContent = formatTime();

    const text = document.createElement('span');
    text.className = 'progress-log-text';
    text.textContent = message;

    entry.appendChild(time);
    entry.appendChild(text);
    progressLog.appendChild(entry);

    // Update the log entry count
    const logEntryCount = document.getElementById('logEntryCount');
    if (logEntryCount) {
        const count = parseInt(logEntryCount.textContent) + 1;
        logEntryCount.textContent = count;
    }

    // Scroll to bottom with a slight delay to ensure the animation completes
    setTimeout(() => {
        progressLog.scrollTop = progressLog.scrollHeight;

        // Auto-open the details element if it's closed
        const detailsElement = progressLog.closest('details');
        if (detailsElement && !detailsElement.open) {
            detailsElement.open = true;
        }
    }, 100);

    // Also update the progress step text for important messages
    if (message.includes('Converting') ||
        message.includes('Processing') ||
        message.includes('Extracting') ||
        message.includes('Completed')) {
        document.getElementById('progressStep').textContent = message;
    }
}

// Global variables
let eventSource = null;
let pollingInterval = null;
let currentTaskId = null;

// Function to poll for task status as a fallback
async function pollTaskStatus() {
    if (!currentTaskId) {
        console.error('No task ID available for polling');
        return;
    }

    try {
        console.log(`Polling for task ${currentTaskId}...`);
        const response = await fetch(`/api/task/${currentTaskId}/status`);

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Polled task status:', data);

        // Process the data similar to SSE updates
        if (data.progress !== undefined) {
            document.getElementById('progressBar').style.width = `${data.progress}%`;
            document.getElementById('progressPercentage').textContent = `${data.progress}%`;
        }

        if (data.current_step) {
            const currentStep = data.current_step;
            document.getElementById('progressStep').textContent = currentStep;

            // if (!seenSteps.has(currentStep)) {
            //     addLogEntry(currentStep);
            //     seenSteps.set(currentStep, true);
            //     console.log(`Added new step from polling: ${currentStep}`);
            // }
        }

        if (data.steps && data.steps.length > 0) {
            console.log(`Polling found ${data.steps.length} steps`);

            // Sort steps by timestamp if available
            const sortedSteps = [...data.steps].sort((a, b) => {
                return (a.timestamp || 0) - (b.timestamp || 0);
            });

            // Process all steps in order
            let newStepsAdded = 0;
            sortedSteps.forEach(stepObj => {
                if (stepObj.step && !seenSteps.has(stepObj.step)) {
                    addLogEntry(stepObj.step);
                    seenSteps.set(stepObj.step, true);
                    newStepsAdded++;
                }
            });

            if (newStepsAdded > 0) {
                console.log(`Added ${newStepsAdded} new steps from polling`);
            }
        }

        // If task is completed or failed, stop polling
        if (data.status === 'completed') {
            console.log('Task completed, stopping polling');
            stopPolling();
            fetchResults(data);
        } else if (data.status === 'failed') {
            console.log('Task failed, stopping polling');
            stopPolling();
        }
    } catch (error) {
        console.error('Error polling task status:', error);
    }
}

// Function to start polling
function startPolling(taskId) {
    // Stop any existing polling
    stopPolling();

    // Set the current task ID
    currentTaskId = taskId;

    // Start polling every 2 seconds
    pollingInterval = setInterval(pollTaskStatus, 2000);
    console.log(`Started polling for task ${taskId}`);
}

// Function to stop polling
function stopPolling() {
    if (pollingInterval) {
        clearInterval(pollingInterval);
        pollingInterval = null;
        console.log('Stopped polling');
    }
}

function connectToSSE(taskId) {
    // Close any existing connection
    if (eventSource) {
        eventSource.close();
        eventSource = null;
    }

    // Set the current task ID for polling
    currentTaskId = taskId;

    // Get DOM elements
    const progressDiv = document.getElementById('progress');
    const progressBar = document.getElementById('progressBar');
    const progressPercentage = document.getElementById('progressPercentage');
    const progressStep = document.getElementById('progressStep');
    const progressLog = document.getElementById('progressLog');

    // Clear previous log entries
    progressLog.innerHTML = '';

    // Reset the log entry count
    const logEntryCount = document.getElementById('logEntryCount');
    if (logEntryCount) {
        logEntryCount.textContent = '0';
    }

    // Show progress container
    progressDiv.style.display = 'block';

    // Add initial log entry
    addLogEntry(`Starting to track progress for task ${taskId}...`);
    addLogEntry('Connecting to server for real-time updates...');

    // Start polling as a fallback
    startPolling(taskId);
    addLogEntry('Started polling as a fallback mechanism...');

    try {
        // Create EventSource for SSE connection
        eventSource = new EventSource(`/api/progress/${taskId}`);
        console.log(`Connected to SSE for task ${taskId}`);
    } catch (error) {
        console.error('Error creating SSE connection:', error);
        addLogEntry(`Error connecting to server: ${error.message}`);
        return null;
    }

    // Track seen steps to avoid duplicates
    const seenSteps = new Map();

    // Handle connection open
    eventSource.addEventListener('connected', (event) => {
        console.log('SSE connection established');
        addLogEntry('Connected to server. Waiting for updates...');

        // Fetch the current status immediately to ensure we have the latest data
        fetch(`/api/task/${taskId}/status`)
            .then(response => response.json())
            .then(data => {
                console.log('Initial task status:', data);

                // Process the initial status
                if (data.progress !== undefined) {
                    progressBar.style.width = `${data.progress}%`;
                    progressPercentage.textContent = `${data.progress}%`;
                }

                if (data.current_step) {
                    progressStep.textContent = data.current_step;
                    if (!seenSteps.has(data.current_step)) {
                        addLogEntry(data.current_step);
                        seenSteps.set(data.current_step, true);
                    }
                }

                // Process any steps in the history
                if (data.steps && data.steps.length > 0) {
                    data.steps.forEach(stepObj => {
                        if (stepObj.step && !seenSteps.has(stepObj.step)) {
                            addLogEntry(stepObj.step);
                            seenSteps.set(stepObj.step, true);
                        }
                    });
                }
            })
            .catch(error => {
                console.error('Error fetching initial status:', error);
            });
    });

    // Handle progress updates
    eventSource.addEventListener('update', (event) => {
        const data = JSON.parse(event.data);
        console.log('Progress update:', data);

        // If debug mode is enabled, show the raw data
        if (document.getElementById('debugMode').checked) {
            addLogEntry(`DEBUG: ${JSON.stringify(data).substring(0, 100)}...`);
        }

        // Update progress bar and text
        if (data.progress !== undefined) {
            progressBar.style.width = `${data.progress}%`;
            progressPercentage.textContent = `${data.progress}%`;
        }

        // Check if there's a new step
        if (data.current_step) {
            const currentStep = data.current_step;
            progressStep.textContent = currentStep;

            // Add to log if it's a new step
            if (!seenSteps.has(currentStep)) {
                addLogEntry(currentStep);
                seenSteps.set(currentStep, true);
                console.log(`Added new step to log: ${currentStep}`);
            }
        }

        // Check if there are steps in the history that we haven't logged yet
        if (data.steps && data.steps.length > 0) {
            console.log(`Processing ${data.steps.length} steps from history`);

            // Sort steps by timestamp if available
            const sortedSteps = [...data.steps].sort((a, b) => {
                return (a.timestamp || 0) - (b.timestamp || 0);
            });

            // Process all steps in order
            let newStepsAdded = 0;
            sortedSteps.forEach(stepObj => {
                if (stepObj.step && !seenSteps.has(stepObj.step)) {
                    addLogEntry(stepObj.step);
                    seenSteps.set(stepObj.step, true);
                    newStepsAdded++;
                }
            });

            if (newStepsAdded > 0) {
                console.log(`Added ${newStepsAdded} new steps from history`);
            }
        }

        // If task is completed or failed, close the connection and stop polling
        if (data.status === 'completed') {
            addLogEntry('Task completed successfully!');
            closeSSE(); // This also stops polling
            checkExtractionResults(taskId);
        } else if (data.status === 'failed') {
            addLogEntry(`Task failed: ${data.error || 'Unknown error'}`);
            closeSSE(); // This also stops polling
        }
    });

    // Handle completion event
    eventSource.addEventListener('completed', (event) => {
        console.log('Task completed');
        const data = JSON.parse(event.data);
        addLogEntry('Extraction completed successfully!');
        closeSSE(); // This also stops polling
        checkExtractionResults(taskId);
    });

    // Handle failure event
    eventSource.addEventListener('failed', (event) => {
        console.log('Task failed');
        const data = JSON.parse(event.data);
        addLogEntry(`Extraction failed: ${data.error || 'Unknown error'}`);
        closeSSE(); // This also stops polling
    });

    // Handle errors
    eventSource.addEventListener('error', (event) => {
        console.error('SSE connection error:', event);
        addLogEntry('SSE connection error. Continuing with polling fallback...');

        // Close the current connection but don't stop polling
        if (eventSource) {
            eventSource.close();
            eventSource = null;
        }

        // We'll rely on the polling fallback which is already running
        addLogEntry('Using polling fallback to continue tracking progress...');

        // Try to reconnect after a short delay
        setTimeout(() => {
            console.log('Attempting to reconnect SSE...');
            try {
                // Create a new EventSource
                const newEventSource = new EventSource(`/api/progress/${taskId}`);

                // Copy all event listeners from the old EventSource
                const eventTypes = ['connected', 'update', 'completed', 'failed', 'error'];
                eventTypes.forEach(type => {
                    const listeners = eventSource?._listeners?.[type] || [];
                    listeners.forEach(listener => {
                        newEventSource.addEventListener(type, listener);
                    });
                });

                // Replace the old EventSource with the new one
                eventSource = newEventSource;
                addLogEntry('Reconnected to SSE server.');
            } catch (error) {
                console.error('Error reconnecting to SSE:', error);
                addLogEntry(`Error reconnecting to SSE: ${error.message}. Continuing with polling.`);
            }
        }, 5000);
    });

    // Store event listeners for reconnection
    eventSource._listeners = eventSource._listeners || {};
    ['connected', 'update', 'completed', 'failed'].forEach(type => {
        eventSource._listeners[type] = eventSource._listeners[type] || [];
    });

    return eventSource;
}

// Function to close SSE connection and stop polling
function closeSSE() {
    // Close SSE connection
    if (eventSource) {
        eventSource.close();
        eventSource = null;
        console.log('Closed SSE connection');
    }

    // Stop polling
    stopPolling();
}

document.getElementById('extractorForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    const resId = document.getElementById('resId').value;
    const showProcessStatus = document.getElementById('showProcessStatus').checked;
    const resultDiv = document.getElementById('result');
    const progressDiv = document.getElementById('progress');
    const resultContent = document.getElementById('resultContent');
    const statusItem = resultDiv.querySelector('.status-item');

    // Reset and hide result div initially
    resultDiv.style.display = 'none';
    statusItem.classList.remove('success', 'failure');
    resultContent.innerHTML = '';

    try {
        // If showing process status, prepare the progress UI first
        if (showProcessStatus) {
            // Show progress container and reset
            progressDiv.style.display = 'block';
            document.getElementById('progressBar').style.width = '0%';
            document.getElementById('progressPercentage').textContent = '0%';
            document.getElementById('progressStep').textContent = 'Initializing...';
            document.getElementById('progressLog').innerHTML = '';

            // Reset the log entry count
            const logEntryCount = document.getElementById('logEntryCount');
            if (logEntryCount) {
                logEntryCount.textContent = '0';
            }

            addLogEntry(`Starting extraction for resource ID: ${resId}`);
            addLogEntry('Sending extraction request to server...');
        }

        // Make the API request to get the task ID
        const response = await fetch('/api/extract', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                resId: resId,
                showProcessStatus: showProcessStatus
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || `HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        addLogEntry('Received initial response from server');

        // Get the task ID from the response
        const taskId = data.task_id;

        if (!taskId) {
            throw new Error('No task ID returned from server');
        }

        console.log(`Received task ID: ${taskId}`);

        if (showProcessStatus) {
            // Connect to SSE for real-time updates
            connectToSSE(taskId);

            // For processing status, we'll wait for SSE to complete
            if (data.status === 'processing') {
                addLogEntry(`Extraction is processing in the background for resource ID: ${resId}`);
                return; // Exit early, SSE will handle the rest
            }
        } else {
            // If not showing progress, show loading in result div
            resultDiv.style.display = 'block';
            statusItem.classList.add('loading');
            resultContent.innerHTML = '<div class="loading-message">Extracting data... This may take a few minutes.</div><div class="loading-message">The system is processing the PDF file and extracting MCQs, answer keys, and explanations.</div>';
        }

        // Format the response for better readability
        let formattedResult = '';

        if (data.status === 'success') {
            formattedResult += `<div class="success-msg">Extraction completed successfully!</div>`;
            formattedResult += `<h4>MCQs:</h4>`;

            if (data.mcqs && data.mcqs.length > 0) {
                formattedResult += `<p>Total MCQs: ${data.mcqs.length}</p>`;

                // Add pagination controls
                formattedResult += `
                <div class="mcq-pagination">
                    <button id="prevPage" disabled>Previous</button>
                    <span class="mcq-pagination-info">Page <span id="currentPage">1</span> of <span id="totalPages">1</span></span>
                    <button id="nextPage">Next</button>
                </div>
                `;

                // Container for MCQs with formatted display
                formattedResult += `<div id="mcqContainer" class="mcq-container"></div>`;

                // Initialize MCQ display with pagination
                const mcqsData = data.mcqs;
                const itemsPerPage = 5;
                const totalPages = Math.ceil(mcqsData.length / itemsPerPage);

                // Update the DOM with pagination info
                setTimeout(() => {
                    document.getElementById('totalPages').textContent = totalPages;

                    // Function to render MCQs for a specific page
                    window.renderMCQs = function(page) {
                        const startIndex = (page - 1) * itemsPerPage;
                        const endIndex = Math.min(startIndex + itemsPerPage, mcqsData.length);
                        const mcqsToShow = mcqsData.slice(startIndex, endIndex);

                        let html = "";
                        mcqsToShow.forEach(question => {
                            html += "<div class='item-wrap'>" +
                                "<pre>"+
                                (question.direction || "") +
                                (question.passage || "") +
                                ('\n\n'+(question.question_number) + ". " + question.question_text+'\n' || "");
                            html +="</pre>";
                            html +="<pre>";

                            // Handle options
                            if (question.options && question.options.length > 0) {
                                question.options.forEach(option => {
                                    html += '\n(' + option.identifier + ') ' + option.text;
                                });
                            } else {
                                // Fallback for older format
                                if(question.option1) html+= '\n(1) '+question.option1;
                                if(question.option2) html+= '\n(2) '+question.option2;
                                if(question.option3) html+= '\n(3) '+question.option3;
                                if(question.option4) html+= '\n(4) '+question.option4;
                                if(question.option5) html+= '\n(5) '+question.option5;
                            }
                            html +="</pre>";

                            // Handle question images
                            if (question.question_images && question.question_images.length > 0) {
                                html += "<p><strong>Question Images:</strong></p>";
                                question.question_images.forEach(img => {
                                    // Add BASE_URL if the image path is relative
                                    // Make sure img is a string before calling startsWith
                                    const imgStr = String(img || '');
                                    console.log('Image path:', imgStr, 'BASE_URL:', BASE_URL);
                                    const imgSrc = imgStr.startsWith('https') ? imgStr : `${BASE_URL}${imgStr}`;
                                    html += `<img src="${imgSrc}" style="max-width: 100%; display: block; margin-bottom: 10px;" />`;
                                });
                            }

                            // Handle option images
                            if (question.option_images && Object.keys(question.option_images).length > 0) {
                                Object.entries(question.option_images).forEach(([key, img]) => {
                                    html += `<p><strong>Option ${key} Image:</strong></p>`;
                                    // Add BASE_URL if the image path is relative
                                    // Make sure img is a string before calling startsWith
                                    const imgStr = String(img || '');
                                    const imgSrc = imgStr.startsWith('https') ? imgStr : `${BASE_URL}${imgStr}`;
                                    html += `<img src="${imgSrc}" style="max-width: 100%; display: block; margin-bottom: 10px;" />`;
                                });
                            }
                            html += "</div>";
                        });

                        document.getElementById('mcqContainer').innerHTML = html;
                        document.getElementById('currentPage').textContent = page;
                        document.getElementById('prevPage').disabled = page === 1;
                        document.getElementById('nextPage').disabled = page === totalPages;
                        window.currentMCQPage = page;

                        // Render math expressions using KaTeX
                        renderMathInElement(document.getElementById('mcqContainer'), {
                            delimiters: [
                                { left: "\\(", right: "\\)", display: false },
                                { left: "\\[", right: "\\]", display: true },
                                { left: "$$", right: "$$", display: true },
                                { left: "$", right: "$", display: false },
                            ],
                            ignoredTags: []
                        });
                    };

                    // Initial render
                    window.currentMCQPage = 1;
                    window.renderMCQs(1);

                    // Add event listeners for pagination
                    document.getElementById('prevPage').addEventListener('click', () => {
                        if (window.currentMCQPage > 1) {
                            window.currentMCQPage--;
                            window.renderMCQs(window.currentMCQPage);
                        }
                    });

                    document.getElementById('nextPage').addEventListener('click', () => {
                        if (window.currentMCQPage < totalPages) {
                            window.currentMCQPage++;
                            window.renderMCQs(window.currentMCQPage);
                        }
                    });
                }, 100);
            } else {
                formattedResult += `<p>No MCQs found</p>`;
            }

            formattedResult += `<h4>Answer Keys:</h4>`;
            if (data.answer_keys) {
                formattedResult += `<details>`;
                formattedResult += `<summary>View Answer Keys</summary>`;

                // Use the provided format for answer keys
                function construct_answerkey(answerkey) {
                    return `<pre>${answerkey}</pre>`;
                }

                // Check if answer_keys is a string or an object
                if (typeof data.answer_keys === 'string') {
                    formattedResult += construct_answerkey(data.answer_keys);
                } else {
                    // If it's an object, convert it to a formatted string
                    const answerKeyString = JSON.stringify(data.answer_keys, null, 2);
                    formattedResult += construct_answerkey(answerKeyString);
                }

                formattedResult += `</details>`;

                // Add a script to render math in answer keys after they're displayed
                formattedResult += `
                    document.querySelector('details:nth-of-type(1)').addEventListener('toggle', function(e) {
                        if (this.open) {
                            renderMathInElement(this, {
                                delimiters: [
                                    { left: "\\\\(", right: "\\\\)", display: false },
                                    { left: "\\\\[", right: "\\\\]", display: true },
                                    { left: "$$", right: "$$", display: true },
                                    { left: "$", right: "$", display: false },
                                ],
                                ignoredTags: []
                            });
                        }
                    });
               `;
            } else {
                formattedResult += `<p>No answer keys found</p>`;
            }

            formattedResult += `<h4>Explanations:</h4>`;
            if (data.explanations && data.explanations.length > 0) {
                formattedResult += `<p>Total Explanations: ${data.explanations.length}</p>`;
                formattedResult += `<details>`;
                formattedResult += `<summary>View Explanations</summary>`;

                // Use the provided format for explanations
                const explanationsHtml = (() => {
                    // Use the BASE_URL variable defined at the top of the script
                    let html = "";
                    data.explanations.forEach(exp => {
                        html += "<div class='item-wrap'>" +
                            "<pre>" + (exp.text || exp.explanation || JSON.stringify(exp)) + "</pre>";

                        // Check if explanation has images
                        if (exp.explanation_images && exp.explanation_images.length > 0) {
                            html += "<p><strong>Explanation Images:</strong></p>";
                            exp.explanation_images.forEach(img => {
                                // Add the base URL if the image path is relative
                                // Make sure img is a string before calling startsWith
                                const imgStr = String(img || '');
                                const imgSrc = imgStr.startsWith('https') ? imgStr : `${BASE_URL}${imgStr}`;
                                html += `<img src="${imgSrc}" style="max-width: 100%; display: block; margin-bottom: 10px;" />`;
                            });
                        }
                        html += "</div>";
                    });
                    return html;
                })();

                formattedResult += explanationsHtml;
                formattedResult += `</details>`;

                // Add a script to render math in explanations after they're displayed
                formattedResult += `
                    document.querySelector('details:nth-of-type(2)').addEventListener('toggle', function(e) {
                        if (this.open) {
                            renderMathInElement(this, {
                                delimiters: [
                                    { left: "\\\\(", right: "\\\\)", display: false },
                                    { left: "\\\\[", right: "\\\\]", display: true },
                                    { left: "$$", right: "$$", display: true },
                                    { left: "$", right: "$", display: false },
                                ],
                                ignoredTags: []
                            });
                        }
                    });
               `;
            } else {
                formattedResult += `<p>No explanations found</p>`;
            }

            formattedResult += `<h4>Pattern:</h4>`;
            if (data.pattern) {
                formattedResult += `<details>`;
                formattedResult += `<summary>View Pattern</summary>`;
                formattedResult += `<pre>${JSON.stringify(data.pattern, null, 2)}</pre>`;
                formattedResult += `</details>`;
            } else {
                formattedResult += `<p>No pattern information</p>`;
            }

            formattedResult += `<h4>Cost Information:</h4>`;
            formattedResult += `<ul>`;
            formattedResult += `<li>Total Cost: ${data.total_cost || 'N/A'}</li>`;
            formattedResult += `<li>Page Info Cost: ${data.page_info_cost || 'N/A'}</li>`;
            formattedResult += `<li>Question Cost: ${data.question_cost || 'N/A'}</li>`;
            formattedResult += `<li>Explanation Cost: ${data.explanation_cost || 'N/A'}</li>`;
            formattedResult += `<li>Answer Key Cost: ${data.answer_key_cost || 'N/A'}</li>`;
            formattedResult += `</ul>`;

            if (data.wrong_answer_keys && data.wrong_answer_keys.length > 0) {
                formattedResult += `<h4>Mismatched Question Numbers:</h4>`;
                formattedResult += `<pre>${JSON.stringify(data.wrong_answer_keys, null, 2)}</pre>`;
            }
        } else {
            formattedResult += `<div class="error-msg">Extraction failed: ${data.message || 'Unknown error'}</div>`;
            formattedResult += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
        }

        // Set the formatted result
        resultContent.innerHTML = formattedResult;
        statusItem.classList.add(data.status === 'success' ? 'success' : 'failure');
        statusItem.classList.remove('loading');

        // Render math expressions in the entire result content
        if (data.status === 'success') {
            renderMathInElement(resultContent, {
                delimiters: [
                    { left: "\\(", right: "\\)", display: false },
                    { left: "\\[", right: "\\]", display: true },
                    { left: "$$", right: "$$", display: true },
                    { left: "$", right: "$", display: false },
                ],
                ignoredTags: ['script', 'noscript', 'style', 'textarea', 'pre', 'code']
            });
        }
    } catch (error) {
        console.error('Error in form submission:', error);
        resultContent.innerHTML = `<div class="error-msg">Error: ${error.message}</div>`;
        statusItem.classList.add('failure');
        statusItem.classList.remove('loading');
        resultDiv.style.display = 'block';

        // Close SSE connection and stop polling
        closeSSE();
    }
});

// Clean up function to handle page unload
window.addEventListener('beforeunload', () => {
    // Close SSE connection and stop polling
    closeSSE();
});

// Function to check for extraction results
async function checkExtractionResults(taskId) {
    try {
        addLogEntry('Fetching extraction results...');
        const response = await fetch(`/api/task/${taskId}/status`);
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || `HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();

        if (data.status === 'completed' && data.result) {
            // Display the result
            processContent(data.result);
        } else if (data.status === 'failed') {
            addLogEntry(`Extraction failed: ${data.error || 'Unknown error'}`);
            const resultDiv = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            const statusItem = resultDiv.querySelector('.status-item');

            resultDiv.style.display = 'block';
            statusItem.classList.add('failure');
            resultContent.innerHTML = `<div class="error-msg">Extraction failed: ${data.error || 'Unknown error'}</div>`;
        } else {
            // If not completed yet, retry after a delay
            addLogEntry('Results not ready yet. Retrying in 3 seconds...');
            setTimeout(() => checkExtractionResults(taskId), 3000);
        }
    } catch (error) {
        console.error('Error checking extraction results:', error);
        addLogEntry(`Error fetching results: ${error.message}. Retrying in 3 seconds...`);
        setTimeout(() => checkExtractionResults(taskId), 3000); // Retry after 3 seconds
    }
}

// Listen for SSE completion to display results
document.addEventListener('sse-completed', (event) => {
    const taskId = event.detail.taskId;
    addLogEntry('Fetching extraction results...');
    checkExtractionResults(taskId);
});

// This function is no longer needed as we directly call checkExtractionResults
// Keeping it for backward compatibility
function notifyCompletion(taskId) {
    console.log(`Task ${taskId} completed, checking results...`);
    // We now directly call checkExtractionResults from pollTaskStatus
}

// Apply KaTeX rendering to MCQs, answer keys, and explanations
document.addEventListener('DOMContentLoaded', function() {
    // Function to render math in an element
    function renderMathInElementWithKaTeX(element) {
        if (!element) return;

        renderMathInElement(element, {
            delimiters: [
                { left: "\\(", right: "\\)", display: false },
                { left: "\\[", right: "\\]", display: true },
                { left: "$$", right: "$$", display: true },
                { left: "$", right: "$", display: false },
            ],
            ignoredTags: []
        });
    }

    // Add event listener for MCQ pagination
    document.addEventListener('click', function(e) {
        if (e.target && (e.target.id === 'prevPage' || e.target.id === 'nextPage')) {
            // Wait for the MCQs to be rendered
            setTimeout(function() {
                renderMathInElementWithKaTeX(document.getElementById('mcqContainer'));
            }, 100);
        }
    });

    // Add event listeners for details elements
    document.addEventListener('toggle', function(e) {
        if (e.target.tagName === 'DETAILS' && e.target.open) {
            renderMathInElementWithKaTeX(e.target);
        }
    }, true);

    // Apply KaTeX rendering to the entire result content when it's updated
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                renderMathInElementWithKaTeX(document.getElementById('resultContent'));
            }
        });
    });

    // Start observing the result content
    const resultContent = document.getElementById('resultContent');
    if (resultContent) {
        observer.observe(resultContent, { childList: true, subtree: true });
    }
});
</script>
{% endblock %}
