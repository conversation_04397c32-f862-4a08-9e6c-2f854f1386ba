{% extends "base.html" %}

{% block title %}Upload PDF | GPT Sir{% endblock %}

{% block content %}
<main class="upload-wrapper">
  <h2>Upload PDF</h2>

  <div id="message-section">
    {% if message %}
      <p style="color: green;">{{ message }}</p>

      {% set preview_file = message.split('Output saved to: ')[1] %}
      <p>
        <a href="/preview/{{ preview_file }}" target="_blank">➡️ Click here to preview the final HTML</a>
      </p>

      <script>
        function formatDuration(seconds) {
          const hrs = Math.floor(seconds / 3600);
          const mins = Math.floor((seconds % 3600) / 60);
          const secs = Math.floor(seconds % 60);

          let parts = [];
          if (hrs) parts.push(`${hrs} hr`);
          if (mins) parts.push(`${mins} min`);
          if (secs || (!hrs && !mins)) parts.push(`${secs} sec`);

          return parts.join(" ");
        }

        if (window.timerInterval) clearInterval(window.timerInterval);
        if (window.startTime) {
          const endTime = new Date();
          const durationInSeconds = (endTime - window.startTime) / 1000;
          const formatted = formatDuration(durationInSeconds);
          document.getElementById("timer").textContent = `⏱️ Processing Time: ${formatted}`;
        }
      </script>
    {% endif %}
  </div>

  <form id="upload-form" action="/upload" method="post" enctype="multipart/form-data">
    <input type="file" name="file" accept=".pdf" required>
    <button type="submit">Upload</button>
  </form>

  <div id="timer"></div>
  <div id="loader" class="loader" style="display: none;"></div>
</main>
{% endblock %}

{% block scripts %}
<script>
  const form = document.getElementById("upload-form");
  const loader = document.getElementById("loader");
  const timerDisplay = document.getElementById("timer");
  const messageSection = document.getElementById("message-section");

  function formatDuration(seconds) {
    const hrs = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    let parts = [];
    if (hrs) parts.push(`${hrs} hr`);
    if (mins) parts.push(`${mins} min`);
    if (secs || (!hrs && !mins)) parts.push(`${secs} sec`);

    return parts.join(" ");
  }

  form.addEventListener("submit", function () {
    loader.style.display = "block";
    timerDisplay.textContent = "";
    if (messageSection) messageSection.innerHTML = "";

    window.startTime = new Date();
    window.timerInterval = setInterval(() => {
      const now = new Date();
      const elapsedSeconds = (now - window.startTime) / 1000;
      const humanTime = formatDuration(elapsedSeconds);
      timerDisplay.textContent = `⏱️ Processing... ${humanTime}`;
    }, 500);
  });
</script>
{% endblock %}