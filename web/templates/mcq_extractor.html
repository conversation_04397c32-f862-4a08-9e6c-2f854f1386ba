{% extends "base.html" %}

{% block title %}MCQ Extractor{% endblock %}

{% block content %}
<div class="test-container">
    <div class="test-card">
        <h1>MCQ Extractor</h1>
        <p class="test-description">Enter a resource ID to extract MCQs from PDF files</p>

        <form id="extractorForm" class="login-form">
            <label for="resId">Enter Res ID</label>
            <input type="text" id="resId" name="resId" required>
            <button type="submit">Extract MCQ</button>
        </form>
        <div id="spinner" class="spinner"></div>

        <div id="logContainer" style="display: none; margin-top: 20px;">
            <h3>Progress Log:</h3>
            <div id="progressLog" class="progress-log"></div>
        </div>

        <div id="finalResultContainer" style="display: none; margin-top: 20px;">
            <h3>Final Extraction Result:</h3>
            <div id="finalResultContent" class="final-result-content"></div>
            <button id="viewFinalJsonBtn" class="btn btn-primary">View Full JSON</button>
        </div>

        <div id="chaptersContainer" style="display: none; margin-top: 20px;">
            <h3>Extraction Results:</h3>
            <table id="resultsTable" class="chapters-table">
                <thead>
                    <tr>
                        <th>Page</th>
                        <th>Column</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="resultsTableBody">
                </tbody>
            </table>
        </div>

        <style>
            .loader-container {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.7);
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                z-index: 9999;
            }

            .loader-spinner {
                border: 8px solid #f3f3f3;
                border-top: 8px solid #3498db;
                border-radius: 50%;
                width: 60px;
                height: 60px;
                animation: spin 1.2s linear infinite;
            }

            .loader-text {
                color: white;
                font-size: 18px;
                margin-top: 20px;
                text-align: center;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .final-result-content {
                max-height: 300px;
                overflow-y: auto;
                background-color: #f5f5f5;
                padding: 15px;
                border-radius: 5px;
                margin-bottom: 10px;
                white-space: pre-wrap;
                font-family: monospace;
            }

            .skipped-extractions {
                background-color: #fff3cd;
                color: #856404;
                padding: 15px;
                border-radius: 5px;
                margin-top: 15px;
                border: 1px solid #ffeeba;
            }

            .status-success {
                color: #155724;
                background-color: #d4edda;
            }

            .status-error {
                color: #721c24;
                background-color: #f8d7da;
            }

            .status-warning {
                color: #856404;
                background-color: #fff3cd;
            }
        </style>

        <!-- Modal for displaying content -->
        <div id="contentModal" class="modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.4);">
            <div class="modal-content" style="background-color: #fefefe; margin: 10% auto; padding: 20px;padding-top: 0!important; border: 1px solid #888; width: 80%; max-height: 70vh; overflow-y: auto;">
                <div style="display: flex;align-items: center;justify-content: space-between;position: sticky;top: 0;background: white;">
                    <h3 id="modalTitle">MCQ Content</h3>
                    <span class="close" style="color: #aaa;font-size: 28px; font-weight: bold; cursor: pointer;">&times;</span>
                </div>
                <pre id="modalContent" style="white-space: pre-wrap; word-break: break-word;"></pre>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Get the base URL from the current location
const BASE_URL = window.location.origin;
console.log('BASE_URL:', BASE_URL);
const form = document.getElementById('extractorForm');
const resultsContainer = document.getElementById('chaptersContainer');
const resultsTableBody = document.getElementById('resultsTableBody');
const spinner = document.getElementById('spinner');
const contentModal = document.getElementById('contentModal');
const modalContent = document.getElementById('modalContent');
const modalTitle = document.getElementById('modalTitle');
const closeModalButton = document.querySelector('.close');
const logContainer = document.getElementById('logContainer');
const progressLog = document.getElementById('progressLog');

let resId = null;
let chapter_id = null;
let book_id = null;

// Close the modal when clicking the close button
closeModalButton.addEventListener('click', function() {
    contentModal.style.display = 'none';
});

// Close the modal when clicking outside of it
window.addEventListener('click', function(event) {
    if (event.target === contentModal) {
        contentModal.style.display = 'none';
    }
});

// Function to show the modal with content
function showContentModal(title, content) {
    modalTitle.textContent = title;
    modalContent.textContent = content;
    contentModal.style.display = 'block';
}

// Function to add a log entry
function addLogEntry(message) {
    const logEntry = document.createElement('div');
    logEntry.className = 'log-entry';
    logEntry.textContent = message;
    progressLog.appendChild(logEntry);
    progressLog.scrollTop = progressLog.scrollHeight;
    // Don't automatically show the log container
}

// Function to handle form submission
form.addEventListener('submit', async function(event) {
    event.preventDefault();

    resId = document.getElementById('resId').value.trim();
    if (!resId) {
        alert('Please enter a resource ID');
        return;
    }

    // Clear previous results
    resultsTableBody.innerHTML = '';
    progressLog.innerHTML = '';
    resultsContainer.style.display = 'none';
    logContainer.style.display = 'none'; // Hide the log container
    spinner.style.display = 'block';

    // Generate a unique request ID for tracking this extraction
    const clientRequestId = Math.random().toString(36).substring(2, 15);

    // First check if the resource has already been processed
    try {
        // Check if the resource has already been processed (silently)
        const statusResponse = await fetch(`/api/extract-mcq/status/${resId}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        });

        const statusData = await statusResponse.json();

        // We don't need to show any logs about the status check

        // Show a nice loader
        const loaderContainer = document.createElement('div');
        loaderContainer.className = 'loader-container';
        loaderContainer.innerHTML = `
            <div class="loader-spinner"></div>
            <div class="loader-text">Extracting MCQs from PDF...<br>This may take several minutes.<br>Please wait.</div>
        `;
        document.body.appendChild(loaderContainer);

        // Call the API to extract MCQs - this will wait for the complete process to finish
        addLogEntry(`Sending extraction request to server (Request ID: ${clientRequestId})...`);

        const response = await fetch(`/api/extract-mcq`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Client-Request-ID': clientRequestId
            },
            body: JSON.stringify({ res_id: resId }),
        });

        // Check if the response is valid JSON
        let data;
        try {
            data = await response.json();
        } catch (jsonError) {
            // Show error in an alert instead of log
            alert(`Error parsing response: ${jsonError.message}\nServer returned an invalid response. The process may have timed out or failed.`);
            spinner.style.display = 'none';
            document.body.removeChild(loaderContainer);
            return;
        }

        if (response.ok) {
            // No need to log about the response

            spinner.style.display = 'none';

            // Remove the loader
            document.body.removeChild(loaderContainer);

            // Display the results
            if (data.status === 'success') {
                chapter_id = data.chapter_id;
                book_id = data.book_id;
                resId = data.resource_id;
                // Check if we have a result path (S3 path)
                if (data.result_path) {
                    // We don't need to show logs about fetching from S3

                    try {
                        // Fetch the final JSON from S3
                        const s3Response = await fetch(`/api/mcq-s3-content?s3_path=${encodeURIComponent(data.result_path)}`);

                        if (s3Response.ok) {
                            const s3Data = await s3Response.json();

                            // Display a summary of the final JSON
                            const finalResultContainer = document.getElementById('finalResultContainer');
                            const finalResultContent = document.getElementById('finalResultContent');
                            const viewFinalJsonBtn = document.getElementById('viewFinalJsonBtn');

                            try {
                                // Parse the JSON content
                                const jsonContent = JSON.parse(s3Data.content);

                                // Create a summary
                                const questionCount = jsonContent.questions ? jsonContent.questions.length : 0;
                                const explanationCount = jsonContent.explanations ? jsonContent.explanations.length : 0;
                                const directionCount = jsonContent.directions ? jsonContent.directions.length : 0;

                                const directions = jsonContent.directions
                                const questions = jsonContent.questions
                                const explanations = jsonContent.explanations

                                // Update the jsonContent with the mapped questions
                                jsonContent.questions = questions;
                                jsonContent.directions = directions;
                                jsonContent.explanations = explanations;
                                // Log the arrays to console for debugging
                                console.log('Questions array:', jsonContent.questions);
                                console.log('Explanations array:', jsonContent.explanations);
                                console.log('Directions array:', jsonContent.directions);

                                // Check if there were any skipped extractions
                                let skippedHtml = '';
                                if (data.skipped_count && data.skipped_count > 0) {
                                    const skippedCount = data.skipped_count;
                                    const skippedLocations = data.skipped_locations ? data.skipped_locations.join(', ') : 'Unknown locations';
                                    skippedHtml = `
                                        <div class="skipped-extractions">
                                            <p><strong>Skipped Extractions:</strong> ${skippedCount}</p>
                                            <p><strong>Skipped Locations:</strong> ${skippedLocations}</p>
                                        </div>
                                    `;

                                    // Also add to the log
                                    addLogEntry(`Warning: ${skippedCount} extractions were skipped due to 504 errors: ${skippedLocations}`);
                                }

                                finalResultContent.innerHTML = `
                                    <p><strong>Questions:</strong> ${questionCount}</p>
                                    <p><strong>Explanations:</strong> ${explanationCount}</p>
                                    <p><strong>Directions:</strong> ${directionCount}</p>
                                    ${skippedHtml}
                                `;

                                // Show the container
                                finalResultContainer.style.display = 'block';

                                // Add event listener to view full JSON with updated content
                                viewFinalJsonBtn.addEventListener('click', () => {
                                    // Use the updated jsonContent with mapped directions
                                    showContentModal('Full JSON Content', JSON.stringify(jsonContent, null, 2));
                                });
                                // No need to log success
                            } catch (jsonError) {
                                // Show error in an alert instead of log
                                alert(`Error parsing S3 JSON content: ${jsonError.message}`);
                                finalResultContent.textContent = s3Data.content;
                                finalResultContainer.style.display = 'block';
                            }
                        } else {
                            // Show error in an alert instead of log
                            alert(`Error fetching final result from S3: ${s3Response.status} ${s3Response.statusText}`);
                        }
                    } catch (s3Error) {
                        // Show error in an alert instead of log
                        alert(`Error fetching final result from S3: ${s3Error.message}`);
                    }
                }

                // Show individual results if available
                if (data.results && data.results.length > 0) {
                    displayResults(data.results);
                    // No need to log about individual files
                }
            } else if (data.status === 'error') {
                // Show error in an alert instead of log
                alert(`Error: ${data.message || 'Unknown error'}`);
            } else {
                // Show error in an alert instead of log
                alert('No results found or extraction failed');
            }
        } else {
            // Show error in an alert instead of log
            alert(`Error: ${data.message || data.detail || 'Failed to extract MCQs'}`);
            spinner.style.display = 'none';
            // Remove the loader if it exists
            if (document.querySelector('.loader-container')) {
                document.body.removeChild(document.querySelector('.loader-container'));
            }
        }
    } catch (error) {
        // Show error in an alert instead of log
        alert(`Error: ${error.message}\nAn unexpected error occurred. Please try again or contact support.`);
        spinner.style.display = 'none';
        // Remove the loader if it exists
        if (document.querySelector('.loader-container')) {
            document.body.removeChild(document.querySelector('.loader-container'));
        }
    }
});



// Function to display results
function displayResults(results) {
    resultsContainer.style.display = 'block';
    resultsTableBody.innerHTML = '';

    results.forEach(result => {
        const row = document.createElement('tr');

        const pageCell = document.createElement('td');
        pageCell.textContent = result.page_number;
        row.appendChild(pageCell);

        const colCell = document.createElement('td');
        colCell.textContent = result.col_number;
        row.appendChild(colCell);

        const statusCell = document.createElement('td');
        statusCell.textContent = result.status;

        // Set appropriate class based on status
        if (result.status === 'success') {
            statusCell.className = 'status-success';
        } else if (result.status === 'skipped') {
            statusCell.className = 'status-warning';
            // Add tooltip with reason if available
            if (result.skipped_reason === '504_gateway_timeout') {
                statusCell.title = 'Skipped due to 504 Gateway Timeout error';
                statusCell.textContent = 'Skipped (504)';
            }
        } else {
            statusCell.className = 'status-error';
        }

        row.appendChild(statusCell);

        const actionsCell = document.createElement('td');

        // View button
        const viewButton = document.createElement('button');
        viewButton.textContent = 'View';
        viewButton.className = 'action-button view-button';
        viewButton.addEventListener('click', async () => {
            // Show loading message
            showContentModal(`MCQ Content - Page ${result.page_number}, Column ${result.col_number}`, 'Loading content...');

            if (result.file_path) {
                try {
                    // Fetch the full content from the file
                    const response = await fetch(`/api/mcq-content?file_path=${encodeURIComponent(result.file_path)}`);
                    if (response.ok) {
                        const data = await response.json();
                        let displayContent = data.content;
                        try {
                            // If it's a JSON string, parse it and format it
                            const parsedContent = JSON.parse(data.content);
                            displayContent = JSON.stringify(parsedContent, null, 2);
                        } catch (e) {
                            // If it's not valid JSON, just display as is
                            console.log('Content is not valid JSON, displaying as is');
                        }
                        showContentModal(`MCQ Content - Page ${result.page_number}, Column ${result.col_number}`, displayContent);
                    } else {
                        showContentModal(`MCQ Content - Page ${result.page_number}, Column ${result.col_number}`,
                            `Error loading content: ${response.status} ${response.statusText}`);
                    }
                } catch (error) {
                    showContentModal(`MCQ Content - Page ${result.page_number}, Column ${result.col_number}`,
                        `Error loading content: ${error.message}`);
                }
            } else {
                // If no file path, show the preview
                showContentModal(`MCQ Content - Page ${result.page_number}, Column ${result.col_number}`,
                    result.content_preview || 'No content available');
            }
        });
        actionsCell.appendChild(viewButton);

        row.appendChild(actionsCell);
        resultsTableBody.appendChild(row);
    });

}
async function addDirections(directions, serverUrl){
    // First transform text to passage
    directions.map(direction=>{
        direction.passage = direction.text;
        delete direction.text;
    })

    console.log('Original directions array:', directions);

    // Process each direction with API call
    const updatedDirections = [];

    for (const direction of directions) {
        try {
            const requestJson = {
                directions:{
                    passage: direction.passage
                }
            };

            console.log('Sending direction to API:', JSON.stringify(requestJson));

            // Call the API for this direction
            const response = await fetch(`${serverUrl}/excel/processDirections`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestJson)
            });

            if (response.ok) {
                const responseData = await response.json();
                console.log('API response:', responseData);

                // Add the directionId to the direction object
                direction.directionId = responseData.directionId;
                updatedDirections.push(direction);
            } else {
                console.error('Error calling processDirections API:', response.status, response.statusText);
                // Still include the direction even if API call failed
                updatedDirections.push(direction);
            }
        } catch (error) {
            console.error('Exception calling processDirections API:', error);
            // Still include the direction even if API call failed
            updatedDirections.push(direction);
        }
    }

    // Log the updated directions array
    console.log('Updated directions array with directionIds:', updatedDirections);

    return updatedDirections;
}

function mapDirectionsToQuestions(directions, questions) {
    console.log('Mapping directions to questions...');

    // First initialize all questions with directionsId = null
    questions.forEach(question => {
        question.directionsId = null;
    });

    // Map directions to questions
    directions.forEach(direction => {
        const directionId = direction.directionId;
        const appliesTo = direction.appliesTo;

        if (!directionId || !appliesTo) {
            console.log(`Skipping direction with missing id or appliesTo:`, direction);
            return;
        }

        // Skip special cases
        if (appliesTo.toLowerCase() === 'next' ||
            appliesTo.toLowerCase() === 'all' ||
            appliesTo.toLowerCase() === 'none') {
            console.log(`Skipping direction with special appliesTo value '${appliesTo}'`);
            return;
        }

        console.log(`Processing direction ${directionId} with appliesTo: ${appliesTo}`);

        // Handle different formats of appliesTo field
        if (appliesTo.includes('-')) {  // Range format like "1-6"
            try {
                const [start, end] = appliesTo.split('-');
                const startNum = parseInt(start.trim());
                const endNum = parseInt(end.trim());

                // Find questions in this range and add directionsId
                questions.forEach(question => {
                    const questionNum = question.question_number;
                    if (questionNum) {
                        try {
                            const qNum = parseInt(questionNum);
                            if (startNum <= qNum && qNum <= endNum) {
                                question.directionsId = directionId;
                                console.log(`Added directionsId ${directionId} to question ${questionNum}`);
                            }
                        } catch (e) {
                            console.warn(`Could not convert question_number ${questionNum} to int`);
                        }
                    }
                });
            } catch (e) {
                console.warn(`Error processing appliesTo range ${appliesTo}: ${e}`);
            }
        } else if (appliesTo.includes(',')) {  // List format like "1,2,3"
            try {
                const questionNums = appliesTo.split(',').map(num => parseInt(num.trim()));

                // Find questions in this list and add directionsId
                questions.forEach(question => {
                    const questionNum = question.question_number;
                    if (questionNum) {
                        try {
                            const qNum = parseInt(questionNum);
                            if (questionNums.includes(qNum)) {
                                question.directionsId = directionId;
                                console.log(`Added directionsId ${directionId} to question ${questionNum}`);
                            }
                        } catch (e) {
                            console.warn(`Could not convert question_number ${questionNum} to int`);
                        }
                    }
                });
            } catch (e) {
                console.warn(`Error processing appliesTo list ${appliesTo}: ${e}`);
            }
        } else {  // Single number format like "17"
            try {
                const appliesToNum = parseInt(appliesTo.trim());

                // Find the question with this number and add directionsId
                questions.forEach(question => {
                    const questionNum = question.question_number;
                    if (questionNum) {
                        try {
                            const qNum = parseInt(questionNum);
                            if (qNum === appliesToNum) {
                                question.directionsId = directionId;
                                console.log(`Added directionsId ${directionId} to question ${questionNum}`);
                            }
                        } catch (e) {
                            console.warn(`Could not convert question_number ${questionNum} to int`);
                        }
                    }
                });
            } catch (e) {
                console.warn(`Error processing appliesTo single number ${appliesTo}: ${e}`);
            }
        }
    });
}

function mapExplanationsToQuestions(explanations, questions) {
    console.log('Mapping explanations to questions...');

    // First initialize all questions with explanation = null
    questions.forEach(question => {
        question.explanation = null;
    });

    // Map explanations to questions
    explanations.forEach(explanation => {
        const explanationNumber = explanation.explanation_number;

        if (!explanationNumber) {
            console.log(`Skipping explanation with missing explanation_number:`, explanation);
            return;
        }

        // Skip special cases
        if (explanationNumber === '__prev__' || explanationNumber === '__next__') {
            console.log(`Skipping explanation with special explanation_number value '${explanationNumber}'`);
            return;
        }

        console.log(`Processing explanation for question ${explanationNumber}`);

        // Find the matching question and add the explanation
        const matchingQuestion = questions.find(question => {
            try {
                return question.question_number === explanationNumber;
            } catch (e) {
                console.warn(`Error comparing question_number ${question.question_number} with explanation_number ${explanationNumber}`);
                return false;
            }
        });

        if (matchingQuestion) {
            matchingQuestion.explanation = explanation.explanation;
            console.log(`Added explanation to question ${explanationNumber}`);
        } else {
            console.warn(`No matching question found for explanation ${explanationNumber}`);
        }
    });

    console.log('Completed mapping explanations to questions');
}

/**
 * Transforms the options array in questions to individual op1, op2, op3, op4, op5 properties
 * @param {Array} questions - Array of question objects with options array to be transformed
 */
function transformOptionsFormat(questions) {
    console.log('Transforming options format in questions...');

    questions.forEach(question => {
        if (Array.isArray(question.options)) {
            // Extract option values from the array
            question.options.forEach((option, index) => {
                // Create op1, op2, op3, etc. properties
                question[`op${index + 1}`] = option;
            });

            // Remove the original options array
            delete question.options;

            console.log(`Transformed options for question ${question.question_number}`);
        } else {
            console.log(`Question ${question.question_number} doesn't have options array`);
        }
    });

    console.log('Completed transforming options format');
}

async function processMCQsWithProgress(questions, serverUrl) {
    const totalQuestions = questions.length;
    let processedCount = 0;
    const progressBar = document.getElementById('mcq-progress-bar');
    const progressText = document.getElementById('mcq-progress-text');

    // Process questions sequentially to maintain order
    for (const question of questions) {
        try {
            // Call addMCQ for this question
            await addMCQ(question, serverUrl);

            // Update progress
            processedCount++;
            const progressPercentage = (processedCount / totalQuestions) * 100;
            progressBar.style.width = `${progressPercentage}%`;
            progressText.textContent = `${processedCount}/${totalQuestions} MCQs processed`;

            // Log progress
            console.log(`Processed MCQ ${processedCount}/${totalQuestions}`);
        } catch (error) {
            console.error(`Error processing MCQ ${processedCount + 1}/${totalQuestions}:`, error);
            // Continue with next question even if this one fails
        }
    }

    // Update final status
    progressText.textContent = `Completed: ${processedCount}/${totalQuestions} MCQs processed`;
    console.log('All MCQs processed');
}

async function addMCQ(mcq, serverUrl) {
    try {
        const requestJson = {
            chapterId: chapter_id,
            resId: resId,
            Question: mcq.text,
            option1: mcq.op1,
            option2: mcq.op2,
            option3: mcq.op3,
            option4: mcq.op4,
            answerDescription: mcq.explanation,
            correctAnswer: "",
            question_images: mcq.question_images,
            option_images: mcq.option_images,
            explanation_images: mcq.explanation_images,
            directionId: mcq.directionId
        }
        console.log(`Sending MCQ ${mcq.question_number} to API`);

        // Call the API for this MCQ
        const response = await fetch(`${serverUrl}/excel/processMCQ`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestJson)
        });

        if (response.ok) {
            const responseData = await response.json();
            console.log(`MCQ ${mcq.question_number} processed successfully:`, responseData);
            return responseData;
        } else {
            console.error(`Error processing MCQ ${mcq.question_number}:`, response.status, response.statusText);
            throw new Error(`API error: ${response.status} ${response.statusText}`);
        }
    } catch (error) {
        console.error(`Exception calling processMCQ API for MCQ ${mcq.question_number}:`, error);
        throw error; // Re-throw to handle in the calling function
    }
}

</script>
{% endblock %}
