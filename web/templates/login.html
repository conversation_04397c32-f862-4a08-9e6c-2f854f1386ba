{% extends "base.html" %}

{% block title %}Login | GPT Sir{% endblock %}

{% block content %}
<div class="login-page-container">
  <div class="login-container">
  <div class="login-card">
    <h1>Welcome to GPT Sir</h1>
    <h2>Login</h2>

    {% if error %}
      <p class="error-msg">{{ error }}</p>
    {% endif %}

    {% if message %}
      <p class="success-msg">{{ message }}</p>
    {% endif %}

    <form action="/" method="post" class="login-form">
        <label for="username">Email or Mobile</label>
        <input type="text" id="username" name="username" required>

        <label for="password">Password</label>
        <div class="password-container">
          <input type="password" id="password" name="password" required>
          <span class="password-toggle" onclick="togglePasswordVisibility()">
            <svg xmlns="http://www.w3.org/2000/svg" class="eye-icon eye-open" viewBox="0 0 24 24" width="24" height="24"><path fill="none" d="M0 0h24v24H0z"/><path d="M12 3c5.392 0 9.878 3.88 10.819 9-.94 5.12-5.427 9-10.819 9-5.392 0-9.878-3.88-10.819-9C2.121 6.88 6.608 3 12 3zm0 16c3.691 0 6.915-2.534 7.736-6C18.915 9.534 15.691 7 12 7c-3.691 0-6.915 2.534-7.736 6 .82 3.466 4.045 6 7.736 6zm0-10c2.21 0 4 1.791 4 4s-1.79 4-4 4-4-1.79-4-4 1.79-4 4-4zm0 6c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z"/></svg>
            <svg xmlns="http://www.w3.org/2000/svg" class="eye-icon eye-closed" viewBox="0 0 24 24" width="24" height="24"><path fill="none" d="M0 0h24v24H0z"/><path d="M17.882 19.297A10.949 10.949 0 0 1 12 21c-5.392 0-9.878-3.88-10.819-9a10.982 10.982 0 0 1 3.34-6.066L1.392 2.808l1.415-1.415 19.799 19.8-1.415 1.414-3.31-3.31zM5.935 7.35A8.965 8.965 0 0 0 3.223 12a9.005 9.005 0 0 0 13.201 5.838l-2.028-2.028A4.5 4.5 0 0 1 8.19 9.604L5.935 7.35zm6.979 6.978l-3.242-3.242a2.5 2.5 0 0 0 3.241 3.241zm7.893 2.264l-1.431-1.43A8.935 8.935 0 0 0 20.777 12 9.005 9.005 0 0 0 9.552 5.338L7.974 3.76C9.221 3.27 10.58 3 12 3c5.392 0 9.878 3.88 10.819 9a10.947 10.947 0 0 1-2.012 4.592zm-9.084-9.084a4.5 4.5 0 0 1 4.769 4.769l-4.77-4.769z"/></svg>
          </span>
        </div>

        <button type="submit">Login</button>
      </form>
    </div>
    </div>
  </div>
{% endblock %}

{% block scripts %}
<script>
  function togglePasswordVisibility() {
    const passwordInput = document.getElementById('password');
    const eyeOpen = document.querySelector('.eye-open');
    const eyeClosed = document.querySelector('.eye-closed');

    if (passwordInput.type === 'password') {
      passwordInput.type = 'text';
      eyeOpen.style.display = 'none';
      eyeClosed.style.display = 'block';
    } else {
      passwordInput.type = 'password';
      eyeOpen.style.display = 'block';
      eyeClosed.style.display = 'none';
    }
  }

  // Initialize the toggle state
  document.addEventListener('DOMContentLoaded', function() {
    const eyeClosed = document.querySelector('.eye-closed');
    eyeClosed.style.display = 'none';
  });
</script>
{% endblock %}