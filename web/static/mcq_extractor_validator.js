// Get the base URL from the current location
const BASE_URL = window.location.origin;
const form = document.getElementById('extractorValidatorForm');
const resultsContainer = document.getElementById('resultsContainer');
const validationSummary = document.getElementById('validationSummary');
const validationDetails = document.getElementById('validationDetails');
const spinner = document.getElementById('spinner');
const progressLog = document.getElementById('progressLog');

// Add event listener to the form
form.addEventListener('submit', async function(event) {
    event.preventDefault();

    // Get the resource ID
    const resId = document.getElementById('resId').value.trim();

    if (!resId) {
        alert('Please enter a Resource ID');
        return;
    }

    // Clear previous results
    validationSummary.innerHTML = '';
    validationDetails.innerHTML = '';
    resultsContainer.style.display = 'none';
    progressLog.innerHTML = '';
    progressLog.style.display = 'block';
    spinner.style.display = 'block';

    // Add initial log entry
    addLogEntry(`Starting MCQ extraction and validation for resource ID ${resId}...`);

    try {
        // Call the API to extract and validate MCQs
        addLogEntry(`Calling API to extract and validate MCQs...`);

        const response = await fetch(`/api/mcq-extractor-and-validator`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ res_id: resId }),
        });

        const data = await response.json();

        // Hide spinner
        spinner.style.display = 'none';

        if (data.status === 'error') {
            addLogEntry(`Error: ${data.message}`, 'error');
            alert(data.message);
            return;
        }

        // Add success log entry
        addLogEntry(`MCQ extraction and validation completed successfully!`, 'success');

        // Display results
        displayResults(data);
    } catch (error) {
        spinner.style.display = 'none';
        addLogEntry(`Error: ${error.message}`, 'error');
        alert(`Error: ${error.message}`);
    }
});

// Function to add a log entry
function addLogEntry(message, type = 'info') {
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry log-${type}`;

    const timestamp = new Date().toLocaleTimeString();
    logEntry.innerHTML = `<span class="log-time">[${timestamp}]</span> ${message}`;

    progressLog.appendChild(logEntry);
    progressLog.scrollTop = progressLog.scrollHeight; // Auto-scroll to bottom
}

// Function to display validation results
function displayResults(data) {
    resultsContainer.style.display = 'block';

    // Display summary
    const totalMCQs = data.mcqs.length;
    const invalidMCQs = data.mcqs.filter(mcq => !mcq.is_valid_answer_key).length;

    // Check if there were any skipped extractions
    let skippedHtml = '';
    if (data.skipped_extractions && data.skipped_extractions.count > 0) {
        const skippedCount = data.skipped_extractions.count;
        const skippedLocations = data.skipped_extractions.locations.join(', ');
        skippedHtml = `
            <div class="skipped-extractions">
                <p><strong>Skipped Extractions:</strong> ${skippedCount}</p>
                <p><strong>Skipped Locations:</strong> ${skippedLocations}</p>
            </div>
        `;

        // Also add to the log
        addLogEntry(`Warning: ${skippedCount} extractions were skipped due to 504 errors: ${skippedLocations}`, 'warning');
    }

    validationSummary.innerHTML = `
        <p><strong>Total MCQs:</strong> ${totalMCQs}</p>
        <p><strong>Valid Answer Keys:</strong> ${totalMCQs - invalidMCQs}</p>
        <p><strong>Invalid Answer Keys:</strong> ${invalidMCQs}</p>
        ${skippedHtml}
    `;

    // Display details for each MCQ
    data.mcqs.forEach(mcq => {
        const mcqDiv = document.createElement('div');
        mcqDiv.className = 'mcq-item';

        // Determine status class
        const statusClass = mcq.is_valid_answer_key ? 'status-valid' : 'status-invalid';

        // Create content
        mcqDiv.innerHTML = `
            <div class="mcq-header ${statusClass}">
                <span class="mcq-number">Question ${mcq.id}</span>
                <span class="mcq-status">${mcq.is_valid_answer_key ? 'Valid' : 'Invalid'}</span>
            </div>
            <div class="mcq-content">
                <p><strong>Question:</strong> ${mcq.question}</p>
                <p><strong>Options:</strong></p>
                <ul>
                    <li>1. ${mcq.option1 || 'N/A'}</li>
                    <li>2. ${mcq.option2 || 'N/A'}</li>
                    <li>3. ${mcq.option3 || 'N/A'}</li>
                    <li>4. ${mcq.option4 || 'N/A'}</li>
                    <li>5. ${mcq.option5 || 'N/A'}</li>
                </ul>
                <p><strong>Original Answer:</strong> ${mcq.original_answer || mcq.correct_answer}</p>
                ${!mcq.is_valid_answer_key ? `<p><strong>Corrected Answer:</strong> ${mcq.corrected_answer}</p>` : ''}
                ${mcq.answer_description ? `<p><strong>Answer Description:</strong> ${mcq.answer_description}</p>` : ''}
            </div>
        `;

        validationDetails.appendChild(mcqDiv);
    });
}
