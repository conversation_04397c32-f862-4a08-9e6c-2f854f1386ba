const hostname = window.location.hostname; // e.g., qa.gptsir.ai
const subdomain = hostname.split('.')[0];   // e.g., qa or publish

const currBaseUrl = `https://${subdomain}.wonderslate.com/funlearn/downloadEpubImage?source=`;
let quizResId = "-1"
let chapterId = "-1"
function processContent(data){
    addLogEntry(`Displaying results`);
    const questions = data.mcqs
    const answer_keys = data.answer_keys;
    const explanations = data.explanations
    const pattern = data.pattern

    questions.sort((a, b) => {
        return a.question_number - b.question_number;
    });

    console.log(questions);
    questionsUIHandler(
        questions,
        explanations,
        answer_keys,
        {
            total_cost: data.total_cost,
            page_info_cost: data.page_info_cost,
            question_cost: data.question_cost,
            explanation_cost: data.explanation_cost,
            answer_key_cost: data.answer_key_cost
        }
    )
    const mcqsWithExplanations = mapExplanationsToMcqs(questions, explanations);

    const answers = parseAnswers(answer_keys);

    const updatedQuestions = mapAnswersToQuestions(mcqsWithExplanations, answers)
    storeMCQs(updatedQuestions)
}

function mapAnswersToQuestions(questionsArray, answerKeysArray) {
  // 1. Create a lookup map for efficient answer retrieval.
  //    Key: question number (as number), Value: answer key string (e.g., '(1)')
  const answerMap = new Map();
  answerKeysArray.forEach(answer => {
    // Convert the string key from answerKeysArray to a number for matching
    const questionNum = parseInt(answer.question_number, 10);
    if (!isNaN(questionNum)) { // Ensure the key is a valid number
        answerMap.set(questionNum, answer.correct_answer);
    } else {
        console.warn(`Invalid key found in answerKeysArray: ${answer.question_number}`);
    }
  });

  // 2. Map over the questions array to create a new array with answers added.
  const questionsWithAnswers = questionsArray.map(question => {
    // Find the corresponding answer in the map using the question_number
    const correctAnswer = answerMap.get(question.question_number);

    // Create a new question object (to avoid modifying the original)
    const newQuestion = { ...question }; // Shallow copy using spread syntax

    // Add the 'correctAnswer' property ONLY if an answer was found
    if (correctAnswer !== undefined) {
      newQuestion.correctAnswer = correctAnswer.replaceAll('(','').replaceAll(')','');
    }
    // If correctAnswer is undefined (not found in map), the property is simply omitted.

    return newQuestion;
  });

  return questionsWithAnswers;
}

function questionsUIHandler(questions, explanations, answerkey, totalCostData) {
    const quizContainer = document.getElementById('quiz-container');

    quizContainer.innerHTML += construct_questions(questions);
    quizContainer.innerHTML += "<hr />";
    quizContainer.innerHTML += "<h3>Explanations</h3>";
    quizContainer.innerHTML += construct_explanations(explanations);
    quizContainer.innerHTML += "<hr />";
    quizContainer.innerHTML += "<h3>Answer Keys</h3>";
    quizContainer.innerHTML += construct_answerkey(answerkey);
    quizContainer.innerHTML += "<hr />";
    quizContainer.innerHTML += "<h3>Cost Analysis</h3>";
    quizContainer.innerHTML += construct_cost_data(totalCostData);

    renderMathInElement(quizContainer, {
        delimiters: [
            { left: "\\(", right: "\\)", display: false },
            { left: "\\[", right: "\\]", display: true },
            { left: "$$", right: "$$", display: true },
            { left: "$", right: "$", display: false },
        ],
        ignoredTags: []
    });

    quizContainer.style.display = "block";
     addLogEntry(`Process completed successfully ✅`);
}

function construct_questions(questions) {
    let html = "";
    questions.forEach(question => {
        html += "<div class='item-wrap'>" +
            "<pre>"+
            (question.direction || "") +
            (question.passage || "") +
            ('\n\n'+(question.question_number) + ". " +question.question_text+'\n' || "");
        html +="</pre>";
        html +="<pre>";
            if(question.option1) html+= '\n(1) '+question.option1
            if(question.option2) html+= '\n(2) '+question.option2
            if(question.option3) html+= '\n(3) '+question.option3
            if(question.option4) html+= '\n(4) '+question.option4
            if(question.option5) html+= '\n(5) '+question.option5
        html +="</pre>";

        if (question.question_images.length > 0) {
            html += "<p><strong>Question Images:</strong></p>";
            question.question_images.forEach(img => {
                html += `<img src="${currBaseUrl}${img}" style="max-width: 100%; display: block; margin-bottom: 10px;" />`;
            });
        }

        if (question.option_images && Object.keys(question.option_images).length > 0) {
            Object.entries(question.option_images).forEach(([key, img]) => {
                html += `<p><strong>Option ${key} Image:</strong></p>`;
                html += `<img src="${currBaseUrl}${img}" style="max-width: 100%; display: block; margin-bottom: 10px;" />`;
            });
        }
        html += "</div>";
    });
    return html;
}

function construct_explanations(explanations) {
    let html = "";
    explanations.forEach(exp => {
        html += "<div class='item-wrap'>" +
            "<pre>" + exp.text + "</pre>";
        if (exp.explanation_images.length > 0) {
            html += "<p><strong>Explanation Images:</strong></p>";
            exp.explanation_images.forEach(img => {
                html += `<img src="${currBaseUrl}${img}" style="max-width: 100%; display: block; margin-bottom: 10px;" />`;
            });
        }
        html += "</div>";
    });
    return html;
}

function construct_answerkey(answerkey) {
    return `<pre>${answerkey}</pre>`;
}

function construct_ans_key_comp(ansKeyComp) {
    let html = "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
    html += "<tr><th>Question</th><th>Correct Answer</th><th>Explanation Answer</th><th>Valid</th></tr>";

    ansKeyComp.forEach(item => {
        html += `<tr>
            <td>${item.question}</td>
            <td>${item.correct_ans_key}</td>
            <td>${item.explanation_key}</td>
            <td>${item.valid ? "✅" : "❌"}</td>
        </tr>`;
    });

    html += "</table>";
    return html;
}

function construct_cost_data(costData) {
    let html = "<ul>";
    html += `<li><strong>Total Cost:</strong> ₹${costData.total_cost.toFixed(2)}</li>`;
    html += `<li><strong>Page Info Cost:</strong> ₹${costData.page_info_cost.toFixed(2)}</li>`;
    html += `<li><strong>Question Cost:</strong> ₹${costData.question_cost.toFixed(2)}</li>`;
    html += `<li><strong>Explanation Cost:</strong> ₹${costData.explanation_cost.toFixed(2)}</li>`;
    html += `<li><strong>Answer Key Cost:</strong> ₹${costData.answer_key_cost.toFixed(2)}</li>`;
    html += "</ul>";
    return html;
}

function parseAnswers(inputString) {
    const regex = /\b(\d+)\.?\s+\((\d+)\)/g;
    // Array to store the results
    const result = [];
    // Set to track question numbers we've already processed
    const processedQuestions = new Set();

    // Find all matches
    let match;
    while ((match = regex.exec(inputString)) !== null) {
        const questionNumber = match[1];

        // Add to our result array
        result.push({
            question_number: questionNumber,
            correct_answer: `(${match[2]})`
        });

        // Mark this question as processed
        processedQuestions.add(questionNumber);
    }

    return result;
}

function mapExplanationsToMcqs(processedMcqsArray, explanationsArray) {
    // Input validation
    if (!Array.isArray(processedMcqsArray) || !Array.isArray(explanationsArray)) {
        console.error("Invalid input: Both arguments must be arrays.");
        return processedMcqsArray || [];
    }

    // --- Step 1: Create an efficient lookup map for explanations ---
    const explanationMap = new Map();
    const explanationNumberRegex = /^\s*(\d+)\.\s*/;

    explanationsArray.forEach((exp, index) => {
        if (!exp || typeof exp.text !== 'string') {
            console.warn(`Skipping explanation at index ${index}: Invalid object structure or missing 'text'.`);
            return;
        }

        const match = exp.text.match(explanationNumberRegex);
        let correctNumber;

        if (match && match[1]) {
            correctNumber = parseInt(match[1], 10);
            if (isNaN(correctNumber)) {
                console.warn(`Could not parse number from explanation text starting with "${match[0].trim()}" at index ${index}. Skipping.`);
                return;
            }
        } else {
            console.warn(`Could not find pattern "Number." at the start of explanation text for index ${index}. Text starts with: "${exp.text.substring(0, 10)}...". Skipping.`);
            return;
        }

        if (explanationMap.has(correctNumber)) {
            console.warn(`Duplicate explanation found for question number ${correctNumber}. Overwriting with explanation from index ${index}.`);
        }
        explanationMap.set(correctNumber, {
            text: exp.text,
            images: exp.explanation_images || [] // Default to empty array if missing
        });
    });

    // --- Step 2: Iterate through MCQs and add explanations ---
    processedMcqsArray.forEach(mcq => {
        if (typeof mcq.question_number !== 'number' || isNaN(mcq.question_number)) {
            console.warn(`Skipping MCQ mapping: Invalid or missing question_number.`, mcq);
            mcq.answerDescription = "";
            mcq.explanation_images = [];
            return;
        }

        if (explanationMap.has(mcq.question_number)) {
            // Explanation found - map it
            const explanationData = explanationMap.get(mcq.question_number);
            mcq.answerDescription = explanationData.text;
            mcq.explanation_images = explanationData.images;
        } else {
            // No explanation found - add default empty values
            console.warn(`No explanation found for question number ${mcq.question_number}. Setting defaults.`);
            mcq.answerDescription = "";
            mcq.explanation_images = [];
        }
    });
    return processedMcqsArray;
}

async function storeMCQs(mcqs){
    document.body.classList.remove('loaded');
    const mcq_store_api = "/excel/processMCQ"
    let resId = "-1"
    for(let i =0; i<mcqs.length; i++){
        try {
            const mcqRequestObj = {
                chapterId: chapterId,
                resId: resId,
                Question: mcqs[i].question_text,
                option1: mcqs[i].option1,
                option2: mcqs[i].option2,
                option3: mcqs[i].option3,
                option4: mcqs[i].option4,
                answerDescription: mcqs[i].answerDescription,
                correctAnswer: mcqs[i].correctAnswer,
                question_images: mcqs[i].question_images,
                option_images: mcqs[i].option_images,
                explanation_images: mcqs[i].explanation_images,
                isValidAnswerKey: mcqs[i].answer_key_valid
            }
            if(mcqRequestObj.Question == "" || mcqRequestObj.Question== null || mcqRequestObj.Question ==null){
                mcqRequestObj.Question = ""+mcqs[i].question_number
            }
            if(mcqs[i].direction){
                mcqRequestObj.directions = mcqs[i].direction
            }
            if(mcqs[i].passage){
                mcqRequestObj.passage = mcqs[i].passage
            }
            console.log(mcqRequestObj)
            // const response = await fetch(mcq_store_api,{
            //     method:"POST",
            //     body:JSON.stringify(mcqRequestObj)
            // })
            // const result = await response.json()
            // if(result.status == "OK"){
            //     resId = result.additionalInformation.resId
            //     quizResId = result.additionalInformation.resId
            // }else{
            //     return false;
            // }
        }catch (e){
            console.log(e)
        }
    }
}

function extractAnswerKeys(dataObject) {
  const extractedData = [];
  // Regex to find patterns like "1. (1)", "12. (3)", etc.
  // (\d+) captures the number before the dot (key)
  // (\(\d+\)) captures the number in parentheses (value)
  const regex = /(\d+)\.\s+(\(\d+\))/g; // g flag for global search

  // Iterate through the properties of the input object
  for (const key in dataObject) {
    // Use hasOwnProperty check for safety, especially if the object's prototype could be modified
    if (Object.hasOwnProperty.call(dataObject, key)) {
      // Skip the first key/value (assuming it's always key "1" and holds metadata like "ANSWERS")
      if (key === "1") {
        continue; // Move to the next key
      }

      const line = dataObject[key];

      // Ensure the value is a string before attempting to match
      if (typeof line === 'string') {
        let match;
        // Use exec in a loop to find all matches within the string value
        while ((match = regex.exec(line)) !== null) {
          extractedData.push({
            key: match[1],   // The first captured group (e.g., "1", "6", "11")
            value: match[2]  // The second captured group (e.g., "(1)", "(3)", "(4)")
          });
        }
         /* Alternative using matchAll (more modern):
         const matches = line.matchAll(regex);
         for (const match of matches) {
             extractedData.push({
                key: match[1],
                value: match[2]
            });
         }
         */
      } else {
        // Optional: Handle cases where a value isn't a string if needed
        console.warn(`Skipping non-string value for key "${key}":`, line);
      }
    }
  }

  return extractedData;
}

function createAnswerTableHtml(answerArray) {
  // Start the table structure and add the header row
  let htmlString = `
  <table border="1"> <!-- Added border for basic visibility -->
    <thead>
      <tr>
        <th>Question Number</th>
        <th>Answer Key</th>
      </tr>
    </thead>
    <tbody>
  `;

  // Loop through the array and add a table row (<tr>) for each item
  answerArray.forEach(item => {
    htmlString += `
      <tr>
        <td>${item.key}</td>
        <td>${item.value}</td>
      </tr>
    `;
  });

  // Close the table body and table tags
  htmlString += `
    </tbody>
  </table>
  `;

  return htmlString.trim(); // Trim any leading/trailing whitespace
}