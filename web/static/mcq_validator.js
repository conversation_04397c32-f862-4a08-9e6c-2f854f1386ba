// Get the base URL from the current location
const BASE_URL = window.location.origin;
const form = document.getElementById('validatorForm');
const resultsContainer = document.getElementById('resultsContainer');
const validationSummary = document.getElementById('validationSummary');
const validationDetails = document.getElementById('validationDetails');
const spinner = document.getElementById('spinner');

form.addEventListener('submit', async function(event) {
    event.preventDefault();
    
    // Get the resource ID
    const resId = document.getElementById('resId').value.trim();
    
    if (!resId) {
        alert('Please enter a Resource ID');
        return;
    }
    
    // Clear previous results
    validationSummary.innerHTML = '';
    validationDetails.innerHTML = '';
    resultsContainer.style.display = 'none';
    spinner.style.display = 'block';
    
    try {
        // Call the API to validate MCQs
        const response = await fetch(`/api/validate-mcq/${resId}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        });
        
        const data = await response.json();
        
        // Hide spinner
        spinner.style.display = 'none';
        
        if (data.status === 'error') {
            alert(data.message);
            return;
        }
        
        // Display results
        displayResults(data);
    } catch (error) {
        spinner.style.display = 'none';
        alert(`Error: ${error.message}`);
    }
});

function displayResults(data) {
    resultsContainer.style.display = 'block';
    
    // Display summary
    const totalMCQs = data.mcqs.length;
    const invalidMCQs = data.mcqs.filter(mcq => !mcq.is_valid_answer_key).length;
    
    validationSummary.innerHTML = `
        <p><strong>Total MCQs:</strong> ${totalMCQs}</p>
        <p><strong>Valid Answer Keys:</strong> ${totalMCQs - invalidMCQs}</p>
        <p><strong>Invalid Answer Keys:</strong> ${invalidMCQs}</p>
    `;
    
    // Display details for each MCQ
    data.mcqs.forEach(mcq => {
        const mcqDiv = document.createElement('div');
        mcqDiv.className = 'mcq-item';
        
        // Determine status class
        const statusClass = mcq.is_valid_answer_key ? 'status-valid' : 'status-invalid';
        
        // Create content
        mcqDiv.innerHTML = `
            <div class="mcq-header ${statusClass}">
                <span class="mcq-number">Question ${mcq.id}</span>
                <span class="mcq-status">${mcq.is_valid_answer_key ? 'Valid' : 'Invalid'}</span>
            </div>
            <div class="mcq-content">
                <p><strong>Question:</strong> ${mcq.question}</p>
                <p><strong>Options:</strong></p>
                <ul>
                    <li>1. ${mcq.option1 || 'N/A'}</li>
                    <li>2. ${mcq.option2 || 'N/A'}</li>
                    <li>3. ${mcq.option3 || 'N/A'}</li>
                    <li>4. ${mcq.option4 || 'N/A'}</li>
                    <li>5. ${mcq.option5 || 'N/A'}</li>
                </ul>
                <p><strong>Original Answer:</strong> ${mcq.original_answer || mcq.correct_answer}</p>
                ${!mcq.is_valid_answer_key ? `<p><strong>Corrected Answer:</strong> ${mcq.corrected_answer}</p>` : ''}
                ${mcq.answer_description ? `<p><strong>Answer Description:</strong> ${mcq.answer_description}</p>` : ''}
            </div>
        `;
        
        validationDetails.appendChild(mcqDiv);
    });
}
