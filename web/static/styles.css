@import url('https://fonts.googleapis.com/css2?family=Alata&display=swap');

:root {
  --gunmetal: #333745;
  --cerise: #e63462;
  --bittersweet: #fe5f55;
  --tea-green: #c7efcf;
  --beige: #eef5db;
}

body {
  font-family: 'Alata', sans-serif;
  background-color: var(--beige);
  margin: 0;
  padding: 0;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  color: var(--gunmetal);
}

.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 1rem;
}

.login-card {
  background-color: #fff;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 4px 25px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
  text-align: left;
  box-sizing: border-box;
}

.login-card h1 {
  text-align: center;
  margin-bottom: 1.5rem;
  color: var(--gunmetal);
}

.login-form {
  display: flex;
  flex-direction: column;
}

.login-form label {
  margin-bottom: 0.5rem;
  font-weight: bold;
}

.login-form input {
  padding: 0.6rem;
  margin-bottom: 1.2rem;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 1rem;
}

.login-form button {
  background-color: var(--cerise);
  color: white;
  padding: 0.6rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.login-form button:hover {
  background-color: var(--bittersweet);
}

.error-msg {
  color: var(--cerise);
  text-align: center;
  margin-bottom: 1rem;
}

.success-msg {
  color: green;
  text-align: center;
  margin-bottom: 1rem;
}

.home-container {
  text-align: center;
  padding: 2rem;
  max-width: 600px;
  margin: auto;
}

.home-container h1 {
  color: var(--gunmetal);
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.home-container p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  color: #555;
}

.project-links {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.project-card {
  text-decoration: none;
  padding: 1rem 1.5rem;
  background-color: var(--cerise);
  color: white;
  font-size: 1rem;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  width: 100%;
  max-width: 320px;
}

.project-card:hover {
  background-color: var(--bittersweet);
  transform: translateY(-2px);
}

/* Header Styles */
.site-header {
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: 100%;
  padding: 0.8rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.logo-link {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  text-decoration: none;
  transition: opacity 0.3s ease;
}

.logo-link:hover {
  opacity: 0.8;
}

.logo {
  height: 40px;
  width: auto;
  border-radius: 5px;
}

.site-title {
  font-size: 1.5rem;
  margin: 0;
  color: var(--gunmetal);
}

.nav-container {
  display: flex;
  align-items: center;
}

.logout-btn {
  background-color: var(--cerise);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  text-decoration: none;
  font-size: 0.9rem;
  transition: background-color 0.3s ease;
}

.logout-btn:hover {
  background-color: var(--bittersweet);
}

/* Content Container */
.content-container {
  flex: 1;
  width: 100%;
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 1.5rem;
  box-sizing: border-box;
}

/* Footer Styles */
.site-footer {
  background-color: white;
  padding: 1.5rem 0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  width: 100%;
  margin-top: auto;
}

.footer-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
  text-align: center;
  gap: 0.8rem;
}

.footer-container p {
  margin: 0;
  color: var(--gunmetal);
}

.footer-powered {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.ws-logo {
  height: 25px;
  width: auto;
}

/* Card Styles */
.card {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.card-title {
  font-size: 1.3rem;
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--gunmetal);
}

.card-content {
  margin-bottom: 1rem;
}

.card-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--cerise);
}

/* Project Cards Grid */
.project-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

/* No Projects Message */
.no-projects-message {
  grid-column: 1 / -1;
  background-color: #f9f9f9;
  border-radius: 10px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.no-projects-message p {
  color: var(--gunmetal);
  font-size: 1.1rem;
  margin: 0;
}

/* Test Page Styles */
.test-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.test-card {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  width: 100%;
  max-width: 800px;
}

.test-card h1 {
  color: var(--cerise);
  text-align: center;
  margin-top: 0;
  margin-bottom: 0.5rem;
}

.test-description {
  text-align: center;
  font-size: 1.2rem;
  color: var(--gunmetal);
  margin-bottom: 2rem;
}

.status-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.status-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: #f9f9f9;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.status-item.success {
  background-color: rgba(199, 239, 207, 0.3);
  border-left: 4px solid var(--tea-green);
}

.status-item.failure {
  background-color: rgba(254, 95, 85, 0.1);
  border-left: 4px solid var(--bittersweet);
}

.status-item.loading {
  background-color: rgba(238, 245, 219, 0.5);
  border-left: 4px solid var(--beige);
}

.status-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
}

.status-text {
  font-size: 1.1rem;
  font-weight: 500;
}

.test-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.test-button {
  padding: 0.8rem 1.5rem;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 150px;
}

.test-button.primary {
  background-color: var(--cerise);
  color: white;
  border: none;
}

.test-button.primary:hover {
  background-color: var(--bittersweet);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.test-button.secondary {
  background-color: white;
  color: var(--gunmetal);
  border: 2px solid var(--gunmetal);
}

.test-button.secondary:hover {
  background-color: var(--gunmetal);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments for test page */
@media (max-width: 768px) {
  .test-container {
    padding: 1rem;
  }

  .test-card {
    padding: 1.5rem;
  }

  .test-actions {
    flex-direction: column;
  }

  .test-button {
    width: 100%;
  }
}

.project-item {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-decoration: none;
  color: var(--gunmetal);
  display: flex;
  flex-direction: column;
}

.project-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.project-item.disabled {
  opacity: 0.7;
  cursor: not-allowed;
  position: relative;
}

.project-item.disabled::after {
  content: '\1F512'; /* Lock emoji */
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 1.2rem;
}

.project-item-content {
  padding: 1.5rem;
}

.project-item-title {
  font-size: 1.2rem;
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: var(--gunmetal);
}

.project-item-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--cerise);
  text-align: center;
}

/* Upload Page Styles */
.upload-wrapper {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.upload-wrapper h2 {
  margin-top: 0;
  color: var(--gunmetal);
  text-align: center;
  margin-bottom: 1.5rem;
}

#upload-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

#upload-form input[type="file"] {
  padding: 0.8rem;
  border: 2px dashed var(--cerise);
  border-radius: 8px;
  background-color: rgba(230, 52, 98, 0.05);
}

#upload-form button {
  background-color: var(--cerise);
  color: white;
  padding: 0.8rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

#upload-form button:hover {
  background-color: var(--bittersweet);
}

/* Video Module Styles */

/* Form sections */
.form-section {
  margin-bottom: 2.5rem;
  position: relative;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--cerise);
  display: flex;
  align-items: center;
}

.section-icon {
  margin-right: 0.5rem;
  opacity: 0.8;
}

/* Upload container */
.upload-container {
  position: relative;
  border: 2px dashed var(--cerise);
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  background-color: rgba(230, 52, 98, 0.05);
  cursor: pointer;
}

.upload-container:hover {
  background-color: rgba(230, 52, 98, 0.1);
}

.upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  width: 100%;
  height: 100%;
  color: var(--cerise);
}

.upload-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
  opacity: 0.8;
}

.upload-input {
  position: absolute;
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  overflow: hidden;
  z-index: -1;
}

/* Content textarea */
.content-textarea {
  min-height: 150px;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 1rem;
  font-family: monospace;
  transition: all 0.3s ease;
}

.content-textarea:focus {
  border-color: var(--cerise);
  box-shadow: 0 0 0 3px rgba(230, 52, 98, 0.2);
}

/* Sample links */
.sample-links {
  margin-top: 0.75rem;
  text-align: right;
  font-size: 0.9rem;
}

.sample-link {
  color: var(--cerise);
  text-decoration: none;
  transition: all 0.3s ease;
}

.sample-link:hover {
  color: var(--bittersweet);
  text-decoration: underline;
}

/* Form controls */
.form-control {
  border-radius: 8px;
  padding: 0.75rem 1rem;
  border: 1px solid #ddd;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: var(--cerise);
  box-shadow: 0 0 0 3px rgba(230, 52, 98, 0.2);
}

.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--gunmetal);
}

/* Color pickers */
.color-pickers {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-top: 1rem;
}

.color-picker-item {
  flex: 1;
  min-width: 150px;
}

.color-picker-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.color-picker {
  width: 3rem;
  height: 3rem;
  padding: 0;
  border: none;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.color-text {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-family: monospace;
  text-transform: uppercase;
}

/* Format options */
.format-options {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  justify-content: center;
}

.format-option {
  position: relative;
  width: 150px;
}

.format-checkbox {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.format-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  border-radius: 8px;
  border: 2px solid transparent;
  background-color: #f9f9f9;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.format-checkbox:checked + .format-label {
  border-color: var(--cerise);
  background-color: rgba(230, 52, 98, 0.05);
}

.format-preview {
  margin-bottom: 0.75rem;
  background-color: #ddd;
  border-radius: 4px;
}

.ratio-16-9 {
  width: 80px;
  height: 45px;
}

.ratio-9-16 {
  width: 45px;
  height: 80px;
}

.ratio-1-1 {
  width: 60px;
  height: 60px;
}

.format-label span {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.format-label small {
  color: var(--gunmetal);
  font-size: 0.8rem;
}

/* Generate button */
.generate-btn {
  background-color: var(--cerise);
  color: white;
  border: none;
  padding: 1rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.generate-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  background-color: var(--bittersweet);
}

.generate-btn:active {
  transform: translateY(1px);
}

/* Results card */
.results-card {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.results-header {
  background-color: var(--tea-green);
  color: var(--gunmetal);
  padding: 1.25rem 1.5rem;
}

.results-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.results-body {
  padding: 1.5rem;
}

/* Video result cards */
.video-card {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.video-card:last-child {
  margin-bottom: 0;
}

.video-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.video-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--cerise);
}

.video-info {
  color: var(--gunmetal);
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.video-preview {
  width: 100%;
  border-radius: 8px;
  margin-bottom: 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.download-btn {
  background-color: var(--cerise);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  align-self: flex-start;
}

.download-btn:hover {
  background-color: var(--bittersweet);
  transform: translateY(-2px);
}

/* Error alert */
.error-alert {
  background-color: rgba(254, 95, 85, 0.1);
  color: var(--bittersweet);
  padding: 1rem 1.5rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

/* Additional styles for drag and drop */
.upload-container.highlight {
  border-color: var(--tea-green);
  background-color: rgba(199, 239, 207, 0.1);
}

.upload-container.file-selected {
  border-color: var(--tea-green);
  background-color: rgba(199, 239, 207, 0.1);
}

.upload-container.file-selected .upload-icon {
  color: var(--tea-green);
}

/* Loading state */
.form-section.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Responsive adjustments for video module */
@media (max-width: 768px) {
  .color-pickers {
    flex-direction: column;
    gap: 1rem;
  }

  .format-options {
    gap: 1rem;
  }

  .format-option {
    width: 120px;
  }

  .generate-btn {
    width: 100%;
    padding: 0.75rem 1.5rem;
  }
}

.loader {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid var(--cerise);
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin: 1rem auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Styles */
@media (max-width: 768px) {
  .header-container, .footer-container {
    padding: 0 1rem;
  }

  .content-container {
    padding: 0 1rem;
    margin: 1.5rem auto;
  }

  .project-grid {
    grid-template-columns: 1fr;
  }

  .upload-wrapper {
    padding: 1.5rem;
  }

  .site-title {
    font-size: 1.2rem;
  }

  .logo {
    height: 30px;
  }
}

/* Login Page Specific Styles */
.login-page-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 200px); /* Account for header and footer */
  padding: 1rem;
}

.login-card {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  width: 100%;
  max-width: 400px;
}

.login-card h1 {
  color: var(--gunmetal);
  text-align: center;
  margin-top: 0;
  margin-bottom: 0.5rem;
}

.login-card h2 {
  color: var(--cerise);
  text-align: center;
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

/* Password visibility toggle */
.password-container {
  position: relative;
  width: 100%;
}

.password-container input[type="password"],
.password-container input[type="text"] {
  width: 100%;
  padding-right: 40px; /* Make room for the icon */
  box-sizing: border-box;
}

.password-toggle {
  position: absolute;
  inset-block: 0;
  right: 10px;
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  pointer-events: auto;
  line-height: 0;
}

.eye-icon {
  width: 18px;
  height: 18px;
  fill: var(--gunmetal);
  opacity: 0.6;
  transition: opacity 0.2s ease;
  display: block;
}

.password-toggle:hover .eye-icon {
  opacity: 1;
}
