from fastapi import Request, HTTPException, Depends
from fastapi.responses import RedirectResponse
from typing import List, Optional

# Authentication dependency
def require_login(request: Request):
    if not request.session.get("user"):
        return RedirectResponse(url="/", status_code=303)
    return None

# Role-based access control dependency
def require_role(roles: List[str]):
    def role_checker(request: Request):
        user_roles = request.session.get("roles", [])
        if not any(role in user_roles for role in roles):
            raise HTTPException(status_code=403, detail="Forbidden: Insufficient permissions")
        return None
    return Depends(role_checker)

def getuser(request: Request):
    user = request.session.get("user")
    if user:
        return user["username"]
    return None
