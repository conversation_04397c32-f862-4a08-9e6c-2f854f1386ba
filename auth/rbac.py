from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>earer
from starlette.requests import Request
from sqlalchemy.orm import Session
from db_config.db import get_session, AUTH_SCHEMA
from db_config.db_models import User, Role, UserRole
from typing import List, Optional
import logging

logger = logging.getLogger(__name__)

# Use the get_session function from db.py
get_db = get_session

def get_user_roles(db: Session, user_id: int) -> List[str]:
    """Get user roles from database"""
    user_roles = db.query(UserRole).filter(UserRole.user_id == user_id).all()
    roles = []

    for user_role in user_roles:
        role = db.query(Role).filter(Role.id == user_role.role_id).first()
        if role:
            roles.append(role.authority)

    return roles

def require_roles(required_roles: List[str]):
    """
    Dependency for requiring specific roles to access a route
    """
    def role_checker(request: Request, db: Session = Depends(get_db)):
        # Get user from session
        user_data = request.session.get("user")
        if not user_data:
            return None  # Let the require_login dependency handle this

        # Get user from database
        user = db.query(User).filter(User.id == user_data["id"]).first()
        if not user:
            return None  # Let the require_login dependency handle this

        # Get user roles
        user_roles = get_user_roles(db, user.id)

        # Check if user has any of the required roles
        if not any(role in user_roles for role in required_roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to access this resource"
            )

        # Add roles to session for convenience
        request.session["roles"] = user_roles

        return None

    return role_checker
