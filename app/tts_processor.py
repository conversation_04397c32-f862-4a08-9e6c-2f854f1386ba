from services.tts_service import get_indian_standard_voices, text_to_speech
from services.response_service import handle_tts_data


def process_tts(content, language):
    voices = get_indian_standard_voices(language)
    text = content
    voice_name = voices[0]["name"]
    encoding = "OGG_OPUS"
    language_code = "-".join(voice_name.split("-")[:2])

    print(voices)
    print(voices[0])
    print(voice_name)
    modified_content = handle_tts_data(text)
    audio_content, timepoints = text_to_speech(modified_content, voice_name, language_code, encoding)
    return audio_content, timepoints


